import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAuthorIdGenreIdToContentStatisticsCache1752987654321
  implements MigrationInterface
{
  name = 'AddAuthorIdGenreIdToContentStatisticsCache1752987654321';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_statistics_cache\` ADD \`authorId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_statistics_cache\` ADD \`genreId\` int NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_statistics_cache\` DROP COLUMN \`genreId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_statistics_cache\` DROP COLUMN \`authorId\``,
    );
  }
}
