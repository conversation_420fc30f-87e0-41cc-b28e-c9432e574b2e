import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { AuthService } from './auth.service';
import { JwtStrategy } from './strategy/jwt.strategy';
import { UserRepository } from '../../database/repositories/user.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../../database/entities/user.entity';
import { AuthController } from './auth.controller';
import { UserModule } from '../user/user.module';
import { ContentModule } from '../content/content.module';
import { SessionEntity } from '../../database/entities/session.entity';
import { SessionRepository } from '../../database/repositories/session.repository';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';
@Module({
  imports: [
    PassportModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET,
      signOptions: { expiresIn: '1h' },
    }),
    TypeOrmModule.forFeature([UserEntity, SessionEntity]),
    UserModule,
    ContentModule,
    AnalyticsQueueModule,
  ],
  providers: [AuthService, JwtStrategy, UserRepository, SessionRepository],
  controllers: [AuthController],
  exports: [AuthService],
})
export class AuthModule {}
