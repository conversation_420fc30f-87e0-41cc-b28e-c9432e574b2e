import { Column, Entity, OneToMany } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { ContentEntity } from './content.entity';
import { DisplayEntity } from './display.entity';

@Entity('genre')
export class GenreEntity extends DefaultEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @OneToMany(() => ContentEntity, (content) => content.genre)
  contents: ContentEntity[];

  @OneToMany(() => DisplayEntity, (display) => display.genre)
  displays: DisplayEntity[];
}
