import { HttpService } from '@nestjs/axios';
import { Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { EAiDirectionApi } from 'src/common/ai-webtoon.enum';

@Injectable()
export class NotificationGoogleService {
  private readonly logger = new Logger(NotificationGoogleService.name);

  constructor(private readonly httpService: HttpService) {}

  async sendGoogleChatNotification(
    directionApi: EAiDirectionApi,
    title: string,
    payload: any,
    isSuccess: boolean,
    errorDescription?: string,
  ): Promise<void> {
    const webhookUrl =
      directionApi === EAiDirectionApi.SENT
        ? process.env.GOOGLE_CHAT_SENT_TO_AI_WEBHOOK_URL
        : process.env.GOOGLE_CHAT_RECEIVED_FROM_AI_WEBHOOK_URL;
    if (!webhookUrl) return;

    const formattedPayload = JSON.stringify(payload, null, 2);
    const color = isSuccess ? '#357a38' : '#ab003c';
    const subtitle = isSuccess ? 'AI Request Success' : 'AI Request Error';

    const widgets = [
      {
        textParagraph: {
          text: `<font color="${color}"><b>${title}</b></font>`,
        },
      },
    ];

    if (!isSuccess && errorDescription) {
      widgets.push({
        textParagraph: {
          text: `<font color="#f73378">${errorDescription}</font>`,
        },
      });
    }

    const message = {
      cardsV2: [
        {
          cardId: 'ai-notification-card',
          card: {
            header: {
              title: title,
              subtitle: subtitle,
              imageUrl:
                'https://developers.google.com/chat/images/quickstart-app-avatar.png',
              imageType: 'CIRCLE',
            },
            sections: [
              {
                widgets: widgets,
              },
              {
                collapsible: true,
                widgets: [
                  {
                    textParagraph: {
                      text: `<font color="#666666"><pre>${formattedPayload}</pre></font>`,
                    },
                  },
                ],
              },
            ],
          },
        },
      ],
    };

    try {
      await firstValueFrom(this.httpService.post(webhookUrl, message));
    } catch (chatError) {
      this.logger.error(
        'Failed to send notification to Google Chat:',
        chatError,
      );
    }
  }

  async sendLogNotificationToGoogleChatDataWorkingWithAI(data: {
    directionApi: EAiDirectionApi;
    apiName?: string;
    apiUrl?: string;
    event: string;
    data: any;
    isSuccess: boolean;
    errorDescription?: string;
  }): Promise<void> {
    await this.sendGoogleChatNotification(
      data.directionApi,
      `Event: ${data.event}${
        data.apiName ? `, API: ${data.apiName}` : ''
      }${data.apiUrl ? `, Api Url: ${data.apiUrl}` : ''}`,
      data.data,
      data.isSuccess,
      data.errorDescription,
    );
  }
}
