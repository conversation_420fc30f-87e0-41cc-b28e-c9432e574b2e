import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';
import { PaginationDto } from 'src/modules/notification/dtos/pagination.dto';
import { AiWebtoonSyntheticDataService } from './ai-webtoon-synthetic-data.service';
import { AddAiWebtoonSyntheticDataImagesDto } from './dto/add-images.dto';
import { CreateAiWebtoonSyntheticDataDto } from './dto/create.dto';
import { DeleteAiWebtoonSyntheticDataImageDto } from './dto/delete-image.dto';
import { AiWebtoonSyntheticGenerateImagesDto } from './dto/generate-images.dto';
import { AiWebtoonSyntheticReceiveGenerateImageDto } from './dto/receive-generate-image.dto';

@ApiTags('ai-webtoon-synthetic-data')
@Controller('ai-webtoon-synthetic-data')
export class AiWebtoonSyntheticDataController {
  constructor(
    private readonly aiWebtoonSyntheticDataService: AiWebtoonSyntheticDataService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/generate-images/:id')
  generateImages(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: AiWebtoonSyntheticGenerateImagesDto,
  ) {
    return this.aiWebtoonSyntheticDataService.generateImages(id, body);
  }

  @Post('/receive-generate-image')
  receiveGenerateImage(
    @Body() body: AiWebtoonSyntheticReceiveGenerateImageDto,
  ) {
    return this.aiWebtoonSyntheticDataService.receiveGenerateImage(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post()
  create(@Body() body: CreateAiWebtoonSyntheticDataDto) {
    return this.aiWebtoonSyntheticDataService.create(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('add-images/:id')
  addImages(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: AddAiWebtoonSyntheticDataImagesDto,
  ) {
    return this.aiWebtoonSyntheticDataService.addImages(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get()
  list(@Query() dto: PaginationDto) {
    return this.aiWebtoonSyntheticDataService.list(dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/:id')
  detail(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonSyntheticDataService.detail(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/image/:id')
  deleteImage(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: DeleteAiWebtoonSyntheticDataImageDto,
  ) {
    return this.aiWebtoonSyntheticDataService.deleteImage(id, body);
  }
}
