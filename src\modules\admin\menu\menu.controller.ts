import {
  Controller,
  Get,
  Post,
  UseGuards,
  Body,
  Param,
  Put,
  Req,
} from '@nestjs/common';
import { MenuService } from './menu.service';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
import { RoleGuard } from '../auth-admin/guards/role.guard';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from 'src/common/role.enum';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CreateMenuDto } from './dtos/create-menu.dto';
import { UpdateMenuDto } from './dtos/update-menu.dto';
@ApiTags('Admin Menu Management')
@Controller('admin/menu')
@UseGuards(JwtAdminAuthGuard, RoleGuard)
@Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
@ApiBearerAuth()
export class MenuController {
  constructor(private menuService: MenuService) {}

  @Get()
  @ApiOperation({ summary: 'Get all menus' })
  @ApiResponse({ status: 200, description: 'Return all menus' })
  async getMenu() {
    return await this.menuService.getMenu();
  }

  @Post()
  @ApiOperation({ summary: 'Create a new menu' })
  @ApiResponse({ status: 201, description: 'Menu created successfully' })
  async createMenu(@Body() createMenuDto: CreateMenuDto) {
    return await this.menuService.createMenu(createMenuDto);
  }

  @Get('my-menu')
  @ApiOperation({ summary: 'Get all menus by admin' })
  @ApiResponse({ status: 200, description: 'Return all menus by admin' })
  async getMenuByAdmin(@Req() req: any) {
    // console.log(req.user);
    return await this.menuService.getMenuByAdminId(req.user.id);
  }

  @Get('parent')
  @ApiOperation({ summary: 'Get all parent menus' })
  @ApiResponse({ status: 200, description: 'Return all parent menus' })
  async getParentMenu() {
    return await this.menuService.getParentMenu();
  }

  @Get('children/:parentId')
  @ApiOperation({ summary: 'Get all children menus' })
  @ApiResponse({ status: 200, description: 'Return all children menus' })
  async getChildrenMenu(@Param('parentId') parentId: number) {
    return await this.menuService.getChildrenMenu(parentId);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update a menu' })
  @ApiResponse({ status: 200, description: 'Menu updated successfully' })
  async updateMenu(
    @Param('id') id: number,
    @Body() updateMenuDto: UpdateMenuDto,
  ) {
    return await this.menuService.updateMenu(id, updateMenuDto);
  }
}
