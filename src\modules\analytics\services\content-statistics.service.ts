import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ContentEntity } from '../../../database/entities/content.entity';
import { ContentStatisticsCacheEntity } from '../../../database/entities/content-statistics-cache.entity';
import { ContentAnalyticsEntity } from '../../../database/entities/content-analytics.entity';
import {
  ContentRankingStatsEntity,
  PeriodType,
} from '../../../database/entities/content-ranking-stats.entity';
import {
  DateRangeAnalyticsResponseDto,
  DailyAnalyticsDto,
} from '../dtos/content-statistics.dto';

export interface ContentStatisticsData {
  contentId: number;
  title: string;
  releaseDate: Date;
  type: string;
  episodes: number;
  author: string;
  authorId: number;
  series: string;
  genre: string;
  genreId: number;
  freeEpisodeRead: number;
  paidEpisodeRead: number;
  totalEpisodeRead: number;
}

@Injectable()
export class ContentStatisticsService {
  private readonly logger = new Logger(ContentStatisticsService.name);

  constructor(
    @InjectRepository(ContentEntity)
    private contentRepository: Repository<ContentEntity>,
    @InjectRepository(ContentStatisticsCacheEntity)
    private cacheRepository: Repository<ContentStatisticsCacheEntity>,
    @InjectRepository(ContentAnalyticsEntity)
    private contentAnalyticsRepository: Repository<ContentAnalyticsEntity>,
    @InjectRepository(ContentRankingStatsEntity)
    private rankingStatsRepository: Repository<ContentRankingStatsEntity>,
  ) {}

  /**
   * Main job function to refresh content statistics cache
   * Called every 20 seconds by BullMQ job
   */
  async refreshContentStatisticsCache(): Promise<void> {
    const startTime = Date.now();
    this.logger.log('Starting content statistics cache refresh...');

    try {
      // Get all content statistics using optimized query
      const statistics = await this.calculateAllContentStatistics();

      // Batch update cache
      await this.updateCacheInBatches(statistics);

      const duration = Date.now() - startTime;
      this.logger.log(
        `Content statistics cache refreshed successfully. Duration: ${duration}ms, Updated: ${statistics.length} contents`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to refresh content statistics cache: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Calculate statistics for all content using optimized single query
   */
  private async calculateAllContentStatistics(): Promise<
    ContentStatisticsData[]
  > {
    const query = `
      SELECT 
        c.id as contentId,
        c.title,
        c.createdAt as releaseDate,
        CASE WHEN c.isAdult = 1 THEN 'adult' ELSE 'general' END as type,
        COALESCE(episodeStats.episodeCount, 0) as episodes,
        COALESCE(a.name, 'Unknown') as author,
        COALESCE(a.id, NULL) as authorId,
        CASE 
          WHEN s.key = 'completed' THEN 'completed' 
          ELSE 'ongoing' 
        END as series,
        COALESCE(g.name, 'Unknown') as genre,
        COALESCE(g.id, NULL) as genreId,
        COALESCE(episodeStats.freeEpisodeRead, 0) as freeEpisodeRead,
        COALESCE(episodeStats.paidEpisodeRead, 0) as paidEpisodeRead,
        COALESCE(episodeStats.totalEpisodeRead, 0) as totalEpisodeRead
      FROM content c
      LEFT JOIN author a ON c.authorId = a.id
      LEFT JOIN genre g ON c.genreId = g.id
      LEFT JOIN content_setting cs ON c.id = cs.contentId AND cs.type = 'date'
      LEFT JOIN settings s ON cs.settingId = s.id AND s.group = 'content_date'
      LEFT JOIN (
        SELECT 
          e.contentId,
          COUNT(*) as episodeCount,
          SUM(CASE WHEN e.paymentType = 'free' THEN e.viewCount ELSE 0 END) as freeEpisodeRead,
          SUM(CASE WHEN e.paymentType = 'paid' THEN e.viewCount ELSE 0 END) as paidEpisodeRead,
          SUM(e.viewCount) as totalEpisodeRead
        FROM episode e
        GROUP BY e.contentId
      ) episodeStats ON c.id = episodeStats.contentId
      WHERE c.deletedAt IS NULL
    `;

    const results = await this.contentRepository.query(query);
    return results;
  }

  /**
   * Update cache in batches for better performance using MySQL UPSERT
   */
  private async updateCacheInBatches(
    statistics: ContentStatisticsData[],
  ): Promise<void> {
    const batchSize = 100;
    const now = new Date();

    for (let i = 0; i < statistics.length; i += batchSize) {
      const batch = statistics.slice(i, i + batchSize);

      // Remove duplicate contentId entries (keep last)
      const uniqueMap = new Map<number, ContentStatisticsData>();
      for (const stat of batch) {
        uniqueMap.set(stat.contentId, stat); // sẽ tự động ghi đè nếu trùng
      }
      const uniqueBatch = Array.from(uniqueMap.values());

      // Build VALUES for batch insert
      const values = uniqueBatch
        .map(
          (stat) => `(
      ${stat.contentId},
      ${this.escapeString(stat.title)},
      '${this.formatDate(stat.releaseDate)}',
      ${this.escapeString(stat.type)},
      ${stat.episodes},
      ${this.escapeString(stat.author)},
      ${stat.authorId || 'NULL'},
      ${this.escapeString(stat.series)},
      ${this.escapeString(stat.genre)},
      ${stat.genreId || 'NULL'},
      ${stat.freeEpisodeRead},
      ${stat.paidEpisodeRead},
      ${stat.totalEpisodeRead},
      '${this.formatDate(now)}',
      NOW(),
      NOW()
    )`,
        )
        .join(',');

      const upsertQuery = `
      INSERT INTO content_statistics_cache 
      (contentId, title, releaseDate, type, episodes, author, authorId, series, genre, genreId,
       freeEpisodeRead, paidEpisodeRead, totalEpisodeRead, lastUpdated, createdAt, updatedAt)
      VALUES ${values}
      ON DUPLICATE KEY UPDATE
        title = VALUES(title),
        releaseDate = VALUES(releaseDate),
        type = VALUES(type),
        episodes = VALUES(episodes),
        author = VALUES(author),
        authorId = VALUES(authorId),
        series = VALUES(series),
        genre = VALUES(genre),
        genreId = VALUES(genreId),
        freeEpisodeRead = VALUES(freeEpisodeRead),
        paidEpisodeRead = VALUES(paidEpisodeRead),
        totalEpisodeRead = VALUES(totalEpisodeRead),
        lastUpdated = VALUES(lastUpdated),
        updatedAt = NOW()
    `;

      await this.cacheRepository.query(upsertQuery);
    }

    // Clean up old entries (content that no longer exists)
    const activeContentIds = Array.from(
      new Set(statistics.map((s) => s.contentId)),
    );
    if (activeContentIds.length > 0) {
      await this.cacheRepository
        .createQueryBuilder()
        .delete()
        .where('contentId NOT IN (:...ids)', { ids: activeContentIds })
        .execute();
    }
  }

  private escapeString(str: string): string {
    if (str == null) return 'NULL';
    return `'${str.replace(/\\/g, '\\\\').replace(/'/g, "\\'")}'`;
  }

  private formatDate(date: Date): string {
    return date.toISOString().slice(0, 19).replace('T', ' ');
  }

  /**
   * Get cached content statistics for API endpoints
   * Enhanced with date range support using hybrid approach
   */
  async getCachedContentStatistics(filters?: {
    type?: string;
    author?: string;
    genre?: string;
    series?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    data: ContentStatisticsCacheEntity[];
    total: number;
    lastUpdated: Date;
  }> {
    // If date range is provided, use ranking stats table
    if (filters?.startDate || filters?.endDate) {
      return this.getDateRangeRankingStats(filters);
    }

    // Otherwise use existing cache logic for all-time stats
    const query = this.cacheRepository.createQueryBuilder('cache');

    // Apply filters
    if (filters?.type) {
      query.andWhere('cache.type = :type', { type: filters.type });
    }
    if (filters?.author) {
      query.andWhere('cache.authorId = :author', {
        author: filters.author,
      });
    }
    if (filters?.genre) {
      query.andWhere('cache.genreId = :genre', {
        genre: filters.genre,
      });
    }
    if (filters?.series) {
      query.andWhere('cache.series = :series', { series: filters.series });
    }

    // Get total count
    const total = await query.getCount();

    // Apply pagination
    if (filters?.limit) {
      query.limit(filters.limit);
    }
    if (filters?.offset) {
      query.offset(filters.offset);
    }

    // Order by total reads descending
    query.orderBy('cache.totalEpisodeRead', 'DESC');

    const data = await query.getMany();

    // Get last updated time
    const lastUpdated = data.length > 0 ? data[0].lastUpdated : new Date();

    return { data, total, lastUpdated };
  }

  /**
   * Get content statistics using date range from content_ranking_stats table
   * This provides optimized data from the dedicated ranking stats table
   */
  private async getDateRangeRankingStats(filters: {
    type?: string;
    author?: string;
    genre?: string;
    series?: string;
    startDate?: string;
    endDate?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    data: ContentStatisticsCacheEntity[];
    total: number;
    lastUpdated: Date;
  }> {
    const query = this.rankingStatsRepository.createQueryBuilder('ranking');

    // Filter by date range and daily period type
    query.where('ranking.periodType = :periodType', {
      periodType: PeriodType.DAILY,
    });

    if (filters.startDate) {
      query.andWhere('ranking.date >= :startDate', {
        startDate: filters.startDate,
      });
    }
    if (filters.endDate) {
      query.andWhere('ranking.date <= :endDate', {
        endDate: filters.endDate,
      });
    }

    // Apply filters
    if (filters?.type) {
      query.andWhere('ranking.type = :type', { type: filters.type });
    }
    if (filters?.author) {
      query.andWhere('ranking.authorId = :author', {
        author: filters.author,
      });
    }
    if (filters?.genre) {
      query.andWhere('ranking.genreId = :genre', {
        genre: filters.genre,
      });
    }
    if (filters?.series) {
      query.andWhere('ranking.series = :series', { series: filters.series });
    }

    // Group by content and sum statistics
    query
      .select([
        'ranking.contentId as contentId',
        'MAX(ranking.title) as title',
        'MAX(ranking.author) as author',
        'MAX(ranking.authorId) as authorId',
        'MAX(ranking.genre) as genre',
        'MAX(ranking.genreId) as genreId',
        'MAX(ranking.type) as type',
        'MAX(ranking.series) as series',
        'MAX(ranking.episodes) as episodes',
        'SUM(ranking.freeEpisodeRead) as freeEpisodeRead',
        'SUM(ranking.paidEpisodeRead) as paidEpisodeRead',
        'SUM(ranking.totalEpisodeRead) as totalEpisodeRead',
        'SUM(ranking.totalViews) as totalViews',
        'SUM(ranking.uniqueViewers) as uniqueViewers',
        'SUM(ranking.totalReadTime) as totalReadTime',
        'MAX(ranking.updatedAt) as lastUpdated',
      ])
      .groupBy('ranking.contentId')
      .orderBy('SUM(ranking.totalEpisodeRead)', 'DESC');

    // Get total count
    const countQuery = this.rankingStatsRepository
      .createQueryBuilder('ranking')
      .where('ranking.periodType = :periodType', {
        periodType: PeriodType.DAILY,
      });

    if (filters.startDate) {
      countQuery.andWhere('ranking.date >= :startDate', {
        startDate: filters.startDate,
      });
    }
    if (filters.endDate) {
      countQuery.andWhere('ranking.date <= :endDate', {
        endDate: filters.endDate,
      });
    }

    if (filters?.type) {
      countQuery.andWhere('ranking.type = :type', { type: filters.type });
    }
    if (filters?.author) {
      countQuery.andWhere('ranking.authorId = :author', {
        author: filters.author,
      });
    }
    if (filters?.genre) {
      countQuery.andWhere('ranking.genreId = :genre', {
        genre: filters.genre,
      });
    }
    if (filters?.series) {
      countQuery.andWhere('ranking.series = :series', {
        series: filters.series,
      });
    }

    const total = await countQuery
      .select('COUNT(DISTINCT ranking.contentId) as count')
      .getRawOne()
      .then((result) => parseInt(result.count) || 0);

    // Apply pagination
    if (filters?.limit) {
      query.limit(filters.limit);
    }
    if (filters?.offset) {
      query.offset(filters.offset);
    }

    const rawResults = await query.getRawMany();

    // Map results to match ContentStatisticsCacheEntity format
    const data = rawResults.map((row) => ({
      contentId: row.contentId,
      title: row.title,
      author: row.author,
      authorId: row.authorId,
      genre: row.genre,
      genreId: row.genreId,
      type: row.type,
      series: row.series,
      episodes: row.episodes,
      freeEpisodeRead: parseInt(row.freeEpisodeRead) || 0,
      paidEpisodeRead: parseInt(row.paidEpisodeRead) || 0,
      totalEpisodeRead: parseInt(row.totalEpisodeRead) || 0,
      lastUpdated: row.lastUpdated,
    })) as ContentStatisticsCacheEntity[];

    const lastUpdated = data.length > 0 ? data[0].lastUpdated : new Date();

    return { data, total, lastUpdated };
  }

  /**
   * Get cache freshness info
   */
  async getCacheInfo(): Promise<{
    totalCachedContent: number;
    lastUpdated: Date;
    cacheAge: number; // seconds
  }> {
    const result = await this.cacheRepository
      .createQueryBuilder('cache')
      .select(['COUNT(*) as total', 'MAX(cache.lastUpdated) as lastUpdated'])
      .getRawOne();

    const lastUpdated = result.lastUpdated || new Date(0);
    const cacheAge = Math.floor((Date.now() - lastUpdated.getTime()) / 1000);

    return {
      totalCachedContent: parseInt(result.total) || 0,
      lastUpdated,
      cacheAge,
    };
  }

  /**
   * Get content analytics data by date range
   */
  async getContentAnalyticsByDateRange(filters: {
    startDate?: string;
    endDate?: string;
    type?: string;
    author?: string;
    genre?: string;
    series?: string;
    limit?: number;
    offset?: number;
  }): Promise<{
    data: DateRangeAnalyticsResponseDto[];
    total: number;
  }> {
    const { startDate, endDate, type, author, genre, series, limit, offset } =
      filters;

    // Build the base query
    let query = `
      SELECT 
        c.id as contentId,
        c.title,
        COALESCE(a.name, 'Unknown') as author,
        COALESCE(g.name, 'Unknown') as genre,
        CASE WHEN c.isAdult = 1 THEN 'adult' ELSE 'general' END as type,
        COALESCE(SUM(ca.viewCount), 0) as totalViews,
        COALESCE(SUM(ca.uniqueViewers), 0) as uniqueViewers,
        COALESCE(SUM(ca.totalReadTime), 0) as totalReadTime,
        CASE 
          WHEN SUM(ca.viewCount) > 0 THEN SUM(ca.totalReadTime) / SUM(ca.viewCount)
          ELSE 0
        END as averageReadTime,
        COUNT(DISTINCT ca.episodeId) as episodesViewed
      FROM content c
      LEFT JOIN author a ON c.authorId = a.id
      LEFT JOIN genre g ON c.genreId = g.id
      LEFT JOIN content_setting cs ON c.id = cs.contentId AND cs.type = 'date'
      LEFT JOIN settings s ON cs.settingId = s.id AND s.group = 'content_date'
      LEFT JOIN content_analytics ca ON c.id = ca.contentId
    `;

    const conditions: string[] = ['c.deletedAt IS NULL'];
    const params: any[] = [];

    // Add date filters
    if (startDate) {
      conditions.push('ca.date >= ?');
      params.push(startDate);
    }
    if (endDate) {
      conditions.push('ca.date <= ?');
      params.push(endDate);
    }

    // Add content filters
    if (type) {
      if (type === 'adult') {
        conditions.push('c.isAdult = 1');
      } else {
        conditions.push('c.isAdult = 0');
      }
    }
    if (author) {
      conditions.push('a.name LIKE ?');
      params.push(`%${author}%`);
    }
    if (genre) {
      conditions.push('g.name LIKE ?');
      params.push(`%${genre}%`);
    }
    if (series) {
      if (series === 'completed') {
        conditions.push('s.key = ?');
        params.push('completed');
      } else {
        conditions.push('(s.key != ? OR s.key IS NULL)');
        params.push('completed');
      }
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' GROUP BY c.id, c.title, a.name, g.name, c.isAdult';
    query += ' ORDER BY totalViews DESC';

    // Get total count
    const countQuery = `
      SELECT COUNT(DISTINCT c.id) as total
      FROM content c
      LEFT JOIN author a ON c.authorId = a.id
      LEFT JOIN genre g ON c.genreId = g.id
      LEFT JOIN content_setting cs ON c.id = cs.contentId AND cs.type = 'date'
      LEFT JOIN settings s ON cs.settingId = s.id AND s.group = 'content_date'
      LEFT JOIN content_analytics ca ON c.id = ca.contentId
      ${conditions.length > 0 ? 'WHERE ' + conditions.join(' AND ') : ''}
    `;

    const [results, countResult] = await Promise.all([
      this.contentAnalyticsRepository.query(
        query +
          (limit ? ` LIMIT ${limit}` : '') +
          (offset ? ` OFFSET ${offset}` : ''),
        params,
      ),
      this.contentAnalyticsRepository.query(countQuery, params),
    ]);

    // Get daily breakdown for each content
    const contentIds = results.map((r: any) => r.contentId);
    const dailyBreakdown = await this.getDailyBreakdownForContents(
      contentIds,
      startDate,
      endDate,
    );

    // Format response
    const data: DateRangeAnalyticsResponseDto[] = results.map((row: any) => ({
      contentId: row.contentId,
      title: row.title,
      author: row.author,
      genre: row.genre,
      type: row.type,
      totalViews: parseInt(row.totalViews) || 0,
      uniqueViewers: parseInt(row.uniqueViewers) || 0,
      totalReadTime: parseInt(row.totalReadTime) || 0,
      averageReadTime: parseFloat(row.averageReadTime) || 0,
      episodesViewed: parseInt(row.episodesViewed) || 0,
      dailyBreakdown: dailyBreakdown[row.contentId] || [],
    }));

    return {
      data,
      total: parseInt(countResult[0]?.total) || 0,
    };
  }

  /**
   * Get daily breakdown for specific contents
   */
  private async getDailyBreakdownForContents(
    contentIds: number[],
    startDate?: string,
    endDate?: string,
  ): Promise<Record<number, DailyAnalyticsDto[]>> {
    if (contentIds.length === 0) return {};

    let query = `
      SELECT 
        ca.contentId,
        ca.date,
        SUM(ca.viewCount) as views,
        SUM(ca.uniqueViewers) as uniqueViewers,
        SUM(ca.totalReadTime) as readTime
      FROM content_analytics ca
      WHERE ca.contentId IN (${contentIds.map(() => '?').join(',')})
    `;

    const params: any[] = [...contentIds];

    if (startDate) {
      query += ' AND ca.date >= ?';
      params.push(startDate);
    }
    if (endDate) {
      query += ' AND ca.date <= ?';
      params.push(endDate);
    }

    query += ' GROUP BY ca.contentId, ca.date ORDER BY ca.contentId, ca.date';

    const results = await this.contentAnalyticsRepository.query(query, params);

    // Group by contentId
    const grouped: Record<number, DailyAnalyticsDto[]> = {};
    for (const row of results) {
      if (!grouped[row.contentId]) {
        grouped[row.contentId] = [];
      }
      grouped[row.contentId].push({
        date: row.date.toISOString().split('T')[0], // Format as YYYY-MM-DD
        views: parseInt(row.views) || 0,
        uniqueViewers: parseInt(row.uniqueViewers) || 0,
        readTime: parseInt(row.readTime) || 0,
      });
    }

    return grouped;
  }

  /**
   * Update daily ranking stats from content analytics data
   */
  async updateDailyRankingStats(date: string): Promise<void> {
    const startTime = Date.now();
    this.logger.log(`Starting daily ranking stats update for ${date}...`);

    try {
      const query = `
        INSERT INTO content_ranking_stats 
        (contentId, title, author, authorId, genre, genreId, type, series, episodes, 
         periodType, date, freeEpisodeRead, paidEpisodeRead, totalEpisodeRead, 
         totalViews, uniqueViewers, totalReadTime, createdAt, updatedAt)
        SELECT 
          ca.contentId,
          MAX(c.title) as title,
          MAX(COALESCE(a.name, 'Unknown')) as author,
          MAX(COALESCE(a.id, NULL)) as authorId,
          MAX(COALESCE(g.name, 'Unknown')) as genre,
          MAX(COALESCE(g.id, NULL)) as genreId,
          MAX(CASE WHEN c.isAdult = 1 THEN 'adult' ELSE 'general' END) as type,
          MAX(CASE 
            WHEN s.key = 'completed' THEN 'completed' 
            ELSE 'ongoing' 
          END) as series,
          (SELECT COUNT(*) FROM episode e WHERE e.contentId = ca.contentId) as episodes,
          'daily' as periodType,
          ca.date,
          SUM(CASE WHEN e.paymentType = 'free' THEN ca.viewCount ELSE 0 END) as freeEpisodeRead,
          SUM(CASE WHEN e.paymentType = 'paid' THEN ca.viewCount ELSE 0 END) as paidEpisodeRead,
          SUM(ca.viewCount) as totalEpisodeRead,
          SUM(ca.viewCount) as totalViews,
          SUM(ca.uniqueViewers) as uniqueViewers,
          SUM(ca.totalReadTime) as totalReadTime,
          NOW(),
          NOW()
        FROM content_analytics ca
        JOIN content c ON ca.contentId = c.id
        LEFT JOIN episode e ON ca.episodeId = e.id
        LEFT JOIN author a ON c.authorId = a.id
        LEFT JOIN genre g ON c.genreId = g.id
        LEFT JOIN content_setting cs ON c.id = cs.contentId AND cs.type = 'date'
        LEFT JOIN settings s ON cs.settingId = s.id AND s.group = 'content_date'
        WHERE ca.date = ? AND c.deletedAt IS NULL
        GROUP BY ca.contentId, ca.date
        ON DUPLICATE KEY UPDATE
          title = VALUES(title),
          author = VALUES(author),
          authorId = VALUES(authorId),
          genre = VALUES(genre),
          genreId = VALUES(genreId),
          type = VALUES(type),
          series = VALUES(series),
          episodes = VALUES(episodes),
          freeEpisodeRead = VALUES(freeEpisodeRead),
          paidEpisodeRead = VALUES(paidEpisodeRead),
          totalEpisodeRead = VALUES(totalEpisodeRead),
          totalViews = VALUES(totalViews),
          uniqueViewers = VALUES(uniqueViewers),
          totalReadTime = VALUES(totalReadTime),
          updatedAt = NOW()
      `;

      await this.rankingStatsRepository.query(query, [date]);

      const duration = Date.now() - startTime;
      this.logger.log(
        `Daily ranking stats updated successfully for ${date}. Duration: ${duration}ms`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update daily ranking stats for ${date}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Update all-time ranking stats from content statistics cache
   */
  async updateAllTimeRankingStats(): Promise<void> {
    const startTime = Date.now();
    this.logger.log('Starting all-time ranking stats update...');

    try {
      const query = `
        INSERT INTO content_ranking_stats 
        (contentId, title, author, authorId, genre, genreId, type, series, episodes, 
         periodType, date, freeEpisodeRead, paidEpisodeRead, totalEpisodeRead, 
         totalViews, uniqueViewers, totalReadTime, createdAt, updatedAt)
        SELECT 
          cache.contentId,
          cache.title,
          cache.author,
          cache.authorId,
          cache.genre,
          cache.genreId,
          cache.type,
          cache.series,
          cache.episodes,
          'all_time' as periodType,
          NULL as date,
          cache.freeEpisodeRead,
          cache.paidEpisodeRead,
          cache.totalEpisodeRead,
          cache.totalEpisodeRead as totalViews,
          0 as uniqueViewers,
          0 as totalReadTime,
          NOW(),
          NOW()
        FROM content_statistics_cache cache
        ON DUPLICATE KEY UPDATE
          title = VALUES(title),
          author = VALUES(author),
          authorId = VALUES(authorId),
          genre = VALUES(genre),
          genreId = VALUES(genreId),
          type = VALUES(type),
          series = VALUES(series),
          episodes = VALUES(episodes),
          freeEpisodeRead = VALUES(freeEpisodeRead),
          paidEpisodeRead = VALUES(paidEpisodeRead),
          totalEpisodeRead = VALUES(totalEpisodeRead),
          totalViews = VALUES(totalViews),
          updatedAt = NOW()
      `;

      await this.rankingStatsRepository.query(query);

      const duration = Date.now() - startTime;
      this.logger.log(
        `All-time ranking stats updated successfully. Duration: ${duration}ms`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to update all-time ranking stats: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
