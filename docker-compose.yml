version: '3'
services:
  # app:
  #   container_name: nest_app
  #   build:
  #     context: .
  #     dockerfile: Dockerfile
  #   ports:
  #     - "3005:3005"
  #   depends_on:
  #     - db
  #     - redis
  #   networks:
  #     - nest-network
  #   restart: unless-stopped
  #   volumes:
  #     - ./uploads:/app/uploads

  db:
    container_name: trunk_db
    image: 'mysql:8.0.19'
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
    ports:
      - '33071:3306'
    volumes:
      - db-store:/var/lib/mysql
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - nest-network

  redis:
    container_name: trunk_redis
    image: redis:latest
    ports:
      - '63799:6379'
    restart: always
    volumes:
      - redis-store:/data
    networks:
      - nest-network

  mailhog:
    image: mailhog/mailhog
    container_name: mailhog
    ports:
      - '1025:1025'
      - '8025:8025'
    networks:
      - nest-network

volumes:
  db-store:
  redis-store:

networks:
  nest-network:
    driver: bridge
