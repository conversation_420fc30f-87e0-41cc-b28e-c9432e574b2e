import { AuthAdminService } from './auth-admin.service';
import { Controller, Post, Body, UseGuards, Get, Query } from '@nestjs/common';
import { AdminLoginDto } from './dtos/login.dto';
import { CreateAdminDto } from './dtos/create-admin.dto';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from '../../../common/role.enum';
import { JwtAdminAuthGuard } from './guards/jwt-admin-auth.guard';
import { RoleGuard } from './guards/role.guard';
import { ForgotPasswordDto } from '../../auth/dtos/forgot-password.dto';
import { ResetPasswordDto } from '../../auth/dtos/reset-password.dto';
@ApiTags('Admin Auth')
@Controller('admin/auth')
export class AuthController {
  constructor(private authAdminService: AuthAdminService) {}

  @Post('login')
  @ApiOperation({ summary: 'Login' })
  async login(@Body() loginDto: AdminLoginDto) {
    return this.authAdminService.login(loginDto);
  }

  @Post('create-admin')
  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard, RoleGuard)
  @Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
  @ApiOperation({ summary: 'Create Admin' })
  createAdmin(@Body() createAdminDto: CreateAdminDto) {
    return this.authAdminService.createAdmin(createAdminDto);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Forgot Password' })
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authAdminService.forgotPassword(forgotPasswordDto);
  }

  @Get('check-forgot-password-token')
  @ApiOperation({ summary: 'Check Forgot Password Token' })
  checkForgotPasswordToken(@Query('token') token: string) {
    return this.authAdminService.checkForgotPasswordToken(token);
  }

  @Post('reset-password')
  @ApiOperation({ summary: 'Reset Password' })
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authAdminService.resetPassword(resetPasswordDto);
  }
}
