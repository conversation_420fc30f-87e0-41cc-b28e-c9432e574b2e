import { MigrationInterface, QueryRunner } from 'typeorm';

export class SupportQandA1753174976769 implements MigrationInterface {
  name = 'SupportQandA1753174976769';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`support_category\` (\`id\` int NOT NULL AUTO_INCREMENT, \`name\` varchar(255) NOT NULL, \`title\` varchar(255) NOT NULL, \`description\` varchar(255) NULL, \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`order\` int NOT NULL DEFAULT '1', \`expected_response_time\` int NULL, \`expected_resolution_time\` int NULL, \`default_assignee_id\` int NULL, \`auto_assign\` tinyint NOT NULL DEFAULT 0, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, UNIQUE INDEX \`IDX_78e3a15a5e2eadd63d7119b0cd\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`support_response\` (\`id\` int NOT NULL AUTO_INCREMENT, \`ticket_id\` int NOT NULL, \`admin_id\` int NOT NULL, \`response\` text NOT NULL, \`attachments\` json NULL, \`status\` enum ('draft', 'sent', 'auto_reply') NOT NULL DEFAULT 'sent', \`response_completed_time\` datetime NULL, \`is_internal\` tinyint NOT NULL DEFAULT 0, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`support_ticket\` (\`id\` int NOT NULL AUTO_INCREMENT, \`user_id\` int NOT NULL, \`category_id\` int NULL, \`ticket_number\` varchar(255) NOT NULL, \`subject\` varchar(255) NOT NULL, \`description\` text NOT NULL, \`attachments\` json NULL, \`priority\` enum ('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium', \`status\` enum ('open', 'assigned', 'in_progress', 'pending_user', 'closed') NOT NULL DEFAULT 'open', \`assigned_admin_id\` int NULL, \`is_read\` tinyint NOT NULL DEFAULT 0, \`response_check_time\` datetime NULL, \`closed_at\` datetime NULL, \`contextData\` json NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, UNIQUE INDEX \`IDX_6ec248be1e1834f3982ac39af4\` (\`ticket_number\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_response\` ADD CONSTRAINT \`FK_bd06fd83edef4d1195af6cea69d\` FOREIGN KEY (\`ticket_id\`) REFERENCES \`support_ticket\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_response\` ADD CONSTRAINT \`FK_2cbd584a811a7978bd0c937390e\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_ticket\` ADD CONSTRAINT \`FK_cb2e00c16c925e889f6e2dc2a4b\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_ticket\` ADD CONSTRAINT \`FK_aa6979721cd1f92e978d0ff7cac\` FOREIGN KEY (\`category_id\`) REFERENCES \`support_category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_ticket\` ADD CONSTRAINT \`FK_ac05fbf263d156b6f5d8e7ceeab\` FOREIGN KEY (\`assigned_admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`support_ticket\` DROP FOREIGN KEY \`FK_ac05fbf263d156b6f5d8e7ceeab\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_ticket\` DROP FOREIGN KEY \`FK_aa6979721cd1f92e978d0ff7cac\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_ticket\` DROP FOREIGN KEY \`FK_cb2e00c16c925e889f6e2dc2a4b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_response\` DROP FOREIGN KEY \`FK_2cbd584a811a7978bd0c937390e\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`support_response\` DROP FOREIGN KEY \`FK_bd06fd83edef4d1195af6cea69d\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_6ec248be1e1834f3982ac39af4\` ON \`support_ticket\``,
    );
    await queryRunner.query(`DROP TABLE \`support_ticket\``);
    await queryRunner.query(`DROP TABLE \`support_response\``);
    await queryRunner.query(
      `DROP INDEX \`IDX_78e3a15a5e2eadd63d7119b0cd\` ON \`support_category\``,
    );
    await queryRunner.query(`DROP TABLE \`support_category\``);
  }
}
