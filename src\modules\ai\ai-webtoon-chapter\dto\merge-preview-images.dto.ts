import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { <PERSON>Array, IsEnum, IsInt, IsString, IsUrl, Min } from 'class-validator';

export enum ImageFormat {
  JPG = 'jpg',
  PNG = 'png',
}

export class MergePreviewImagesDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  chapterId: number;

  @ApiProperty()
  @IsString()
  background: string;

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(0)
  gap: number;

  @ApiProperty({ type: [String] })
  @IsArray()
  @IsUrl({}, { each: true })
  images: string[];

  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  width: number;

  @ApiProperty({ enum: ImageFormat })
  @IsEnum(ImageFormat)
  format: ImageFormat;
}
