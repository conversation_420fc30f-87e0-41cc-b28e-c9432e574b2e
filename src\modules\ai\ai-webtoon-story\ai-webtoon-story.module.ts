import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiLetteringLanguageRepository } from 'src/database/repositories/ai-lettering-language.repository';
import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { AiWebtoonStoryAiLetteringRepository } from 'src/database/repositories/ai-webtoon-story-ai-lettering.repository';
import { AiWebtoonStoryRepository } from 'src/database/repositories/ai-webtoon-story.repository';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';
import { AiWebtoonCharacterModule } from '../ai-webtoon-character/ai-webtoon-character.module';
import { AiWebtoonCutChapterModule } from '../ai-webtoon-cut-chapter/ai-webtoon-cut-chapter.module';
import { AiWebtoonStory } from './ai-webtoon-story.controller';
import { AiWebtoonStoryService } from './ai-webtoon-story.service';
// import { SocketModule } from '../socket/socket.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonStoryRepository,
      AiWebtoonChapterRepository,
      AiWebtoonCutChapterRepository,
      AiWebtoonSceneChapterRepository,
      AiLetteringLanguageRepository,
      AiWebtoonStoryAiLetteringRepository,
      AiLetteringRepository,
    ]),
    AiWebtoonCharacterModule,
    AiWebtoonCutChapterModule,
    // SocketModule,
  ],
  controllers: [AiWebtoonStory],
  providers: [AiWebtoonStoryService],
  exports: [AiWebtoonStoryService],
})
export class AiWebtoonStoryModule {}
