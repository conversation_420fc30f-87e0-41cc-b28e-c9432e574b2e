import { EAiLetteringClassificationType } from 'src/common/ai-webtoon.enum';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { AiWebtoonStoryAiLetteringEntity } from './ai-webtoon-story-ai-lettering.entity';
import { DefaultEntity } from './default.entity';
import { AiLetteringLanguageEntity } from './ai-lettering-language.entity';
import { AiLetteringTypeEntity } from './ai-lettering-type.entity';

@Entity('ai_lettering')
export class AiLetteringEntity extends DefaultEntity {
  @Column({ name: 'ai_lettering_language_id', type: 'int', nullable: true })
  aiLetteringLanguageId: number;

  @Column({ name: 'ai_lettering_type_id', type: 'int', nullable: true })
  aiLetteringTypeId: number;

  @Index({ unique: true, where: 'deletedAt IS NULL' })
  @Column({ type: 'varchar' })
  name: string;

  @Column({
    type: 'enum',
    enum: EAiLetteringClassificationType,
    default: EAiLetteringClassificationType.ALL,
  })
  classification: EAiLetteringClassificationType;

  @Column({ type: 'varchar' })
  extension: string;

  @Column({ type: 'varchar' })
  url: string;

  @Column({ name: 'thumbnail_url', type: 'varchar' })
  thumbnailUrl: string;

  @ManyToOne(() => AiLetteringLanguageEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ai_lettering_language_id' })
  aiLetteringLanguage: AiLetteringLanguageEntity;

  @ManyToOne(() => AiLetteringTypeEntity, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'ai_lettering_type_id' })
  aiLetteringType: AiLetteringTypeEntity;

  @OneToMany(
    () => AiWebtoonStoryAiLetteringEntity,
    (aiWebtoonStoryAiLettering) => aiWebtoonStoryAiLettering.aiLettering,
  )
  aiWebtoonStoryAiLetterings: AiWebtoonStoryAiLetteringEntity[];
}
