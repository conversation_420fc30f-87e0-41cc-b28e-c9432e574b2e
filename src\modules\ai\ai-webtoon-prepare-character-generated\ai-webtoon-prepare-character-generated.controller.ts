import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  UseGuards,
} from '@nestjs/common';
import { Api<PERSON>earerAuth, ApiTags } from '@nestjs/swagger';
import { AiWebtoonPrepareCharacterGeneratedService } from './ai-webtoon-prepare-character-generated.service';

import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';
import { GenerateCutImagesDto } from './dto/generate-cut-images.dto';
import { ReceiveCutImagesDto } from './dto/receive-cut-images.dto';
import { UpdatePrepareCharacterGeneratedDto } from './dto/update.dto';

@ApiTags('ai-webtoon-prepare-character-generated')
@Controller('ai-webtoon-prepare-character-generated')
export class AiWebtoonPrepareCharacterGeneratedController {
  constructor(
    private readonly aiWebtoonPrepareCharacterGeneratedService: AiWebtoonPrepareCharacterGeneratedService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('list-by-prepare-character/:prepareCharacterId')
  listByPrepareCharacter(
    @Param('prepareCharacterId', ParseIntPipe) prepareCharacterId: number,
  ) {
    return this.aiWebtoonPrepareCharacterGeneratedService.listByPrepareCharacter(
      prepareCharacterId,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('generate-cut-images/:id')
  generateCutImages(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: GenerateCutImagesDto,
  ) {
    return this.aiWebtoonPrepareCharacterGeneratedService.generateCutImages(
      id,
      body,
    );
  }

  @Post('receive-cut-images')
  receiveCutImages(@Body() body: ReceiveCutImagesDto) {
    return this.aiWebtoonPrepareCharacterGeneratedService.receiveCutImages(
      body,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/:id')
  detail(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonPrepareCharacterGeneratedService.detail(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/:id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdatePrepareCharacterGeneratedDto,
  ) {
    return this.aiWebtoonPrepareCharacterGeneratedService.update(id, body);
  }
}
