import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsArray,
  IsUrl,
  <PERSON>Length,
} from 'class-validator';

export class UserFollowUpDto {
  @ApiProperty({
    example:
      'I tried the suggested solution but the issue persists. Here are more details...',
    description: 'Additional information or follow-up message from user',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(2000)
  message: string;

  @ApiProperty({
    example: ['https://example.com/additional-screenshot.png'],
    description: 'Additional attachment URLs for follow-up',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  attachments?: string[];
}
