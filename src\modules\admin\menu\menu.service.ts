import { Injectable, NotFoundException } from '@nestjs/common';
import { MenuRepository } from '../../../database/repositories/menu.repository';
import { CreateMenuDto } from './dtos/create-menu.dto';
import { MenuEntity } from '../../../database/entities/menu.entity';
import { MenuAdminStatus, MenuStatus } from '../../../common/status.enum';
import { MenuAdminRepository } from '../../../database/repositories/menu-admin.repository';
import { In } from 'typeorm';
import { UpdateMenuDto } from './dtos/update-menu.dto';

@Injectable()
export class MenuService {
  constructor(
    private menuRepository: MenuRepository,
    private menuAdminRepository: MenuAdminRepository,
  ) {}

  async getMenu() {
    const allMenuItems = await this.menuRepository.find({
      where: { status: MenuStatus.ACTIVE },
      order: { order: 'ASC' },
    });

    const parentMenus = allMenuItems.filter((item) => !item.parentId);
    const structuredMenu = parentMenus.map((parent) => {
      const children = allMenuItems.filter(
        (item) => item.parentId === parent.id,
      );
      return {
        ...parent,
        children: children.length > 0 ? children : undefined,
      };
    });

    return structuredMenu;
  }

  async getParentMenu() {
    return await this.menuRepository.find({
      where: { status: MenuStatus.ACTIVE, parentId: 0 },
      order: { order: 'ASC' },
    });
  }

  async getChildrenMenu(parentId: number) {
    return await this.menuRepository.find({
      where: { status: MenuStatus.ACTIVE, parentId },
      order: { order: 'ASC' },
    });
  }

  async createMenu(createMenuDto: CreateMenuDto) {
    const menu = new MenuEntity();
    menu.key = createMenuDto.key;
    menu.label = createMenuDto.label;
    menu.icon = createMenuDto.icon;
    menu.route = createMenuDto.route;
    menu.order = createMenuDto.order;
    menu.parentId = createMenuDto.parentId;
    return await this.menuRepository.save(menu);
  }

  async getMenuByAdminId(adminId: number) {
    const parentMenus = await this.menuRepository.find({
      where: { parentId: 0 },
      order: { order: 'ASC' },
    });
    // console.log(adminId);

    const menusAdmin = await this.menuAdminRepository.find({
      where: { admin: { id: adminId } },
      relations: ['menu'],
    });

    const menus = parentMenus.map((menu) => {
      const children = menusAdmin.filter((m) => m.menu.parentId === menu.id);
      return {
        ...menu,
        children,
      };
    });

    return menus;
  }

  async getMenuAdmin(adminId: number, key: string[]) {
    return await this.menuAdminRepository.find({
      select: {
        menu: {
          id: true,
          key: true,
        },
      },
      where: {
        admin: { id: adminId },
        status: MenuAdminStatus.ACTIVE,
        menu: { key: In(key) },
      },
      relations: ['menu'],
    });
  }

  async updateMenu(id: number, updateMenuDto: UpdateMenuDto) {
    const menu = await this.menuRepository.findOne({
      where: { id },
    });

    if (!menu) {
      throw new NotFoundException('Menu not found');
    }

    menu.key = updateMenuDto.key;
    menu.label = updateMenuDto.label;
    menu.icon = updateMenuDto.icon;
    menu.route = updateMenuDto.route;
    menu.order = updateMenuDto.order;
    menu.parentId = updateMenuDto.parentId;
    return await this.menuRepository.save(menu);
  }
}
