import { Modu<PERSON> } from '@nestjs/common';
import { DisplayManagementController } from './display-management.controller';
import { DisplayManagementService } from './display-management.service';
import { DisplayRepository } from '../../../database/repositories/display.repository';
import { DisplayEntity } from '../../../database/entities/display.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ContentRepository } from '../../../database/repositories/content.repository';
import { GenreEntity } from '../../../database/entities/genre.entity';
import { GenreRepository } from '../../../database/repositories/genre.repository';

@Module({
  imports: [TypeOrmModule.forFeature([DisplayEntity, GenreEntity])],
  controllers: [DisplayManagementController],
  providers: [
    DisplayManagementService,
    DisplayRepository,
    ContentRepository,
    GenreRepository,
  ],
  exports: [DisplayManagementService],
})
export class DisplayManagementModule {}
