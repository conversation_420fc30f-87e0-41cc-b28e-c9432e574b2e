import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonTrainingLogEntity } from '../entities/ai-webtoon-training-log.entity';

@Injectable()
export class AiWebtoonTrainingLogRepository extends Repository<AiWebtoonTrainingLogEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonTrainingLogEntity, dataSource.createEntityManager());
  }
}
