import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiApiGenStateManagementModule } from '../ai-api-gen-state-management/ai-api-gen-state-management.module';
import { AiServiceModule } from '../ai-service/ai-service.module';
// import { SocketModule } from '../socket/socket.module';
import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { AiWebtoonSceneChapterController } from './ai-webtoon-scene-chapter.controller';
import { AiWebtoonSceneChapterService } from './ai-webtoon-scene-chapter.service';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonSceneChapterRepository,
      AiWebtoonChapterRepository,
      AiWebtoonCutChapterRepository,
    ]),
    AiServiceModule,
    AiApiGenStateManagementModule,
    // SocketModule,
  ],
  controllers: [AiWebtoonSceneChapterController],
  providers: [AiWebtoonSceneChapterService],
  exports: [AiWebtoonSceneChapterService],
})
export class AiWebtoonSceneChapterModule {}
