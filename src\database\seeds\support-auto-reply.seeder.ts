import { DataSource } from 'typeorm';
import { SettingEntity } from '../entities/setting.entity';
import { SettingMetadata } from '../entities/setting_metadata.entity';
import { SettingGroup } from '../../common/setting.enum';
import { SettingStatus } from '../../common/status.enum';

export async function seedSupportAutoReplySettings(
  dataSource: DataSource,
): Promise<void> {
  const settingRepository = dataSource.getRepository(SettingEntity);
  const metadataRepository = dataSource.getRepository(SettingMetadata);

  // Check if support auto-reply settings already exist
  const existingSettings = await settingRepository.findOne({
    where: { group: SettingGroup.SUPPORT_AUTO_REPLY },
  });

  if (existingSettings) {
    console.log('Support auto-reply settings already seeded');
    return;
  }

  try {
    // Create main auto-reply setting
    const autoReplySetting = settingRepository.create({
      key: 'qa_auto_reply_global',
      value: 'Global Q&A Auto Reply Configuration',
      group: SettingGroup.SUPPORT_AUTO_REPLY,
      order: 1,
      status: SettingStatus.ACTIVE,
    });

    const savedSetting = await settingRepository.save(autoReplySetting);

    // Create metadata for the auto-reply setting
    const metadataItems = [
      {
        key: 'enabled',
        value: 'true', // Enable auto-reply by default
        setting: savedSetting,
      },
      {
        key: 'content',
        value:
          'Thank you for contacting us. We have received your inquiry and will respond within 24 hours. For urgent matters, please include "URGENT" in your subject line.',
        setting: savedSetting,
      },
      {
        key: 'delay_minutes',
        value: '2', // Send auto-reply after 2 minutes
        setting: savedSetting,
      },
      {
        key: 'admin_username',
        value: 'root', // Root admin for auto-replies
        setting: savedSetting,
      },
      {
        key: 'email_template',
        value: 'qa-auto-reply', // Email template name
        setting: savedSetting,
      },
      {
        key: 'subject_prefix',
        value: 'Re: ', // Auto-reply email subject prefix
        setting: savedSetting,
      },
    ];

    const metadata = metadataRepository.create(metadataItems);
    await metadataRepository.save(metadata);

    console.log('✅ Successfully seeded support auto-reply settings');
    console.log(`  - Main setting: ${savedSetting.key}`);
    console.log(`  - Metadata items: ${metadata.length}`);
  } catch (error) {
    console.error(
      '❌ Error seeding support auto-reply settings:',
      error.message,
    );
    throw error;
  }
}
