import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
  Min,
} from 'class-validator';
import {
  EAiBaseModelGenImageType,
  EAiWebtoonStoryBorderType,
} from 'src/common/ai-webtoon.enum';
import { IsValidSeed } from 'src/common/decorators/is-valid-seed.decorator';

export class ReceiveGenerateImageDto {
  @ApiProperty()
  @IsInt()
  @Min(1)
  chapterId: number;

  @ApiProperty()
  @IsInt()
  @Min(1)
  id: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  image: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  imageWithBorder: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  rootImage: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  imageWidth: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsNumber()
  imageHeight: number;

  @ApiProperty({ enum: EAiWebtoonStoryBorderType })
  @IsNotEmpty()
  @IsEnum(EAiWebtoonStoryBorderType)
  borderType: EAiWebtoonStoryBorderType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  borderColor: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  borderWeight: number;

  @ApiProperty({
    required: false,
    description:
      'Seed value as a string, must be a number less than 18446744073709551615',
  })
  @IsString()
  @IsOptional()
  @IsValidSeed()
  seed: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  sampler: string;

  @ApiProperty({ required: false })
  @IsInt()
  @IsOptional()
  steps: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  cfgScale: number;

  @ApiProperty()
  @IsBoolean()
  isDone: boolean;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  apiName: string;

  @ApiProperty()
  @IsEnum(EAiBaseModelGenImageType)
  baseModel: EAiBaseModelGenImageType;
}
