import {
  EAiBaseModelInpaintType,
  EAiFrameImageSizeType,
  EAiGenPromptByDescriptionType,
  EAiWebtoonCharacterType,
  EAiWebtoonStoryBorderType,
} from 'src/common/ai-webtoon.enum';
import { IAiGeneralCharacter } from './ai-webtoon-character.interface';
import { AiWebtoonSyntheticDataType } from 'src/modules/ai/ai-webtoon-synthetic-data/dto/add-images.dto';

export interface IGenerateImageDataItem {
  id: number;
  description: string;
  negativePrompt?: string;
  generalPrompt: string;
  characters: IAiGeneralCharacter[];
  dialogues: any[];
  numberOfImages: number;
  width: number;
  height: number;
  rotate: EAiFrameImageSizeType;
  baseModel: string;
  sketchImage?: string;
  depthImage?: string;
  seed?: string;
  cfgScale: number;
  steps: number;
  controlNetStrength: number;
  borderType: EAiWebtoonStoryBorderType;
  borderColor: string;
  borderWeight: number;
}

export interface ISendGenerateCutImagesRequest {
  chapterId: number;
  pathReceived: string;
  data: IGenerateImageDataItem[];
  apiName?: string;
}

export interface ISendInpaintRequest {
  uuid?: string;
  cutId: number;
  numberOfImages?: number;
  negativePrompt?: string;
  originImage: string;
  maskImage: string;
  prompt: string;
  characterIds: number[];
  baseModel: EAiBaseModelInpaintType;
  pathReceived: string;
  apiName?: string;
}

export interface IGeneratePromptByDescriptionItem {
  cutId: number;
  typePrompt: EAiGenPromptByDescriptionType;
  description: string;
  characters: IAiGeneralCharacter[];
}

export interface ISendGeneratePromptByDescriptionRequest {
  isImportFile: boolean;
  data: IGeneratePromptByDescriptionItem[];
}

export interface ISendTestingDataRequest {
  uuid: string;
  numberOfImages: number;
  data: {
    characters: {
      character: string;
      gender: string;
      characterId: number;
    }[];
    prompt: {
      bg?: string;
      generalCharacters: string;
    };
    characterDesc?: {
      character: string;
      description: string;
    }[];
  }[];
  model?: string;
}

export interface IGenerateSamplePromptsRequest {
  countResult: number;
  prompts: string[];
}

export interface ISendGenerateSceneImagesRequest {
  apiName?: string;
  sceneId: number;
  description?: string;
  prompt: string;
  numberOfImages: number;
  width: number;
  height: number;
  rotate: string;
}
export interface ISendGenerateDescriptionLabelByImagesRequest {
  image_links: string[];
  prompt: string;
}

export interface ISendCharacterTrainingSyncRequest {
  modelId: string;
  modelUrl: string;
  typeModel: string;
  characterType: EAiWebtoonCharacterType;
}

export interface ISendGenerateCollectionImagesRequest {
  aiWebtoonPrepareCharacterGeneratedId: number;
  characterId: number | null;
  numberOfImages: number;
  loraStrength: number;
  prompt: string;
}

interface IDataItem {
  uuid: string;
  image: string;
  prompt?: string;
}

export interface IAiWebtoonSyntheticDataSend {
  characterId?: number;
  syntheticDataId: number;
  pathReceived: string;
  data: IDataItem[];
  instancePrompt?: string;
  type: AiWebtoonSyntheticDataType;
  apiName?: string;
}
