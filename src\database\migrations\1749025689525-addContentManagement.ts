import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddContentManagement1749025689525 implements MigrationInterface {
  name = 'AddContentManagement1749025689525';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`genre\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(255) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`content\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`title\` varchar(255) NOT NULL, \`description\` varchar(255) NOT NULL, \`content\` longtext NOT NULL, \`image\` varchar(255) NOT NULL, \`genreId\` int NULL, UNIQUE INDEX \`REL_ef067ee3c41ac74d4f4f109d71\` (\`genreId\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`content_setting\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`contentId\` int NULL, \`settingId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `CREATE TABLE \`author\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`name\` varchar(255) NOT NULL, \`email\` varchar(255) NULL, \`bankNumber\` varchar(255) NULL, \`memo\` text NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD CONSTRAINT \`FK_ef067ee3c41ac74d4f4f109d712\` FOREIGN KEY (\`genreId\`) REFERENCES \`genre\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` ADD CONSTRAINT \`FK_971076410aca05825f06c23133f\` FOREIGN KEY (\`contentId\`) REFERENCES \`content\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` ADD CONSTRAINT \`FK_59c497d44651042e19b79e0f086\` FOREIGN KEY (\`settingId\`) REFERENCES \`settings\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` DROP FOREIGN KEY \`FK_59c497d44651042e19b79e0f086\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` DROP FOREIGN KEY \`FK_971076410aca05825f06c23133f\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP FOREIGN KEY \`FK_ef067ee3c41ac74d4f4f109d712\``,
    );
    await queryRunner.query(`DROP TABLE \`author\``);
    await queryRunner.query(`DROP TABLE \`content_setting\``);
    await queryRunner.query(
      `DROP INDEX \`REL_ef067ee3c41ac74d4f4f109d71\` ON \`content\``,
    );
    await queryRunner.query(`DROP TABLE \`content\``);
    await queryRunner.query(`DROP TABLE \`genre\``);
  }
}
