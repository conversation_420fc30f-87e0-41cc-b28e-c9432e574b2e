import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  Index,
} from 'typeorm';

export enum UploaderType {
  USER = 'user',
  ADMIN = 'admin',
}

export enum FileType {
  IMAGE = 'image',
  DOCUMENT = 'document',
}

export enum UploadContext {
  PROFILE = 'profile',
  CONTENT = 'content',
  SUPPORT = 'support',
  SETTINGS = 'settings',
  GENERAL = 'general',
  BULK = 'bulk',
}

@Entity('uploads')
@Index(['uploaderId', 'uploaderType'])
@Index(['uploadContext'])
@Index(['fileType'])
@Index(['createdAt'])
export class UploadEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: false })
  @Index()
  uploaderId: number;

  @Column({
    type: 'enum',
    enum: UploaderType,
    nullable: false,
  })
  uploaderType: UploaderType;

  @Column({ type: 'varchar', length: 255, nullable: false })
  originalName: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  fileName: string;

  @Column({ type: 'varchar', length: 500, nullable: false })
  filePath: string;

  @Column({ type: 'bigint', nullable: false })
  fileSize: number;

  @Column({ type: 'varchar', length: 100, nullable: false })
  mimeType: string;

  @Column({
    type: 'enum',
    enum: FileType,
    nullable: false,
  })
  fileType: FileType;

  @Column({
    type: 'enum',
    enum: UploadContext,
    nullable: false,
  })
  uploadContext: UploadContext;

  @Column({ type: 'text', nullable: true })
  description: string | null;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @Column({ type: 'varchar', length: 500, nullable: true })
  thumbnailPath: string | null;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}