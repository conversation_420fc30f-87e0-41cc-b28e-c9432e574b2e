import {
  EAiWebtoonBasicStatus,
  EAiWebtoonCharacterGender,
} from 'src/common/ai-webtoon.enum';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { AdminEntity } from './admin.entity';
import { DefaultEntity } from './default.entity';

@Entity('ai_webtoon_prepare_character')
export class AiWebtoonPrepareCharacterEntity extends DefaultEntity {
  @Column({ type: 'varchar' })
  name: string;

  @Column({
    type: 'enum',
    enum: EAiWebtoonCharacterGender,
    default: EAiWebtoonCharacterGender.MALE,
  })
  gender: EAiWebtoonCharacterGender;

  @Column({ nullable: true, type: 'text' })
  note?: string | null;

  @Column({
    type: 'enum',
    enum: EAiWebtoonBasicStatus,
    default: EAiWebtoonBasicStatus.NOT_GENERATED,
  })
  status: EAiWebtoonBasicStatus;

  @Column({ name: 'admin_id', type: 'int' })
  adminId: number;

  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;
}
