import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { SubscriptionPlanService } from '../services/subscription-plan.service';
import { CreateSubscriptionPlanDto } from '../dto/create-subscription-plan.dto';
import { UpdateSubscriptionPlanDto } from '../dto/update-subscription-plan.dto';
import { JwtAdminAuthGuard } from '../../auth-admin/guards/jwt-admin-auth.guard';
import { AdminRole } from '../../../../common/role.enum';
import { Roles } from '../../../auth/decorators/role.decorator';

@ApiTags('Admin - Subscription Plans')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
@Roles(AdminRole.ADMIN, AdminRole.SUPER_ADMIN)
@Controller('admin/subscription-plans')
export class SubscriptionPlanController {
  constructor(
    private readonly subscriptionPlanService: SubscriptionPlanService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a new subscription plan' })
  @ApiBody({ type: CreateSubscriptionPlanDto })
  @ApiResponse({
    status: 201,
    description: 'Subscription plan created successfully',
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async create(@Body() createSubscriptionPlanDto: CreateSubscriptionPlanDto) {
    return await this.subscriptionPlanService.create(createSubscriptionPlanDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all subscription plans' })
  @ApiResponse({ status: 200, description: 'List of subscription plans' })
  async findAll() {
    return await this.subscriptionPlanService.findAll();
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active subscription plans' })
  @ApiResponse({
    status: 200,
    description: 'List of active subscription plans',
  })
  async findActive() {
    return await this.subscriptionPlanService.findActive();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get subscription plan by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription plan ID' })
  @ApiResponse({ status: 200, description: 'Subscription plan details' })
  @ApiResponse({ status: 404, description: 'Subscription plan not found' })
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return await this.subscriptionPlanService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update subscription plan' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription plan ID' })
  @ApiBody({ type: UpdateSubscriptionPlanDto })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan updated successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription plan not found' })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
  ) {
    return await this.subscriptionPlanService.update(
      id,
      updateSubscriptionPlanDto,
    );
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete subscription plan' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription plan ID' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan deleted successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription plan not found' })
  async remove(@Param('id', ParseIntPipe) id: number) {
    await this.subscriptionPlanService.remove(id);
    return { message: 'Subscription plan deleted successfully' };
  }

  @Patch(':id/activate')
  @ApiOperation({ summary: 'Activate subscription plan' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription plan ID' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan activated successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription plan not found' })
  async activate(@Param('id', ParseIntPipe) id: number) {
    return await this.subscriptionPlanService.activate(id);
  }

  @Patch(':id/deactivate')
  @ApiOperation({ summary: 'Deactivate subscription plan' })
  @ApiParam({ name: 'id', type: 'number', description: 'Subscription plan ID' })
  @ApiResponse({
    status: 200,
    description: 'Subscription plan deactivated successfully',
  })
  @ApiResponse({ status: 404, description: 'Subscription plan not found' })
  async deactivate(@Param('id', ParseIntPipe) id: number) {
    return await this.subscriptionPlanService.deactivate(id);
  }
}
