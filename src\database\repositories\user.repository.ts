import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { UserEntity } from '../../database/entities/user.entity';
import * as bcrypt from 'bcrypt';
@Injectable()
export class UserRepository extends Repository<UserEntity> {
  constructor(private dataSource: DataSource) {
    super(UserEntity, dataSource.createEntityManager());
  }

  async findByEmail(email: string) {
    return this.findOne({ where: { email } });
  }

  async createUser(email: string, password: string, ip?: string) {
    const user = new UserEntity();
    user.email = email;
    user.password = await bcrypt.hash(password, 10);
    user.signUpIp = ip || '';
    return await this.save(user);
  }

  async findByForgotPasswordToken(token: string) {
    return this.findOne({ where: { forgotPasswordToken: token } });
  }
}
