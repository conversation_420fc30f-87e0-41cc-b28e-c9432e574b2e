import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { UserNotificationRecipientEntity } from '../entities/user-notification-recipient.entity';

@Injectable()
export class UserNotificationRecipientRepository extends Repository<UserNotificationRecipientEntity> {
  constructor(
    @InjectRepository(UserNotificationRecipientEntity)
    private repository: Repository<UserNotificationRecipientEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async bulkCreate(userNotificationId: number, userIds: number[]) {
    const recipients = userIds.map((userId) => ({
      userNotificationId,
      userId,
    }));

    return await this.save(recipients);
  }

  async deleteByNotificationId(userNotificationId: number) {
    return await this.delete({ userNotificationId });
  }
}
