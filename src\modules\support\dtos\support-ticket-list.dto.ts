import { ApiProperty } from '@nestjs/swagger';
import {
  TicketStatus,
  TicketPriority,
} from '../../../database/entities/support-ticket.entity';

export class SupportTicketCategoryDto {
  @ApiProperty({ example: 1 })
  id: number;

  @ApiProperty({ example: 'content-access' })
  name: string;

  @ApiProperty({ example: 'Content Access Issues' })
  title: string;
}

export class SupportTicketLastResponseDto {
  @ApiProperty({ example: 456 })
  id: number;

  @ApiProperty({
    example: 'Thank you for contacting us. We are looking into your issue...',
  })
  response: string;

  @ApiProperty({ enum: ['sent', 'auto_reply', 'draft'] })
  status: string;

  @ApiProperty({ example: '2025-01-22T10:30:00.000Z' })
  createdAt: Date;

  @ApiProperty({
    example: {
      id: 1,
      username: 'support_admin',
    },
  })
  admin: {
    id: number;
    username: string;
  };
}

export class SupportTicketListItemDto {
  @ApiProperty({ example: 123 })
  id: number;

  @ApiProperty({ example: 'SUP-2025-0001' })
  ticketNumber: string;

  @ApiProperty({ example: 'Cannot access premium content' })
  subject: string;

  @ApiProperty({ enum: TicketStatus })
  status: TicketStatus;

  @ApiProperty({ enum: TicketPriority })
  priority: TicketPriority;

  @ApiProperty({ type: SupportTicketCategoryDto, nullable: true })
  category: SupportTicketCategoryDto | null;

  @ApiProperty({ example: true })
  hasUnreadResponse: boolean;

  @ApiProperty({ example: '2025-01-22T10:30:00.000Z', nullable: true })
  lastResponseAt: Date | null;

  @ApiProperty({ example: 2 })
  responseCount: number;

  @ApiProperty({ type: SupportTicketLastResponseDto, nullable: true })
  lastResponse: SupportTicketLastResponseDto | null;

  @ApiProperty({ example: '2025-01-22T09:00:00.000Z' })
  createdAt: Date;
}

export class PaginationDto {
  @ApiProperty({ example: 1 })
  page: number;

  @ApiProperty({ example: 10 })
  limit: number;

  @ApiProperty({ example: 1 })
  total: number;

  @ApiProperty({ example: 1 })
  totalPages: number;
}

export class SupportTicketListResponseDto {
  @ApiProperty({ type: [SupportTicketListItemDto] })
  data: SupportTicketListItemDto[];

  @ApiProperty({ type: PaginationDto })
  pagination: PaginationDto;
}
