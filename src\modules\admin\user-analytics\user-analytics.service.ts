import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import {
  UserAnalyticsQueryDto,
  HourlyAnalyticsQueryDto,
  MonthlyAnalyticsQueryDto,
} from './dtos/user-analytics-request.dto';
import {
  UserAnalyticsResponseDto,
  HourlyAnalyticsResponseDto,
  MonthlyAnalyticsResponseDto,
  UserAnalyticsStatsDto,
  HourlyAnalyticsStatsDto,
  MonthlyAnalyticsStatsDto,
} from './dtos/user-analytics-response.dto';

@Injectable()
export class UserAnalyticsService {
  constructor(private dataSource: DataSource) {}

  async getDailyAnalytics(
    filters: UserAnalyticsQueryDto,
  ): Promise<UserAnalyticsResponseDto> {
    const { startDate, endDate } = filters;

    // Set default date range if not provided (last 30 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

    const queryStartDate = startDate ? new Date(startDate) : defaultStartDate;
    const queryEndDate = endDate ? new Date(endDate) : defaultEndDate;

    // Format dates for SQL query (YYYY-MM-DD)
    const formattedStartDate = queryStartDate.toISOString().split('T')[0];
    const formattedEndDate = queryEndDate.toISOString().split('T')[0];

    const query = `
      SELECT 
        ua.date,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.activeUsers ELSE 0 END) as pcActiveUsers,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.activeUsers ELSE 0 END) as mobileActiveUsers,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.newRegistrations ELSE 0 END) as pcNewRegistrations,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.newRegistrations ELSE 0 END) as mobileNewRegistrations,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.deletedAccounts ELSE 0 END) as pcDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.deletedAccounts ELSE 0 END) as mobileDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.loginCount ELSE 0 END) as pcLoginCount,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.loginCount ELSE 0 END) as mobileLoginCount,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.uniqueLogins ELSE 0 END) as pcUniqueLogins,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.uniqueLogins ELSE 0 END) as mobileUniqueLogins,
        SUM(ua.activeUsers) as totalActiveUsers,
        SUM(ua.newRegistrations) as totalNewRegistrations,
        SUM(ua.deletedAccounts) as totalDeletedAccounts,
        SUM(ua.loginCount) as totalLoginCount,
        SUM(ua.uniqueLogins) as totalUniqueLogins
      FROM user_analytics ua
      WHERE ua.date BETWEEN ? AND ?
        AND ua.deletedAt IS NULL
      GROUP BY ua.date
      ORDER BY ua.date DESC
    `;

    const rawResults = await this.dataSource.query(query, [
      formattedStartDate,
      formattedEndDate,
    ]);

    const processedData: UserAnalyticsStatsDto[] = rawResults.map(
      (row: any) => {
        const totalViews = parseInt(row.totalLoginCount) || 0;
        const signup = parseInt(row.totalNewRegistrations) || 0;
        const signupRate = totalViews > 0 ? (signup / totalViews) * 100 : 0;

        return {
          date: row.date.toISOString().split('T')[0],
          totalViews,
          userActive: parseInt(row.totalActiveUsers) || 0,
          signup,
          signupRate: Math.round(signupRate * 100) / 100,
          deleteAccount: parseInt(row.totalDeletedAccounts) || 0,
        };
      },
    );

    return {
      data: processedData,
      total: processedData.length,
      queryInfo: {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
      },
    };
  }

  async getHourlyAnalytics(
    filters: HourlyAnalyticsQueryDto,
  ): Promise<HourlyAnalyticsResponseDto> {
    const { date } = filters;

    const query = `
      SELECT 
        ua.hour,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.activeUsers ELSE 0 END) as pcActiveUsers,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.activeUsers ELSE 0 END) as mobileActiveUsers,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.newRegistrations ELSE 0 END) as pcNewRegistrations,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.newRegistrations ELSE 0 END) as mobileNewRegistrations,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.deletedAccounts ELSE 0 END) as pcDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.deletedAccounts ELSE 0 END) as mobileDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.loginCount ELSE 0 END) as pcLoginCount,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.loginCount ELSE 0 END) as mobileLoginCount,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.uniqueLogins ELSE 0 END) as pcUniqueLogins,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.uniqueLogins ELSE 0 END) as mobileUniqueLogins,
        SUM(ua.activeUsers) as totalActiveUsers,
        SUM(ua.newRegistrations) as totalNewRegistrations,
        SUM(ua.deletedAccounts) as totalDeletedAccounts,
        SUM(ua.loginCount) as totalLoginCount,
        SUM(ua.uniqueLogins) as totalUniqueLogins
      FROM user_analytics ua
      WHERE ua.date = ?
        AND ua.deletedAt IS NULL
      GROUP BY ua.hour
      ORDER BY ua.hour
    `;

    const rawResults = await this.dataSource.query(query, [date]);

    const processedData: HourlyAnalyticsStatsDto[] = rawResults.map(
      (row: any) => {
        const totalViews = parseInt(row.totalLoginCount) || 0;
        const signup = parseInt(row.totalNewRegistrations) || 0;
        const signupRate = totalViews > 0 ? (signup / totalViews) * 100 : 0;

        return {
          hour: `${String(row.hour).padStart(2, '0')}-${String(row.hour + 1).padStart(2, '0')}`,
          totalViews,
          userActive: parseInt(row.totalActiveUsers) || 0,
          signup,
          signupRate: Math.round(signupRate * 100) / 100,
          deleteAccount: parseInt(row.totalDeletedAccounts) || 0,
        };
      },
    );

    return {
      data: processedData,
      total: processedData.length,
      queryInfo: {
        date,
      },
    };
  }

  async getMonthlyAnalytics(
    filters: MonthlyAnalyticsQueryDto,
  ): Promise<MonthlyAnalyticsResponseDto> {
    const { startMonth, endMonth } = filters;

    // Convert YYYY-MM to date range
    const startDate = `${startMonth}-01`;
    const endDate = `${endMonth}-31`; // Last day of end month

    const query = `
      SELECT 
        DATE_FORMAT(ua.date, '%Y-%m') as month,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.activeUsers ELSE 0 END) as pcActiveUsers,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.activeUsers ELSE 0 END) as mobileActiveUsers,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.newRegistrations ELSE 0 END) as pcNewRegistrations,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.newRegistrations ELSE 0 END) as mobileNewRegistrations,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.deletedAccounts ELSE 0 END) as pcDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.deletedAccounts ELSE 0 END) as mobileDeletedAccounts,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.loginCount ELSE 0 END) as pcLoginCount,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.loginCount ELSE 0 END) as mobileLoginCount,
        SUM(CASE WHEN ua.platform = 'pc' THEN ua.uniqueLogins ELSE 0 END) as pcUniqueLogins,
        SUM(CASE WHEN ua.platform = 'mobile' THEN ua.uniqueLogins ELSE 0 END) as mobileUniqueLogins,
        SUM(ua.activeUsers) as totalActiveUsers,
        SUM(ua.newRegistrations) as totalNewRegistrations,
        SUM(ua.deletedAccounts) as totalDeletedAccounts,
        SUM(ua.loginCount) as totalLoginCount,
        SUM(ua.uniqueLogins) as totalUniqueLogins,
        AVG(ua.activeUsers) as avgDailyActiveUsers,
        COUNT(DISTINCT ua.date) as daysInMonth
      FROM user_analytics ua
      WHERE ua.date BETWEEN ? AND ?
        AND ua.deletedAt IS NULL
      GROUP BY DATE_FORMAT(ua.date, '%Y-%m')
      ORDER BY month DESC
    `;

    const rawResults = await this.dataSource.query(query, [startDate, endDate]);

    const processedData: MonthlyAnalyticsStatsDto[] = rawResults.map(
      (row: any) => {
        const totalViews = parseInt(row.totalLoginCount) || 0;
        const signup = parseInt(row.totalNewRegistrations) || 0;
        const signupRate = totalViews > 0 ? (signup / totalViews) * 100 : 0;

        return {
          month: row.month,
          totalViews,
          userActive: parseInt(row.totalActiveUsers) || 0,
          signup,
          signupRate: Math.round(signupRate * 100) / 100,
          deleteAccount: parseInt(row.totalDeletedAccounts) || 0,
          avgDailyActiveUsers: Math.round(
            parseFloat(row.avgDailyActiveUsers) || 0,
          ),
        };
      },
    );

    return {
      data: processedData,
      total: processedData.length,
      queryInfo: {
        startMonth,
        endMonth,
      },
    };
  }
}
