import { Injectable } from '@nestjs/common';

export interface ExchangeRateData {
  currency: string;
  rate: number;
  date: string;
  language?: string;
  region?: string;
}

@Injectable()
export class ExchangeRateService {
  // Mock exchange rates - in production, this would call external API
  private readonly mockRates: Record<string, number> = {
    USD: 1300,
    EUR: 1400,
    JPY: 9.5,
    GBP: 1600,
    CNY: 180,
  };

  /**
   * Get KRW exchange rate for a currency using previous day rate
   * @param currency - Source currency (USD, EUR, etc.)
   * @param language - User language (en, ko, ja, etc.)
   * @param region - User region (US, KR, JP, etc.)
   * @returns Exchange rate to KRW
   */
  getKRWRate(
    currency: string,
    language?: string,
    region?: string,
  ): Promise<number> {
    // In production, this would:
    // 1. Call external API for previous day rates
    // 2. Apply language/region specific rates
    // 3. Cache results in Redis

    const baseRate = this.mockRates[currency.toUpperCase()] || 1300;

    // Apply regional adjustments (mock logic)
    let adjustedRate = baseRate;
    if (language === 'ko' && region === 'KR') {
      adjustedRate = baseRate * 0.995; // Slightly better rate for Korean users
    } else if (language === 'ja' && region === 'JP') {
      adjustedRate = baseRate * 0.998;
    }

    return Promise.resolve(adjustedRate);
  }

  /**
   * Convert amount to KRW using previous day exchange rate
   * @param amount - Amount to convert
   * @param currency - Source currency
   * @param language - User language
   * @param region - User region
   * @returns Amount in KRW
   */
  async convertToKRW(
    amount: number,
    currency: string,
    language?: string,
    region?: string,
  ): Promise<number> {
    if (currency.toUpperCase() === 'KRW') {
      return amount;
    }

    const rate = await this.getKRWRate(currency, language, region);
    return Math.round(amount * rate);
  }

  /**
   * Get exchange rate data with metadata
   * @param currency - Source currency
   * @param language - User language
   * @param region - User region
   * @returns Exchange rate data with metadata
   */
  async getExchangeRateData(
    currency: string,
    language?: string,
    region?: string,
  ): Promise<ExchangeRateData> {
    const rate = await this.getKRWRate(currency, language, region);
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);

    return {
      currency: currency.toUpperCase(),
      rate,
      date: yesterday.toISOString().split('T')[0],
      language,
      region,
    };
  }
}
