import {
  IsString,
  <PERSON><PERSON>ptional,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { FaqStatus } from '../../../../database/entities/faq.entity';

export class CreateFaqDto {
  @ApiProperty({ description: 'FAQ title' })
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiProperty({ description: 'Support category ID', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  categoryId?: number;

  @ApiProperty({ enum: FaqStatus, default: FaqStatus.ACTIVE })
  @IsOptional()
  @IsEnum(FaqStatus)
  status?: FaqStatus;

  @ApiProperty({ description: 'FAQ content (HTML allowed)' })
  @IsString()
  content: string;

  @ApiProperty({ description: 'Display order', default: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  order?: number;
}
