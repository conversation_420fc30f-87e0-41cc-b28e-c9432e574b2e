import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeUserTable1751541973469 implements MigrationInterface {
  name = 'ChangeUserTable1751541973469';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` CHANGE \`agreeUseInfomation\` \`agreeUseInfomation\` tinyint NULL DEFAULT 0`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` CHANGE \`isMatureContent\` \`isMatureContent\` tinyint NULL DEFAULT 0`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` CHANGE \`isMatureContent\` \`isMatureContent\` tinyint NULL DEFAULT '1'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` CHANGE \`agreeUseInfomation\` \`agreeUseInfomation\` tinyint NULL`,
    );
  }
}
