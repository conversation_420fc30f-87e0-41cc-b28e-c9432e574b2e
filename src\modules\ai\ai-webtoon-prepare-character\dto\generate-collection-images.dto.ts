import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';

export class GenerateCollectionImagesDto {
  @ApiProperty()
  @IsInt()
  @Min(1)
  @IsNotEmpty()
  numberOfImages: number;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  loraStrength: number;

  @ApiProperty({ required: false })
  @IsInt()
  @IsOptional()
  @Min(1)
  characterId: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  prompt: string;
}
