import { Column, Entity, ManyToOne, OneToMany } from 'typeorm';
import { ContentEntity } from './content.entity';
import { DefaultEntity } from './default.entity';
import { EpisodeImageEntity } from './episode_image.entity';
import {
  EpisodeBackgroundColors,
  PaymentType,
  PaymentUnitType,
} from '../../common/setting.enum';
import { UserContentEntity } from './user_content.entity';
import { CommonStatus } from '../../common/status.enum';

@Entity('episode')
export class EpisodeEntity extends DefaultEntity {
  @Column({ nullable: true })
  episodeNumber: number;

  @Column({
    type: 'enum',
    enum: CommonStatus,
    default: CommonStatus.ACTIVE,
  })
  status: CommonStatus;

  @Column({ nullable: true })
  episodeString: string;

  @Column({ nullable: false })
  title: string;

  @Column({ nullable: false })
  expectedReleaseDate: Date;

  @Column({ nullable: false })
  thumbnail: string;

  @Column({ default: false })
  nonUserViewing: boolean;

  @Column({ type: 'int', default: 0 })
  viewCount: number;

  @Column({
    type: 'enum',
    enum: PaymentType,
    default: PaymentType.FREE,
  })
  paymentType: PaymentType;

  @Column({
    type: 'enum',
    enum: PaymentUnitType,
    default: PaymentUnitType.KRW,
  })
  paymentUnit: PaymentUnitType;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  paymentAmount: number;

  @Column({
    type: 'enum',
    enum: EpisodeBackgroundColors,
    nullable: true,
    default: EpisodeBackgroundColors.WHITE,
  })
  bgColor: EpisodeBackgroundColors;

  @ManyToOne(() => ContentEntity, (content) => content.episodes)
  content: ContentEntity;

  @OneToMany(() => EpisodeImageEntity, (image) => image.episode)
  images: EpisodeImageEntity[];

  @OneToMany(() => UserContentEntity, (userContent) => userContent.episode)
  userContents: UserContentEntity[];
}
