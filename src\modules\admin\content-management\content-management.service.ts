import { BadRequestException, Injectable } from '@nestjs/common';
import { ContentRepository } from 'src/database/repositories/content.repository';
import { AuthorRepository } from 'src/database/repositories/author.repository';
import { GenreRepository } from 'src/database/repositories/genre.repository';
import { CreateAuthorDto } from './dtos/create-author.dto';
import { CreateGenreDto } from './dtos/create-genre.dto';
import { DeleteGenreDto } from './dtos/delete-genre.dto';
import { CreateContentDto } from './dtos/create-content.dto';
import { SettingRepository } from '../../../database/repositories/setting.repository';
import { ContentSettingType, SettingGroup } from '../../../common/setting.enum';
import { ContentSettingEntity } from '../../../database/entities/content_setting.entity';
import { EpisodeImageEntity } from '../../../database/entities/episode_image.entity';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import { ContentSettingRepository } from '../../../database/repositories/content-setting.repository';
import { CommonService } from '../../../common/common.service';
import { CreateEpisodeDto } from './dtos/create-episode.dto';
import { EpisodeRepository } from '../../../database/repositories/episode.repository';
import { EpisodeImageRepository } from '../../../database/repositories/episode-image.repository';
import { FilterContentDto } from './dtos/filter-content.dto';
import { UpdateContentDto } from './dtos/update-content.dto';
import { UpdateEpisodeDto } from './dtos/update-episode.dto';
import { FilterAuthorDto } from './dtos/filter-author.dto';
import { UpdateAuthorDto } from './dtos/update-author.dto';
import {
  CommonStatus,
  ContentStatus,
  SettingStatus,
  UserContentStatus,
} from '../../../common/status.enum';
import { UserContentRepository } from 'src/database/repositories/user-content.repository';
import {
  ContentValidationService,
  SettingValidationRequest,
} from './services/content-validation.service';
import { EpisodeImageService } from './services/episode-image.service';
import { UserContentEntity } from '../../../database/entities/user_content.entity';
import { GetContentListDto } from '../../content/dtos/get-content-list.dto';
import { MultiRecentlyWatchedContentDto } from '../../content/dtos/add-recently.dto';
import { In, LessThanOrEqual } from 'typeorm';
import { FilterRecentlyDto } from '../../content/dtos/filter-recently.dto';
import { RecentlySortBy } from '../../../common/common.config';
import { GetContentSettingKeyDto } from '../../content/dtos/get-content-setting-key.dto';
import { EpisodeEntity } from '../../../database/entities/episode.entity';

@Injectable()
export class ContentManagementService {
  constructor(
    private readonly contentRepository: ContentRepository,
    private readonly authorRepository: AuthorRepository,
    private readonly genreRepository: GenreRepository,
    private readonly settingRepository: SettingRepository,
    private readonly contentSettingRepository: ContentSettingRepository,
    private readonly commonService: CommonService,
    private readonly episodeRepository: EpisodeRepository,
    private readonly episodeImageRepository: EpisodeImageRepository,
    private readonly userContentRepository: UserContentRepository,
    private readonly contentValidationService: ContentValidationService,
    private readonly episodeImageService: EpisodeImageService,
  ) {}

  async createAuthor(createAuthorDto: CreateAuthorDto) {
    // Batch validation to replace 2 separate queries with 1
    const { nameExists, emailExists } =
      await this.contentValidationService.validateAuthorUniqueness(
        createAuthorDto.name,
        createAuthorDto.email,
      );

    if (nameExists) {
      throw new BadRequestException(MESSAGE_CONFIG.AUTHOR_ALREADY_EXISTS);
    }

    if (emailExists) {
      throw new BadRequestException(MESSAGE_CONFIG.AUTHOR_EMAIL_ALREADY_EXISTS);
    }

    const author = this.authorRepository.create(createAuthorDto);
    return await this.authorRepository.save(author);
  }

  async createGenre(createGenreDto: CreateGenreDto) {
    const genre = this.genreRepository.create(createGenreDto);
    return await this.genreRepository.save(genre);
  }

  async deleteGenre(deleteGenreDto: DeleteGenreDto) {
    return await this.genreRepository.softDelete(deleteGenreDto.id);
  }

  async getAuthors() {
    return await this.authorRepository.find();
  }

  async getAuthorsFilter(filterDto: FilterAuthorDto) {
    const {
      name,
      dateFilter,
      dateFilterStart,
      dateFilterEnd,
      limit = 10,
      page = 1,
    } = filterDto;

    const queryBuilder = this.authorRepository.createQueryBuilder('author');

    if (name) {
      queryBuilder.andWhere('author.name LIKE :name', { name: `%${name}%` });
    }

    if (dateFilter) {
      queryBuilder.andWhere(
        `author.${dateFilter} BETWEEN :dateFilterStart AND :dateFilterEnd`,
        { dateFilterStart, dateFilterEnd },
      );
    }

    const total = await queryBuilder.getCount();

    return {
      authors: await queryBuilder.getMany(),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit) || 1,
    };
  }

  async getGenres() {
    return await this.genreRepository.find();
  }

  async createContent(createContentDto: CreateContentDto) {
    return this.contentRepository.manager.transaction(
      async (transactionalEntityManager) => {
        try {
          const {
            languageId,
            title,
            description,
            genreId,
            authorId,
            classificationIds,
            dateIds,
            expectedReleaseDate,
            authorPaymentFormatId,
            imagePc,
            imageMobile,
            isAdult,
          } = createContentDto;

          // Batch validation - replace 6+ individual queries with 1 parallel execution
          const settingRequests: SettingValidationRequest[] = [
            { ids: [languageId], group: SettingGroup.LANGUAGE, required: true },
            {
              ids: classificationIds,
              group: SettingGroup.CONTENT_CLASSIFICATION,
              required: true,
            },
            { ids: dateIds, group: SettingGroup.CONTENT_DATE, required: true },
          ];

          if (authorPaymentFormatId) {
            settingRequests.push({
              ids: [authorPaymentFormatId],
              group: SettingGroup.AUTHOR_PAYMENT_FORMAT,
              required: false,
            });
          }

          const validationResults =
            await this.contentValidationService.batchValidateEntities({
              author:
                this.contentValidationService.validateAuthorExists(authorId),
              genre: this.contentValidationService.validateGenreExists(genreId),
              settings:
                this.contentValidationService.validateSettingsExist(
                  settingRequests,
                ),
            });

          const { author, genre, settings } = validationResults;

          // Build content settings from validated results
          const contentSettings: { setting: any; type: ContentSettingType }[] =
            [];

          // Language setting (required)
          const languageSettings = settings!.get(SettingGroup.LANGUAGE) || [];
          contentSettings.push({
            setting: languageSettings[0],
            type: ContentSettingType.LANGUAGE,
          });

          // Payment format setting (optional)
          if (authorPaymentFormatId) {
            const paymentFormatSettings =
              settings!.get(SettingGroup.AUTHOR_PAYMENT_FORMAT) || [];
            if (paymentFormatSettings.length > 0) {
              contentSettings.push({
                setting: paymentFormatSettings[0],
                type: ContentSettingType.PAYMENT_FORMAT,
              });
            }
          }

          // Classification settings (required)
          const classificationSettings =
            settings!.get(SettingGroup.CONTENT_CLASSIFICATION) || [];
          classificationSettings.forEach((classification) => {
            contentSettings.push({
              setting: classification,
              type: ContentSettingType.CLASSIFICATION,
            });
          });

          // Date settings (required)
          const dateSettings = settings!.get(SettingGroup.CONTENT_DATE) || [];
          dateSettings.forEach((date) => {
            contentSettings.push({
              setting: date,
              type: ContentSettingType.DATE,
            });
          });

          // Create content with validated entities
          const content = this.contentRepository.create({
            genre: genre!,
            author: author!,
            title,
            description,
            expectedReleaseDate,
            imagePc,
            imageMobile,
            isAdult,
          });

          const savedContent = await transactionalEntityManager.save(content);

          // Bulk create content settings within transaction
          await Promise.all(
            contentSettings.map((contentSetting) => {
              return transactionalEntityManager.save(ContentSettingEntity, {
                content: savedContent,
                setting: contentSetting.setting,
                type: contentSetting.type,
              });
            }),
          );

          return savedContent;
        } catch (error) {
          console.log(error);
          throw new BadRequestException(error.message);
        }
      },
    );
  }

  uploadImage(file: any) {
    return this.commonService.uploadImageToStorage(file, 'contents');
  }

  async getNextEpisodeNumber(contentId: number) {
    const content = await this.episodeRepository.findOne({
      select: ['id', 'episodeNumber'],
      where: { content: { id: contentId } },
      order: {
        episodeNumber: 'DESC',
      },
    });

    if (!content) {
      throw new BadRequestException(MESSAGE_CONFIG.CONTENT_NOT_FOUND);
    }

    return {
      nextEpisodeNumber: content.episodeNumber + 1,
    };
  }

  async createEpisode(createEpisodeDto: CreateEpisodeDto) {
    return this.episodeRepository.manager.transaction(
      async (transactionalEntityManager) => {
        try {
          const { contentId, images, ...episodeData } = createEpisodeDto;

          // Validate content exists using validation service
          const content =
            await this.contentValidationService.validateContentExists(
              contentId,
            );

          const episode = this.episodeRepository.create({
            content,
            ...episodeData,
          });

          const savedEpisode = await transactionalEntityManager.save(episode);

          // Use optimized episode image service for bulk operations within transaction
          if (images && images.length > 0) {
            const episodeImages = images.map((image, index) => ({
              path: image.path,
              episode: savedEpisode,
              order: index,
            }));
            await transactionalEntityManager.save(
              EpisodeImageEntity,
              episodeImages,
            );
          }

          return savedEpisode;
        } catch (error) {
          console.log(error);
          throw new BadRequestException(error.message);
        }
      },
    );
  }

  async filterContent(filterDto: FilterContentDto) {
    const {
      languageId,
      status,
      genre,
      dateId,
      classification,
      dateFeildFilter,
      dateFeildStart,
      dateFeildEnd,
      author,
      title,
      limit = 10,
      page = 1,
    } = filterDto;

    const queryBuilder = this.contentRepository.createQueryBuilder('content');
    queryBuilder.leftJoinAndSelect(
      'content.contentSettings',
      'contentSettings',
    );
    queryBuilder.leftJoinAndSelect('contentSettings.setting', 'setting');
    queryBuilder.leftJoinAndSelect('content.genre', 'genre');
    queryBuilder.leftJoinAndSelect('content.author', 'author');

    // Add episode count as a subquery instead of loading all episodes
    queryBuilder.addSelect(
      '(SELECT COUNT(e.id) FROM episode e WHERE e.contentId = content.id)',
      'episodeCount',
    );

    if (languageId) {
      queryBuilder.andWhere(
        'EXISTS (SELECT 1 FROM content_setting cs JOIN settings s ON cs.settingId = s.id WHERE cs.contentId = content.id AND s.id = :languageId AND cs.type = :languageType)',
        {
          languageId,
          languageType: ContentSettingType.LANGUAGE,
        },
      );
    }

    if (status) {
      queryBuilder.andWhere('content.status = :status', { status });
    }

    if (genre) {
      queryBuilder.andWhere('genre.id = :genre', { genre });
    }

    if (classification) {
      queryBuilder.andWhere(
        'EXISTS (SELECT 1 FROM content_setting cs JOIN settings s ON cs.settingId = s.id WHERE cs.contentId = content.id AND s.id = :classification AND cs.type = :classificationType)',
        {
          classification,
          classificationType: ContentSettingType.CLASSIFICATION,
        },
      );
    }

    if (dateId) {
      queryBuilder.andWhere(
        'EXISTS (SELECT 1 FROM content_setting cs JOIN settings s ON cs.settingId = s.id WHERE cs.contentId = content.id AND s.id = :dateId AND cs.type = :dateType)',
        {
          dateId,
          classificationType: ContentSettingType.DATE,
        },
      );
    }

    if (dateFeildFilter && dateFeildStart && dateFeildEnd) {
      // Validate dateFeildFilter to prevent SQL injection
      const allowedFields = ['createdAt', 'updatedAt', 'expectedReleaseDate'];
      if (!allowedFields.includes(dateFeildFilter)) {
        throw new BadRequestException('Invalid date field for filtering');
      }

      queryBuilder.andWhere(
        `content.${dateFeildFilter} BETWEEN :dateFeildStart AND :dateFeildEnd`,
        { dateFeildStart, dateFeildEnd },
      );
    }

    if (author) {
      queryBuilder.andWhere('author.name LIKE :author', {
        author: `%${author}%`,
      });
    }

    if (title) {
      queryBuilder.andWhere('content.title LIKE :title', {
        title: `%${title}%`,
      });
    }

    // Get total count before applying pagination
    const total = await queryBuilder.getCount();

    // Apply pagination and get results
    const contents = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .getRawAndEntities();

    // filter by setting type language
    const newContents = contents.entities.map((content, index) => {
      return {
        ...content,
        episodeCount: parseInt(contents.raw[index].episodeCount) || 0,
        settingLanguage: content.contentSettings.find(
          (setting) => setting.type === ContentSettingType.LANGUAGE,
        ),
        settingClassification: content.contentSettings.filter(
          (setting) => setting.type === ContentSettingType.CLASSIFICATION,
        ),
        settingDate: content.contentSettings.filter(
          (setting) => setting.type === ContentSettingType.DATE,
        ),
        settingPaymentFormat: content.contentSettings.find(
          (setting) => setting.type === ContentSettingType.PAYMENT_FORMAT,
        ),
      };
    });

    // remove contentSettings from contents
    const contentsWithoutSetting = newContents.map((content) => {
      return {
        ...content,
        contentSettings: [],
      };
    });

    return {
      contents: contentsWithoutSetting,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit) || 1,
    };
  }

  async updateContent(id: number, updateContentDto: UpdateContentDto) {
    try {
      const content = await this.contentRepository.findOne({
        where: { id },
        relations: [
          'contentSettings',
          'contentSettings.setting',
          'genre',
          'author',
        ],
      });

      if (!content) {
        throw new BadRequestException(MESSAGE_CONFIG.CONTENT_NOT_FOUND);
      }

      const {
        languageId,
        title,
        description,
        genreId,
        authorId,
        classificationIds,
        dateIds,
        expectedReleaseDate,
        authorPaymentFormatId,
        imagePc,
        imageMobile,
        isAdult,
        status,
      } = updateContentDto;

      // Update basic fields

      if (status !== undefined) {
        content.status = status;
      }
      if (title !== undefined) {
        content.title = title;
      }
      if (description !== undefined) {
        content.description = description;
      }
      if (expectedReleaseDate !== undefined) {
        content.expectedReleaseDate = expectedReleaseDate;
      }
      if (imagePc !== undefined) {
        content.imagePc = imagePc;
      }
      if (imageMobile !== undefined) {
        content.imageMobile = imageMobile;
      }

      // Update genre if provided
      if (genreId) {
        const genre = await this.genreRepository.findOneById(genreId);
        if (!genre) {
          throw new BadRequestException(MESSAGE_CONFIG.SETTING_NOT_FOUND);
        }
        content.genre = genre;
      }

      // Update isAdult if provided
      if (isAdult !== undefined) {
        content.isAdult = isAdult;
      }

      // Update author if provided
      if (authorId) {
        const author = await this.authorRepository.findOneById(authorId);
        if (!author) {
          throw new BadRequestException(MESSAGE_CONFIG.SETTING_NOT_FOUND);
        }
        content.author = author;
      }

      // Prepare new contentSettings
      const newContentSettings: { setting: any; type: ContentSettingType }[] =
        [];

      // Handle language setting
      if (languageId) {
        const language = await this.settingRepository.findOneByIdAndGroup(
          languageId,
          SettingGroup.LANGUAGE,
        );
        if (!language) {
          throw new BadRequestException(MESSAGE_CONFIG.SETTING_NOT_FOUND);
        }
        newContentSettings.push({
          setting: language,
          type: ContentSettingType.LANGUAGE,
        });
      } else {
        // Keep existing language setting if not updating
        const existingLanguage = content.contentSettings.find(
          (setting) => setting.type === ContentSettingType.LANGUAGE,
        );
        if (existingLanguage) {
          newContentSettings.push({
            setting: existingLanguage.setting,
            type: ContentSettingType.LANGUAGE,
          });
        }
      }

      // Handle payment format setting
      if (authorPaymentFormatId) {
        const paymentFormat = await this.settingRepository.findOneByIdAndGroup(
          authorPaymentFormatId,
          SettingGroup.AUTHOR_PAYMENT_FORMAT,
        );
        if (!paymentFormat) {
          throw new BadRequestException(MESSAGE_CONFIG.SETTING_NOT_FOUND);
        }
        newContentSettings.push({
          setting: paymentFormat,
          type: ContentSettingType.PAYMENT_FORMAT,
        });
      } else {
        // Keep existing payment format setting if not updating
        const existingPaymentFormat = content.contentSettings.find(
          (setting) => setting.type === ContentSettingType.PAYMENT_FORMAT,
        );
        if (existingPaymentFormat) {
          newContentSettings.push({
            setting: existingPaymentFormat.setting,
            type: ContentSettingType.PAYMENT_FORMAT,
          });
        }
      }

      // Handle classification settings
      if (classificationIds && classificationIds.length > 0) {
        const classifications =
          await this.settingRepository.findManyByIdsAndGroup(
            classificationIds,
            SettingGroup.CONTENT_CLASSIFICATION,
          );

        if (
          classifications.length <= 0 ||
          classifications.length !== classificationIds.length
        ) {
          throw new BadRequestException(
            MESSAGE_CONFIG.SETTING_NOT_FOUND.message +
              `: ${SettingGroup.CONTENT_CLASSIFICATION}`,
          );
        }

        classifications.forEach((classification) => {
          newContentSettings.push({
            setting: classification,
            type: ContentSettingType.CLASSIFICATION,
          });
        });
      } else if (classificationIds === undefined) {
        // Keep existing classification settings if not updating
        const existingClassifications = content.contentSettings.filter(
          (setting) => setting.type === ContentSettingType.CLASSIFICATION,
        );
        existingClassifications.forEach((classification) => {
          newContentSettings.push({
            setting: classification.setting,
            type: ContentSettingType.CLASSIFICATION,
          });
        });
      }

      // Handle date settings
      if (dateIds && dateIds.length > 0) {
        const dates = await this.settingRepository.findManyByIdsAndGroup(
          dateIds,
          SettingGroup.CONTENT_DATE,
        );

        if (dates.length <= 0 || dates.length !== dateIds.length) {
          throw new BadRequestException(
            MESSAGE_CONFIG.SETTING_NOT_FOUND.message +
              `: ${SettingGroup.CONTENT_DATE}`,
          );
        }

        dates.forEach((date) => {
          newContentSettings.push({
            setting: date,
            type: ContentSettingType.DATE,
          });
        });
      } else if (dateIds === undefined) {
        // Keep existing date settings if not updating
        const existingDates = content.contentSettings.filter(
          (setting) => setting.type === ContentSettingType.DATE,
        );
        existingDates.forEach((date) => {
          newContentSettings.push({
            setting: date.setting,
            type: ContentSettingType.DATE,
          });
        });
      }

      // Remove old contentSettings
      await this.contentSettingRepository.delete({
        content: { id: content.id },
      });

      // Save updated content
      const updatedContent = await this.contentRepository.save(content);

      // Save new contentSettings
      await Promise.all(
        newContentSettings.map((contentSetting) => {
          return this.contentSettingRepository.save({
            content: updatedContent,
            setting: contentSetting.setting,
            type: contentSetting.type,
          });
        }),
      );

      return updatedContent;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async updateEpisode(id: number, updateEpisodeDto: UpdateEpisodeDto) {
    try {
      const episode = await this.episodeRepository.findOne({
        where: { id },
        relations: ['content', 'images'],
      });

      if (!episode) {
        throw new BadRequestException('Episode not found');
      }

      const {
        contentId,
        episodeNumber,
        episodeString,
        title,
        expectedReleaseDate,
        bgColor,
        thumbnail,
        noneUserViewing,
        images,
        paymentType,
        paymentUnit,
        paymentAmount,
        status,
      } = updateEpisodeDto;

      // Update content if provided using validation service
      if (contentId) {
        const content =
          await this.contentValidationService.validateContentExists(contentId);
        episode.content = content;
      }

      if (status !== undefined) {
        episode.status = status;
      }

      // Update basic fields
      if (episodeNumber !== undefined) {
        episode.episodeNumber = episodeNumber;
      }
      if (episodeString !== undefined) {
        episode.episodeString = episodeString;
      }
      if (title !== undefined) {
        episode.title = title;
      }
      if (expectedReleaseDate !== undefined) {
        episode.expectedReleaseDate = new Date(expectedReleaseDate);
      }
      if (bgColor !== undefined) {
        episode.bgColor = bgColor;
      }
      if (thumbnail !== undefined) {
        episode.thumbnail = thumbnail;
      }
      if (noneUserViewing !== undefined) {
        episode.nonUserViewing = noneUserViewing;
      }
      if (paymentType !== undefined) {
        episode.paymentType = paymentType;
      }
      if (paymentUnit !== undefined) {
        episode.paymentUnit = paymentUnit;
      }
      if (paymentAmount !== undefined) {
        episode.paymentAmount = paymentAmount;
      }

      // Save updated episode
      const updatedEpisode = await this.episodeRepository.save(episode);

      // Update images if provided using optimized episode image service
      if (images && images.length > 0) {
        await this.episodeImageService.handleEpisodeImages(
          updatedEpisode,
          images,
          true,
        );
      }

      return updatedEpisode;
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async getContent(id: number) {
    const content = await this.contentRepository.findOne({
      where: { id },
      relations: [
        'contentSettings',
        'contentSettings.setting',
        'genre',
        'author',
        'episodes',
        'episodes.images',
      ],
      order: {
        expectedReleaseDate: 'DESC',
        episodes: {
          images: {
            order: 'ASC',
          },
        },
      },
    });

    if (!content) {
      throw new BadRequestException(MESSAGE_CONFIG.CONTENT_NOT_FOUND);
    }

    return content;
  }

  async getEpisode(id: number) {
    const episode = await this.episodeRepository.findOne({
      where: { id },
      relations: ['content', 'images'],
    });

    if (!episode) {
      throw new BadRequestException(MESSAGE_CONFIG.EPISODE_NOT_FOUND);
    }

    return episode;
  }

  async getEpisodeByContentId(contentId: number, page: number, limit: number) {
    const episodesQueryBuilder = this.episodeRepository
      .createQueryBuilder('episode')
      .leftJoinAndSelect('episode.content', 'content')
      .leftJoinAndSelect('episode.images', 'images')
      .where('episode.contentId = :contentId', { contentId })
      .orderBy('episode.id', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    console.log(episodesQueryBuilder.getQueryAndParameters());

    const episodes = await episodesQueryBuilder.getMany();

    const total = await episodesQueryBuilder.getCount();

    return {
      episodes,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit) || 1,
    };
  }

  async checkAuthorExist(name: string) {
    const author = await this.authorRepository.findOneBy({ name });
    return author ? true : false;
  }

  async updateAuthor(id: number, updateAuthorDto: UpdateAuthorDto) {
    try {
      const author = await this.authorRepository.findOneBy({ id });

      if (!author) {
        throw new BadRequestException(MESSAGE_CONFIG.AUTHOR_NOT_FOUND);
      }

      const { name, email, bankNumber, memo } = updateAuthorDto;

      // Validate uniqueness if name or email is being updated
      if (name !== undefined || email !== undefined) {
        const nameToCheck = name !== undefined ? name : author.name;
        const emailToCheck = email !== undefined ? email : author.email;

        const { nameExists, emailExists } =
          await this.contentValidationService.validateAuthorUniqueness(
            nameToCheck,
            emailToCheck,
            id, // Exclude current author from check
          );

        if (nameExists && name !== undefined) {
          throw new BadRequestException(MESSAGE_CONFIG.AUTHOR_ALREADY_EXISTS);
        }

        if (emailExists && email !== undefined) {
          throw new BadRequestException(
            MESSAGE_CONFIG.AUTHOR_EMAIL_ALREADY_EXISTS,
          );
        }
      }

      // Update basic fields
      if (name !== undefined) {
        author.name = name;
      }
      if (email !== undefined) {
        author.email = email;
      }
      if (bankNumber !== undefined) {
        author.bankNumber = bankNumber;
      }
      if (memo !== undefined) {
        author.memo = memo;
      }

      return await this.authorRepository.save(author);
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async getAuthor(id: number) {
    const author = await this.authorRepository.findOne({
      where: { id },
      relations: [
        'contents',
        'contents.genre',
        'contents.contentSettings',
        'contents.contentSettings.setting',
      ],
    });
    if (!author) {
      throw new BadRequestException(MESSAGE_CONFIG.AUTHOR_NOT_FOUND);
    }
    return author;
  }

  async getContentDetailForUser(id: number, userId: number) {
    const content = await this.contentRepository.findOne({
      where: {
        id,
        status: ContentStatus.ACTIVE,
        expectedReleaseDate: LessThanOrEqual(new Date().toISOString()),
        episodes: {
          expectedReleaseDate: LessThanOrEqual(new Date()),
          status: CommonStatus.ACTIVE,
        },
        contentSettings: {
          setting: {
            group: In([
              SettingGroup.CONTENT_CLASSIFICATION,
              SettingGroup.CONTENT_DATE,
            ]),
          },
        },
      },
      relations: [
        'genre',
        'author',
        'episodes',
        'contentSettings',
        'contentSettings.setting',
        'userContents',
        'userContents.episode',
      ],
    });

    if (!content) {
      throw new BadRequestException(MESSAGE_CONFIG.CONTENT_NOT_FOUND);
    }

    let recentlyContents: UserContentEntity[] | null = null;
    if (userId) {
      recentlyContents = await this.userContentRepository.find({
        where: { content: { id }, user: { id: userId } },
        relations: ['content', 'user'],
      });
    }

    content.viewCount++;
    await this.contentRepository.updateViewCount(id);

    return {
      content,
      recentlyContents,
    };
  }

  async getDailyCalendar(
    limit: number,
    page: number,
    group: SettingGroup,
    contentSettingType: ContentSettingType,
    userId?: number,
    settingKey?: string,
    genreId?: number,
  ) {
    // First, get all content that matches our criteria with pagination
    const contentQueryBuilder = this.contentRepository
      .createQueryBuilder('content')
      .leftJoinAndSelect('content.contentSettings', 'contentSettings')
      .leftJoinAndSelect('contentSettings.setting', 'setting')
      .leftJoinAndSelect('content.episodes', 'episodes')
      .leftJoinAndSelect('content.genre', 'genre')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(episode.id)', 'countEpisode')
          .from(EpisodeEntity, 'episode')
          .where('episode.contentId = content.id');
      }, 'countEpisode');

    contentQueryBuilder.andWhere('content.status = :status', {
      status: ContentStatus.ACTIVE,
    });

    if (genreId) {
      contentQueryBuilder.andWhere('content.genre = :genreId', { genreId });
    }

    if (userId) {
      contentQueryBuilder
        .leftJoinAndSelect('content.userContents', 'userContents')
        .leftJoinAndSelect('userContents.episode', 'episode');
      contentQueryBuilder.andWhere('userContents.user = :userId', { userId });
      contentQueryBuilder.addSelect((subQuery) => {
        return subQuery
          .select('COUNT(userContent.id)', 'countUserContent')
          .from(UserContentEntity, 'userContent')
          .where('userContent.contentId = content.id');
      }, 'countUserContent');
    }
    contentQueryBuilder
      .andWhere('content.status = :status', { status: ContentStatus.ACTIVE })
      .andWhere('content.expectedReleaseDate < :currentDate', {
        currentDate: new Date().toISOString(),
      })

      .andWhere('setting.group = :group', { group })
      .andWhere('setting.status = :settingStatus', {
        settingStatus: SettingStatus.ACTIVE,
      });
    if (settingKey) {
      contentQueryBuilder.andWhere('setting.key = :key', { key: settingKey });
    }
    contentQueryBuilder
      .andWhere('contentSettings.type = :type', {
        type: contentSettingType,
      })
      .andWhere('content.expectedReleaseDate < :currentDate', {
        currentDate: new Date().toISOString(),
      })
      .orderBy('content.expectedReleaseDate', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    const contents = await contentQueryBuilder.getMany();

    // Get total count for pagination
    const totalQueryBuilder = this.contentRepository
      .createQueryBuilder('content')
      .leftJoin('content.contentSettings', 'contentSettings')
      .leftJoin('contentSettings.setting', 'setting')
      .andWhere('content.status = :status', { status: ContentStatus.ACTIVE })
      .andWhere('content.expectedReleaseDate < :currentDate', {
        currentDate: new Date().toISOString(),
      })
      .andWhere('setting.group = :group', { group })
      .andWhere('setting.status = :settingStatus', {
        settingStatus: SettingStatus.ACTIVE,
      })
      .andWhere('contentSettings.type = :type', {
        type: contentSettingType,
      });

    const total = await totalQueryBuilder.getCount();

    // Group contents by date setting
    const groupedContents = contents.reduce((acc, content) => {
      const dateSettings = content.contentSettings.filter(
        (cs) => cs.type === contentSettingType,
      );

      dateSettings.forEach((dateSetting) => {
        const settingKey = dateSetting.setting.key;
        if (!acc[settingKey]) {
          acc[settingKey] = {
            id: dateSetting.setting.id,
            key: dateSetting.setting.key,
            value: dateSetting.setting.value,
            contents: [],
            episodeCount: content.episodes ? content.episodes.length : 0,
          };
        }

        // Check if content already exists in this setting to avoid duplicates
        const existingContent = acc[settingKey].contents.find(
          (c) => c.id === content.id,
        );

        if (!existingContent) {
          acc[settingKey].contents.push({
            id: content.id,
            description: content.description,
            title: content.title,
            imagePc: content.imagePc,
            imageMobile: content.imageMobile,
            genre: content.genre,
            expectedReleaseDate: content.expectedReleaseDate,
            episodeCount: content.episodes ? content.episodes.length : 0,
            userContents: content?.userContents || [],
            countView: content.viewCount,
            countEpisode: content.episodes ? content.episodes.length : 0,
            countUserContent: content?.userContents
              ? content?.userContents.length
              : 0,
            contentSettings: content.contentSettings,
          });
        }
      });

      return acc;
    }, {});

    return {
      contents: Object.values(groupedContents),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit) || 1,
    };
  }

  async getEpisodeForUser(id: number, userId?: number) {
    const episode = await this.episodeRepository.findOne({
      where: { id },
      relations: ['content', 'images'],
    });

    if (!episode) {
      throw new BadRequestException(MESSAGE_CONFIG.EPISODE_NOT_FOUND);
    }

    if (!episode.nonUserViewing && !userId) {
      throw new BadRequestException(
        MESSAGE_CONFIG.EPISODE_NOT_FOR_NON_USER_VIEWING,
      );
    }

    episode.viewCount++;
    await this.episodeRepository.updateViewCount(id);

    if (userId) {
      await this.saveEpisodeView(episode.content.id, id, userId);
    }

    return episode;
  }

  async saveEpisodeView(contentId: number, episodeId: number, userId: number) {
    const userContent: UserContentEntity | null =
      await this.userContentRepository.findOne({
        where: {
          content: { id: contentId },
          user: { id: userId },
          episode: { id: episodeId },
        },
      });

    // if userContent exists, update episodeId
    if (userContent) {
      const episode = await this.episodeRepository.findOneBy({ id: episodeId });
      if (!episode) {
        throw new BadRequestException(MESSAGE_CONFIG.EPISODE_NOT_FOUND);
      }
      userContent.episode = episode;
      userContent.lastUpdateTime = Date.now();

      await this.userContentRepository.save(userContent);
      return;
    }

    await this.userContentRepository.save({
      episode: { id: episodeId },
      user: { id: userId },
      content: { id: contentId },
      lastUpdateTime: Date.now(),
    });
  }

  async addRecentlyWatchedContents(
    body: MultiRecentlyWatchedContentDto,
    userId: number,
  ) {
    try {
      await Promise.all(
        body.contentIds.map((c) => {
          return this.saveEpisodeView(c.contentId, c.episodeId || 0, userId);
        }),
      );

      return {
        message: 'Success',
      };
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async deleteRecentlyWatchedContents(
    body: MultiRecentlyWatchedContentDto,
    userId: number,
  ) {
    try {
      await this.userContentRepository.softDelete({
        content: { id: In(body.contentIds.map((c) => c.contentId)) },
        user: { id: userId },
      });

      return {
        message: 'Success',
      };
    } catch (error) {
      console.log(error);
      throw new BadRequestException(error.message);
    }
  }

  async getRecentlyWatchedContents(
    userId: number,
    filterRecentlyDto: FilterRecentlyDto,
  ) {
    const { sortBy, sortOrder, limit, page, searchTitle } = filterRecentlyDto;

    // First, get the latest userContent for each content
    const latestUserContentSubquery = this.userContentRepository
      .createQueryBuilder('uc')
      .select('uc.contentId', 'contentId')
      .addSelect('MAX(uc.id)', 'latestUserContentId')
      .addSelect('MAX(uc.createdAt)', 'latestCreatedAt')
      .addSelect('MAX(uc.lastUpdateTime)', 'latestUpdateTime')
      .where('uc.userId = :userId', { userId })
      .andWhere('uc.status = :status', { status: UserContentStatus.ACTIVE })
      .groupBy('uc.contentId');

    // Main query to get content details with counts
    const userContentsQuery = this.userContentRepository
      .createQueryBuilder('userContent')
      .innerJoin(
        `(${latestUserContentSubquery.getQuery()})`,
        'latest',
        'userContent.id = latest.latestUserContentId',
      )
      .leftJoinAndSelect('userContent.content', 'content')
      .leftJoinAndSelect('content.genre', 'genre')
      .leftJoinAndSelect('content.author', 'author')
      .leftJoinAndSelect('userContent.episode', 'episode')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(episode.id)', 'countEpisode')
          .from(EpisodeEntity, 'episode')
          .where('episode.contentId = content.id');
      }, 'countEpisode')
      .addSelect((subQuery) => {
        return subQuery
          .select('COUNT(userContent.id)', 'countUserContent')
          .from(UserContentEntity, 'userContent')
          .where('userContent.contentId = content.id')
          .andWhere('userContent.userId = :userId', { userId });
      }, 'countUserContent')
      .setParameters(latestUserContentSubquery.getParameters());

    if (searchTitle) {
      userContentsQuery.andWhere('content.title LIKE :title', {
        title: `%${searchTitle}%`,
      });
    }

    if (sortBy === RecentlySortBy.LATEST) {
      userContentsQuery.orderBy('userContent.createdAt', 'DESC');
    } else if (sortBy === RecentlySortBy.UPDATED) {
      userContentsQuery.orderBy('userContent.lastUpdateTime', 'DESC');
    } else if (sortBy === RecentlySortBy.TITLE) {
      userContentsQuery.orderBy(
        'content.title',
        sortOrder.toUpperCase() as 'ASC' | 'DESC',
      );
    }

    // Get total count for pagination
    const totalQuery = this.userContentRepository
      .createQueryBuilder('userContent')
      .innerJoin(
        `(${latestUserContentSubquery.getQuery()})`,
        'latest',
        'userContent.id = latest.latestUserContentId',
      )
      .leftJoin('userContent.content', 'content')
      .setParameters(latestUserContentSubquery.getParameters());

    if (searchTitle) {
      totalQuery.andWhere('content.title LIKE :title', {
        title: `%${searchTitle}%`,
      });
    }

    const total = await totalQuery.getCount();

    // Get results with pagination
    const userContentsResult = await userContentsQuery
      .skip((page - 1) * limit)
      .take(limit)
      .getRawAndEntities();

    // Merge raw data (counts) with entities
    const contentsWithCounts = userContentsResult.entities.map(
      (userContent, index) => {
        const rawData = userContentsResult.raw[index];
        return {
          ...userContent,
          content: {
            ...userContent.content,
            countEpisode: parseInt(rawData.countEpisode) || 0,
            countUserContent: parseInt(rawData.countUserContent) || 0,
          },
        };
      },
    );

    return {
      userContents: contentsWithCounts,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit) || 1,
    };
  }

  async getContentListForUser(query: GetContentListDto) {
    const contentQueryBuilder = await this.filterContent(query);
    return contentQueryBuilder;
  }

  async getMainBanner() {
    const banner = await this.contentRepository.find({
      where: { status: ContentStatus.ACTIVE },
      relations: ['genre', 'author'],
    });

    return banner;
  }

  async getContentClassification(
    getContentSettingKeyDto: GetContentSettingKeyDto,
    userId?: number,
  ) {
    const { limit, page, settingKey, genreId } = getContentSettingKeyDto;
    const contents = await this.getDailyCalendar(
      limit,
      page,
      SettingGroup.CONTENT_CLASSIFICATION,
      ContentSettingType.CLASSIFICATION,
      userId,
      settingKey,
      genreId,
    );

    return contents;
  }
}
