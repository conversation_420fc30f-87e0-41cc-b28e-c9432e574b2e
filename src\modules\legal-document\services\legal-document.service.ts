import { Injectable, NotFoundException } from '@nestjs/common';
import { LegalDocumentRepository } from '../../../database/repositories/legal-document.repository';
import {
  LegalDocumentEntity,
  LegalDocumentType,
  LegalDocumentStatus,
} from '../../../database/entities/legal-document.entity';
import { UserLegalDocumentFilterDto } from '../dtos/user-legal-document-filter.dto';
import { AnalyticsQueueService } from '../../analytics/services/analytics-queue.service';

@Injectable()
export class LegalDocumentService {
  constructor(
    private legalDocumentRepository: LegalDocumentRepository,
    private analyticsQueueService: AnalyticsQueueService,
  ) {}

  async getLegalDocumentsList(
    filterDto: UserLegalDocumentFilterDto,
  ): Promise<LegalDocumentEntity[]> {
    if (filterDto.type) {
      return await this.legalDocumentRepository.findActiveByType(
        filterDto.type,
      );
    }

    // Get all active documents if no type filter
    const termsOfUse = await this.legalDocumentRepository.findActiveByType(
      LegalDocumentType.TERMS_OF_USE,
    );
    const privacyPolicy = await this.legalDocumentRepository.findActiveByType(
      LegalDocumentType.PRIVACY_POLICY,
    );

    return [...termsOfUse, ...privacyPolicy].sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime(),
    );
  }

  async getLegalDocumentDetail(
    identifier: string,
    userId?: number,
  ): Promise<LegalDocumentEntity> {
    const document =
      await this.legalDocumentRepository.findActiveByName(identifier);

    if (!document) {
      throw new NotFoundException('Legal document not found');
    }

    // Increment view count atomically
    await this.legalDocumentRepository.incrementViewCount(document.id);

    // Track analytics if user is authenticated
    if (userId) {
      try {
        await this.analyticsQueueService.trackUserActivity({
          userId,
          activityType: 'legal_document_view',
          platform: 'web',
          timestamp: Date.now(),
          metadata: {
            documentId: document.id,
            documentType: document.type,
            documentName: document.name,
          },
        });
      } catch (error) {
        // Analytics failure should not block the main flow
        console.error('Failed to track legal document view analytics:', error);
      }
    }

    return document;
  }

  async getLegalDocumentDetailById(
    id: number,
    userId?: number,
  ): Promise<LegalDocumentEntity> {
    const document = await this.legalDocumentRepository.findOne({
      where: { id, status: LegalDocumentStatus.ACTIVE },
    });

    if (!document) {
      throw new NotFoundException('Legal document not found');
    }

    // Increment view count atomically
    await this.legalDocumentRepository.incrementViewCount(document.id);

    // Track analytics if user is authenticated
    if (userId) {
      try {
        await this.analyticsQueueService.trackUserActivity({
          userId,
          activityType: 'legal_document_view',
          platform: 'web',
          timestamp: Date.now(),
          metadata: {
            documentId: document.id,
            documentType: document.type,
            documentName: document.name,
          },
        });
      } catch (error) {
        // Analytics failure should not block the main flow
        console.error('Failed to track legal document view analytics:', error);
      }
    }

    return document;
  }

  async getLatestVersionByType(
    type: LegalDocumentType,
  ): Promise<LegalDocumentEntity | null> {
    return await this.legalDocumentRepository.findLatestVersion(type);
  }
}
