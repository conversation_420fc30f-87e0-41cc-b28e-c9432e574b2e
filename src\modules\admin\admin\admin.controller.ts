import {
  Controller,
  Get,
  Param,
  Put,
  Body,
  Delete,
  UseGuards,
  Query,
} from '@nestjs/common';
import { AdminService } from './admin.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UpdateAdminDto } from './dtos/update-admin.dto';
import { RoleGuard } from '../auth-admin/guards/role.guard';
import { AdminRole } from '../../../common/role.enum';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
import { Roles } from '../../auth/decorators/role.decorator';
import { SearchAdminDto } from '../auth-admin/dtos/search.dto';
import { UpdateAdminMenuDto } from './dtos/update-admin-menu.dto';

@Controller('admin/admin')
@ApiTags('Admin Management')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard, RoleGuard)
@Roles(AdminRole.SUPER_ADMIN, AdminRole.ADMIN)
export class AdminController {
  constructor(private adminService: AdminService) {}

  @Get()
  @ApiOperation({ summary: 'Get all admins' })
  async getAdmins(@Query() searchDto: SearchAdminDto) {
    return await this.adminService.getAdmins(searchDto);
  }

  @Get('menu/:adminId')
  @ApiOperation({ summary: 'Get all menus' })
  async getMenuByAdminId(@Param('adminId') adminId: string) {
    return await this.adminService.getMenuByAdminId(Number(adminId));
  }

  @Put('menu/:adminId')
  @ApiOperation({ summary: 'Update menus' })
  async updateMenuByAdminId(
    @Param('adminId') adminId: number,
    @Body() menus: UpdateAdminMenuDto,
  ) {
    return await this.adminService.updateMenuByAdminId(adminId, menus.menus);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get an admin by ID' })
  async getAdmin(@Param('id') id: string) {
    return await this.adminService.getAdmin(Number(id));
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update an admin by ID' })
  async updateAdmin(
    @Param('id') id: string,
    @Body() updateAdminDto: UpdateAdminDto,
  ) {
    return await this.adminService.updateAdmin(Number(id), updateAdminDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete an admin by ID' })
  async deleteAdmin(@Param('id') id: string) {
    return await this.adminService.deleteAdmin(Number(id));
  }
}
