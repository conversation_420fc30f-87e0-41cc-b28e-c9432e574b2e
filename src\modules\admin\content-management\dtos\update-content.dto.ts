import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
} from 'class-validator';

import { IsString } from 'class-validator';
import { ContentStatus } from '../../../../common/status.enum';

export class UpdateContentDto {
  @ApiProperty({
    description: 'Language ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  languageId: number;

  @ApiProperty({
    description: 'Is Adult Content',
    example: true,
  })
  @IsBoolean()
  @Type(() => Boolean)
  @IsOptional()
  isAdult: boolean;

  @ApiProperty({
    description: 'Title',
    example: 'Title',
  })
  @IsString()
  @IsOptional()
  title: string;

  @ApiProperty({
    description: 'Description',
    example: 'Description',
  })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({
    description: 'Genre ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  genreId: number;

  @ApiProperty({
    description: 'Author ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  authorId: number;

  @ApiProperty({
    description: 'Classification IDs',
    example: [1, 2],
  })
  @IsArray()
  @IsOptional()
  classificationIds: number[];

  @ApiProperty({
    description: 'Date IDs',
    example: [1, 2],
  })
  @IsArray()
  @IsOptional()
  dateIds: number[];

  @ApiProperty({
    description: 'Expected Release Date',
    example: '2025-01-01',
  })
  @IsString()
  @IsOptional()
  expectedReleaseDate: string;

  @ApiProperty({
    description: 'Author Payment Format ID',
    example: 1,
  })
  @IsNumber()
  @IsOptional()
  authorPaymentFormatId: number;

  @ApiProperty({
    description: 'Image PC',
    example: 'https://example.com/image.png',
  })
  @IsString()
  @IsOptional()
  imagePc: string;

  @ApiProperty({
    description: 'Image Mobile',
    example: 'https://example.com/image.png',
  })
  @IsString()
  @IsOptional()
  imageMobile: string;

  @ApiProperty({
    description: 'Status of the content',
    enum: ContentStatus,
    example: ContentStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(ContentStatus)
  @Type(() => String)
  status: ContentStatus;
}
