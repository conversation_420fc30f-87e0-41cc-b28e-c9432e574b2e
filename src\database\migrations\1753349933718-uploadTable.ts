import { MigrationInterface, QueryRunner } from 'typeorm';

export class UploadTable1753349933718 implements MigrationInterface {
  name = 'UploadTable1753349933718';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`uploads\` (\`id\` int NOT NULL AUTO_INCREMENT, \`uploaderId\` int NOT NULL, \`uploaderType\` enum ('user', 'admin') NOT NULL, \`originalName\` varchar(255) NOT NULL, \`fileName\` varchar(255) NOT NULL, \`filePath\` varchar(500) NOT NULL, \`fileSize\` bigint NOT NULL, \`mimeType\` varchar(100) NOT NULL, \`fileType\` enum ('image', 'document') NOT NULL, \`uploadContext\` enum ('profile', 'content', 'support', 'settings', 'general', 'bulk') NOT NULL, \`description\` text NULL, \`metadata\` json NULL, \`thumbnailPath\` varchar(500) NULL, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, INDEX \`IDX_569fe786a844a2e381f3280cdc\` (\`uploaderId\`), INDEX \`IDX_2a15f5c5840e6ab92eccc00ce2\` (\`createdAt\`), INDEX \`IDX_423ad10962dde54efae000f440\` (\`fileType\`), INDEX \`IDX_f0868e66622b8d2b9512dce034\` (\`uploadContext\`), INDEX \`IDX_462f58b29517e2b96c1ac75018\` (\`uploaderId\`, \`uploaderType\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_462f58b29517e2b96c1ac75018\` ON \`uploads\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_f0868e66622b8d2b9512dce034\` ON \`uploads\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_423ad10962dde54efae000f440\` ON \`uploads\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_2a15f5c5840e6ab92eccc00ce2\` ON \`uploads\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_569fe786a844a2e381f3280cdc\` ON \`uploads\``,
    );
    await queryRunner.query(`DROP TABLE \`uploads\``);
  }
}
