import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { SystemNotificationRepository } from '../../../database/repositories/system-notification.repository';
import { UserNotificationRepository } from '../../../database/repositories/user-notification.repository';
import { UserNotificationRecipientRepository } from '../../../database/repositories/user-notification-recipient.repository';
import { NotificationViewRepository } from '../../../database/repositories/notification-view.repository';
import {
  SystemNotificationEntity,
  NotificationStatus,
} from '../../../database/entities/system-notification.entity';
import { UserNotificationEntity } from '../../../database/entities/user-notification.entity';
import { NotificationType } from '../../../database/entities/notification-view.entity';
import { CreateSystemNotificationDto } from '../dtos/create-system-notification.dto';
import { CreateUserNotificationDto } from '../dtos/create-user-notification.dto';
import { FullUpdateSystemNotificationDto } from '../dtos/full-update-system-notification.dto';
import { FullUpdateUserNotificationDto } from '../dtos/full-update-user-notification.dto';
import { UserRepository } from '../../../database/repositories/user.repository';
import { In } from 'typeorm';
import * as DOMPurify from 'isomorphic-dompurify';

@Injectable()
export class NotificationService {
  constructor(
    private systemNotificationRepository: SystemNotificationRepository,
    private userNotificationRepository: UserNotificationRepository,
    private userNotificationRecipientRepository: UserNotificationRecipientRepository,
    private notificationViewRepository: NotificationViewRepository,
    private userRepository: UserRepository,
  ) {}

  // System Notification Methods
  async createSystemNotification(
    createDto: CreateSystemNotificationDto,
  ): Promise<SystemNotificationEntity> {
    const notification = this.systemNotificationRepository.create({
      name: createDto.name || 'root',
      title: createDto.title,
      status: createDto.status || NotificationStatus.ACTIVE,
      content: DOMPurify.sanitize(createDto.content),
    });

    return await this.systemNotificationRepository.save(notification);
  }

  async updateSystemNotificationStatus(
    id: number,
    status: NotificationStatus,
  ): Promise<SystemNotificationEntity> {
    const notification = await this.systemNotificationRepository.findOne({
      where: { id },
    });
    if (!notification) {
      throw new NotFoundException('System notification not found');
    }

    notification.status = status;
    return await this.systemNotificationRepository.save(notification);
  }

  async updateSystemNotification(
    id: number,
    updateDto: FullUpdateSystemNotificationDto,
  ): Promise<SystemNotificationEntity> {
    const notification = await this.systemNotificationRepository.findOne({
      where: { id },
    });
    if (!notification) {
      throw new NotFoundException('System notification not found');
    }

    // Update only provided fields
    if (updateDto.name !== undefined) {
      notification.name = updateDto.name;
    }
    if (updateDto.title !== undefined) {
      notification.title = updateDto.title;
    }
    if (updateDto.content !== undefined) {
      notification.content = DOMPurify.sanitize(updateDto.content);
    }
    if (updateDto.status !== undefined) {
      notification.status = updateDto.status;
    }

    return await this.systemNotificationRepository.save(notification);
  }

  async getSystemNotificationsWithViewCount(
    page: number = 1,
    limit: number = 10,
    status?: NotificationStatus,
  ) {
    const [notifications, total] =
      await this.systemNotificationRepository.findWithViewCount(
        page,
        limit,
        status,
      );

    return {
      data: notifications,
      total,
      page,
      limit,
      totalPages: Math.ceil(Number(total) / limit),
    };
  }

  async deleteSystemNotifications(ids: number[]): Promise<void> {
    await this.systemNotificationRepository.softDelete({ id: In(ids) });
  }

  async getSystemNotificationDetailForAdmin(
    id: number,
  ): Promise<SystemNotificationEntity> {
    const notification = await this.systemNotificationRepository.findOne({
      where: { id },
    });

    if (!notification) {
      throw new NotFoundException('System notification not found');
    }

    return notification;
  }

  // User Notification Methods
  async createUserNotification(
    createDto: CreateUserNotificationDto,
  ): Promise<UserNotificationEntity> {
    // Validate user IDs exist
    const users = await this.userRepository.find({
      where: { id: In(createDto.userIds) },
    });

    if (users.length !== createDto.userIds.length) {
      throw new BadRequestException('Some user IDs are invalid');
    }

    // Validate time range
    if (createDto.startTime >= createDto.endTime) {
      throw new BadRequestException('Start time must be before end time');
    }

    const notification = this.userNotificationRepository.create({
      name: createDto.name || 'root',
      title: createDto.title,
      startTime: createDto.startTime,
      endTime: createDto.endTime,
      status: createDto.status || NotificationStatus.ACTIVE,
      content: DOMPurify.sanitize(createDto.content),
    });

    const savedNotification =
      await this.userNotificationRepository.save(notification);

    // Create recipients
    await this.userNotificationRecipientRepository.bulkCreate(
      savedNotification.id,
      createDto.userIds,
    );

    return savedNotification;
  }

  async updateUserNotificationStatus(
    id: number,
    status: NotificationStatus,
  ): Promise<UserNotificationEntity> {
    const notification = await this.userNotificationRepository.findOne({
      where: { id },
    });
    if (!notification) {
      throw new NotFoundException('User notification not found');
    }

    notification.status = status;
    return await this.userNotificationRepository.save(notification);
  }

  async updateUserNotification(
    id: number,
    updateDto: FullUpdateUserNotificationDto,
  ): Promise<UserNotificationEntity> {
    const notification = await this.userNotificationRepository.findOne({
      where: { id },
    });
    if (!notification) {
      throw new NotFoundException('User notification not found');
    }

    // Validate time range if both startTime and endTime are provided
    const newStartTime = updateDto.startTime ?? notification.startTime;
    const newEndTime = updateDto.endTime ?? notification.endTime;

    if (newStartTime >= newEndTime) {
      throw new BadRequestException('Start time must be before end time');
    }

    // Update only provided fields
    if (updateDto.name !== undefined) {
      notification.name = updateDto.name;
    }
    if (updateDto.title !== undefined) {
      notification.title = updateDto.title;
    }
    if (updateDto.startTime !== undefined) {
      notification.startTime = updateDto.startTime;
    }
    if (updateDto.endTime !== undefined) {
      notification.endTime = updateDto.endTime;
    }
    if (updateDto.content !== undefined) {
      notification.content = DOMPurify.sanitize(updateDto.content);
    }
    if (updateDto.status !== undefined) {
      notification.status = updateDto.status;
    }

    const savedNotification =
      await this.userNotificationRepository.save(notification);

    // Update recipients if provided
    if (updateDto.userIds && updateDto.userIds.length > 0) {
      // Validate user IDs exist
      const users = await this.userRepository.find({
        where: { id: In(updateDto.userIds) },
      });

      if (users.length !== updateDto.userIds.length) {
        throw new BadRequestException('Some user IDs are invalid');
      }

      // Delete existing recipients
      await this.userNotificationRecipientRepository.deleteByNotificationId(id);

      // Create new recipients
      await this.userNotificationRecipientRepository.bulkCreate(
        id,
        updateDto.userIds,
      );
    }

    return savedNotification;
  }

  async getUserNotificationsWithViewCount(
    page: number = 1,
    limit: number = 10,
    status?: NotificationStatus,
  ) {
    const [notifications, total] =
      await this.userNotificationRepository.findWithViewCount(
        page,
        limit,
        status,
      );

    return {
      data: notifications,
      total,
      page,
      limit,
      totalPages: Math.ceil(Number(total) / limit),
    };
  }

  async deleteUserNotifications(ids: number[]): Promise<void> {
    // Delete recipients first
    await Promise.all(
      ids.map((id) =>
        this.userNotificationRecipientRepository.deleteByNotificationId(id),
      ),
    );

    // Then soft delete notifications
    await this.userNotificationRepository.softDelete({ id: In(ids) });
  }

  async getUserNotificationDetailForAdmin(
    id: number,
  ): Promise<UserNotificationEntity> {
    const notification = await this.userNotificationRepository.findOne({
      where: { id },
      relations: ['recipients', 'recipients.user'],
    });

    if (!notification) {
      throw new NotFoundException('User notification not found');
    }

    return notification;
  }

  // User-facing methods
  async getActiveSystemNotifications(page: number = 1, limit: number = 10) {
    const [notifications, total] =
      await this.systemNotificationRepository.findActiveNotifications(
        page,
        limit,
      );

    return {
      data: notifications,
      total,
      page,
      limit,
      totalPages: Math.ceil(Number(total) / limit),
    };
  }

  async getSystemNotificationDetail(id: number, userId?: number) {
    const notification = await this.systemNotificationRepository.findOne({
      where: { id, status: NotificationStatus.ACTIVE },
    });

    if (!notification) {
      throw new NotFoundException('System notification not found');
    }

    // Mark as viewed if user is provided
    if (userId) {
      await this.notificationViewRepository.markAsViewed(
        userId,
        NotificationType.SYSTEM,
        id,
      );
    }

    return notification;
  }

  async getUserNotificationsForUser(
    userId: number,
    page: number = 1,
    limit: number = 10,
  ) {
    const [notifications, total] =
      await this.userNotificationRepository.findActiveNotificationsForUser(
        userId,
        page,
        limit,
      );

    return {
      data: notifications,
      total,
      page,
      limit,
      totalPages: Math.ceil(Number(total) / limit),
    };
  }

  async getUserNotificationDetail(id: number, userId: number) {
    const notification =
      await this.userNotificationRepository.findActiveNotificationById(
        id,
        userId,
      );

    if (!notification) {
      throw new NotFoundException(
        'User notification not found or not accessible',
      );
    }

    // Mark as viewed
    await this.notificationViewRepository.markAsViewed(
      userId,
      NotificationType.USER,
      id,
    );

    return notification;
  }
}
