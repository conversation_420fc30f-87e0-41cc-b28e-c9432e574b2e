import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiLetteringLanguageEntity } from '../entities/ai-lettering-language.entity';

@Injectable()
export class AiLetteringLanguageRepository extends Repository<AiLetteringLanguageEntity> {
  constructor(private dataSource: DataSource) {
    super(AiLetteringLanguageEntity, dataSource.createEntityManager());
  }
}
