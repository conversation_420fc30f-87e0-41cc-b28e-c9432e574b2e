import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  UserNotificationEntity,
  NotificationStatus,
} from '../entities/user-notification.entity';

@Injectable()
export class UserNotificationRepository extends Repository<UserNotificationEntity> {
  constructor(
    @InjectRepository(UserNotificationEntity)
    private repository: Repository<UserNotificationEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findActiveNotificationsForUser(
    userId: number,
    page: number = 1,
    limit: number = 10,
  ) {
    const currentTime = Date.now();

    const queryBuilder = this.createQueryBuilder('un')
      .innerJoin(
        'user_notification_recipient',
        'unr',
        'unr.user_notification_id = un.id',
      )
      .where('unr.user_id = :userId', { userId })
      .andWhere('un.status = :status', { status: NotificationStatus.ACTIVE })
      .andWhere('un.startTime <= :currentTime', { currentTime })
      .andWhere('un.endTime >= :currentTime', { currentTime })
      .andWhere('un.deletedAt IS NULL')
      .orderBy('un.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    return await queryBuilder.getManyAndCount();
  }

  async findWithViewCount(page: number = 1, limit: number = 10, status?: NotificationStatus) {
    const queryBuilder = this.createQueryBuilder('un')
      .leftJoin(
        'notification_view',
        'nv',
        'nv.notificationId = un.id AND nv.notificationType = :type',
        { type: 'user' },
      )
      .select([
        'un.id',
        'un.name',
        'un.title',
        'un.status',
        'un.content',
        'un.startTime',
        'un.endTime',
        'un.createdAt',
        'un.updatedAt',
      ])
      .addSelect('COUNT(nv.id)', 'viewCount')
      .where('un.deletedAt IS NULL')
      .groupBy('un.id')
      .orderBy('un.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    // Add status filter if provided
    if (status) {
      queryBuilder.andWhere('un.status = :status', { status });
    }

    // Build count query with same filters
    const countQueryBuilder = this.createQueryBuilder('un')
      .where('un.deletedAt IS NULL');
    
    if (status) {
      countQueryBuilder.andWhere('un.status = :status', { status });
    }

    const [data, total] = await Promise.all([
      queryBuilder.getRawAndEntities(),
      countQueryBuilder.getCount(),
    ]);

    const results = data.entities.map((notification, index) => ({
      ...notification,
      viewCount: parseInt(data.raw[index].viewCount) || 0,
    }));

    return [results, total];
  }

  async findActiveNotificationById(id: number, userId: number) {
    const currentTime = Date.now();

    return await this.createQueryBuilder('un')
      .innerJoin(
        'user_notification_recipient',
        'unr',
        'unr.user_notification_id = un.id',
      )
      .where('un.id = :id', { id })
      .andWhere('unr.user_id = :userId', { userId })
      .andWhere('un.status = :status', { status: NotificationStatus.ACTIVE })
      .andWhere('un.startTime <= :currentTime', { currentTime })
      .andWhere('un.endTime >= :currentTime', { currentTime })
      .andWhere('un.deletedAt IS NULL')
      .getOne();
  }
}
