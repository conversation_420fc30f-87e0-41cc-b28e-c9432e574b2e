import { EAiApiGenStateManagementType } from 'src/common/ai-webtoon.enum';
import { Column, Entity } from 'typeorm';
import { DefaultEntity } from './default.entity';

@Entity('ai_api_gen_state_management')
export class AiApiGenStateManagementEntity extends DefaultEntity {
  @Column({ name: 'name', unique: true })
  name: string;

  @Column({ name: 'field_in_env' })
  fieldInEnv: string;

  @Column({
    name: 'enum',
    enum: EAiApiGenStateManagementType,
    default: EAiApiGenStateManagementType.GEN_IMAGE_FLUX,
  })
  type: EAiApiGenStateManagementType;

  @Column({ default: false })
  status: boolean;

  @Column({ name: 'is_maintenance', default: true })
  isMaintenance: boolean;

  @Column({ name: 'start_time', type: 'datetime', nullable: true })
  startTime?: Date | null;
}
