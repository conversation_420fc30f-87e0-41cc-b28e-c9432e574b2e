import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Min,
  ValidateNested,
} from 'class-validator';
import { EAiLetteringClassificationType } from 'src/common/ai-webtoon.enum';

class CreateAiLetteringItemDto {
  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  @IsNotEmpty()
  extension: string;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsUrl()
  @IsNotEmpty()
  url: string;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsUrl()
  @IsNotEmpty()
  thumbnailUrl: string;
}

export class CreateMultipleAiLetteringDto {
  @ApiProperty({
    example: [
      {
        name: 'Lettering 1',
        extension: 'png',
        url: 'https://example.com/lettering1.png',
        thumbnailUrl: 'https://example.com/lettering1_thumb.png',
      },
    ],
  })
  @ValidateNested({ each: true })
  @Type(() => CreateAiLetteringItemDto)
  @IsArray()
  data: CreateAiLetteringItemDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  aiLetteringLanguageId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  aiLetteringTypeId: number;

  @ApiProperty({ enum: EAiLetteringClassificationType })
  @IsEnum(EAiLetteringClassificationType)
  @IsNotEmpty()
  classification: EAiLetteringClassificationType;
}
