import {
  IsString,
  IsNotEmpty,
  IsArray,
  ValidateNested,
  IsNumber,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
export class CreateSettingMetadataDto {
  @ApiProperty({
    description: 'The key of the setting metadata',
    example: 'key',
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: 'The value of the setting metadata',
    example: 'value',
  })
  @IsString()
  @IsNotEmpty()
  value: string;
}

export class CreateMultipleSettingMetadataDto {
  @IsArray()
  @IsNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => CreateSettingMetadataDto)
  @ApiProperty({
    description: 'The metadata of the setting',
    example: [
      { key: 'categories1', value: 'value' },
      { key: 'categories2', value: 'value2' },
      { key: 'url', value: 'value3' },
    ],
  })
  metadata: CreateSettingMetadataDto[];

  @ApiProperty({
    description: 'The id of the setting',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  settingId: number;
}
