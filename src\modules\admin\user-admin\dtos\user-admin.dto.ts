import { IsString, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import {
  DateFieldFilter,
  InputFieldFilter,
  Platform,
  UserType,
} from '../../../../common/common.config';

export class UserAdminDto {
  @ApiProperty({
    description: 'User type',
    required: false,
    example: UserType.GENERAL,
    enum: UserType,
  })
  @IsString()
  @IsOptional()
  userType: string;

  @ApiProperty({
    description: 'Platform',
    required: false,
    example: 'pc',
    enum: Platform,
  })
  @IsString()
  @IsOptional()
  platform: string;

  @ApiProperty({
    description: 'Date field filter',
    required: false,
    example: 'createdAt',
    enum: DateFieldFilter,
  })
  @IsString()
  @IsOptional()
  dateFieldFilter: string;

  @ApiProperty({
    description: 'Date field filter start',
    required: false,
    example: '2021-01-01',
    format: 'date-time',
  })
  @IsString()
  @IsOptional()
  dateFieldFilterStart: string;

  @ApiProperty({
    description: 'Date field filter end',
    required: false,
    example: '2025-10-01',
    format: 'date',
    type: 'string',
  })
  @IsString()
  @IsOptional()
  dateFieldFilterEnd: string;

  @ApiProperty({
    description: 'Input field filter (id, email, ref, signUpIp, recentIp)',
    required: false,
    example: InputFieldFilter.EMAIL,
    enum: InputFieldFilter,
  })
  @IsString()
  @IsOptional()
  inputFieldFilter: string;

  @ApiProperty({
    description: 'Search',
    required: false,
    example: 'test',
  })
  @IsString()
  @IsOptional()
  search: string;

  @ApiProperty({
    description: 'Search by email (shortcut for inputFieldFilter=email)',
    required: false,
    example: '<EMAIL>',
  })
  @IsString()
  @IsOptional()
  email: string;

  @ApiProperty({
    description: 'Page',
    required: false,
    example: 1,
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  page: number;

  @ApiProperty({
    description: 'Limit',
    required: false,
    example: 10,
  })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  limit: number;
}
