import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { SupportTicketEntity } from './support-ticket.entity';
import { AdminEntity } from './admin.entity';

export enum ResponseStatus {
  UNANSWERED = 'unanswered', // Chưa trả lời
  PENDING = 'pending', // Đang chuẩn bị trả lời
  PROCESSING = 'processing', // Đang xử lý
  COMPLETE = 'complete', // Đã hoàn thành
  COMPLETE_ADD = 'complete_add', // <PERSON><PERSON><PERSON> thành bổ sung
  AUTOMATED_REPLY = 'automatedReply', // Trả lời tự động
}

@Entity('support_response')
export class SupportResponseEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'ticket_id' })
  ticketId: number;

  @Column({ name: 'admin_id' })
  adminId: number;

  @Column('text')
  response: string; // Nội dung trả lời

  @Column('json', { nullable: true })
  attachments: string[]; // File đính kèm

  @Column({
    type: 'enum',
    enum: ResponseStatus,
    default: ResponseStatus.PENDING,
  })
  status: ResponseStatus;

  @Column({ name: 'response_completed_time', nullable: true })
  responseCompletedTime: Date; // Thời điểm hoàn thành response

  @Column({ name: 'is_internal', default: false })
  isInternal: boolean; // Ghi chú nội bộ (không hiện cho user)

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => SupportTicketEntity, (ticket) => ticket.responses)
  @JoinColumn({ name: 'ticket_id' })
  ticket: SupportTicketEntity;

  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;
}
