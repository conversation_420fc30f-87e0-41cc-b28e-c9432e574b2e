import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeTableNameDisplay1749630044149 implements MigrationInterface {
  name = 'ChangeTableNameDisplay1749630044149';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`displays\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`type\` enum ('main_banner', 'genre_display', 'popular_display', 'partner_display') NOT NULL, \`status\` enum ('active', 'inactive') NOT NULL, \`order\` int NOT NULL DEFAULT '1', \`contentId\` int NULL, \`genreId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`displays\` ADD CONSTRAINT \`FK_55852b6c7b3fbf3f635fd63584d\` FOREIGN KEY (\`contentId\`) REFERENCES \`content\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`displays\` ADD CONSTRAINT \`FK_71a80c6ea1b809dc5d8ace51761\` FOREIGN KEY (\`genreId\`) REFERENCES \`genre\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`displays\` DROP FOREIGN KEY \`FK_71a80c6ea1b809dc5d8ace51761\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`displays\` DROP FOREIGN KEY \`FK_55852b6c7b3fbf3f635fd63584d\``,
    );
    await queryRunner.query(`DROP TABLE \`displays\``);
  }
}
