import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull, LessThan } from 'typeorm';
import {
  ExchangeRateEntity,
  SupportedCurrency,
  SupportedLanguage,
  SupportedRegion,
} from '../../../database/entities/exchange-rate.entity';

export interface ExchangeRateData {
  currency: string;
  rate: number;
  date: string;
  language?: string;
  region?: string;
}

@Injectable()
export class ExchangeRateDbService {
  private readonly logger = new Logger(ExchangeRateDbService.name);

  constructor(
    @InjectRepository(ExchangeRateEntity)
    private readonly exchangeRateRepository: Repository<ExchangeRateEntity>,
  ) {}

  /**
   * Find exchange rate with fallback logic
   * Priority: exact match (currency + language + region) -> global rate (currency only)
   */
  async findExchangeRate(
    currency: string,
    language?: string,
    region?: string,
    date?: Date,
  ): Promise<ExchangeRateEntity | null> {
    const targetDate = date || new Date();

    // Try exact match first
    let rate = await this.exchangeRateRepository.findOne({
      where: {
        currency: currency.toUpperCase() as SupportedCurrency,
        language: language as SupportedLanguage,
        region: region as SupportedRegion,
        date: targetDate,
        status: 'active',
      },
    });

    // Fallback to global rate
    if (!rate) {
      rate = await this.exchangeRateRepository.findOne({
        where: {
          currency: currency.toUpperCase() as SupportedCurrency,
          language: IsNull(),
          region: IsNull(),
          date: targetDate,
          status: 'active',
        },
      });
    }

    return rate;
  }

  /**
   * Get KRW exchange rate with fallback
   */
  async getKRWRate(
    currency: string,
    language?: string,
    region?: string,
    date?: Date,
  ): Promise<number> {
    if (currency.toUpperCase() === 'KRW') {
      return 1;
    }

    const rate = await this.findExchangeRate(currency, language, region, date);
    if (!rate) {
      this.logger.warn(
        `No exchange rate found for ${currency} on ${date?.toISOString().split('T')[0]}, using fallback`,
      );
      return this.getFallbackRate(currency);
    }

    return Number(rate.rateToKRW);
  }

  /**
   * Convert amount to KRW
   */
  async convertToKRW(
    amount: number,
    currency: string,
    language?: string,
    region?: string,
    date?: Date,
  ): Promise<number> {
    const rate = await this.getKRWRate(currency, language, region, date);
    return Math.round(amount * rate);
  }

  /**
   * Get exchange rate data with metadata
   */
  async getExchangeRateData(
    currency: string,
    language?: string,
    region?: string,
    date?: Date,
  ): Promise<ExchangeRateData> {
    const targetDate = date || new Date();
    const rate = await this.getKRWRate(currency, language, region, targetDate);

    return {
      currency: currency.toUpperCase(),
      rate,
      date: targetDate.toISOString().split('T')[0],
      language,
      region,
    };
  }

  /**
   * Upsert exchange rates in batch
   */
  async upsertExchangeRates(
    rates: Partial<ExchangeRateEntity>[],
  ): Promise<void> {
    try {
      await this.exchangeRateRepository.upsert(rates, [
        'date',
        'currency',
        'language',
        'region',
      ]);
      this.logger.log(`Upserted ${rates.length} exchange rates`);
    } catch (error) {
      this.logger.error('Failed to upsert exchange rates:', error);
      throw error;
    }
  }

  /**
   * Fallback rates when no DB rate is found
   */
  private getFallbackRate(currency: string): number {
    const fallbackRates: Record<string, number> = {
      USD: 1300,
      EUR: 1420,
      JPY: 9.5,
      GBP: 1650,
      CNY: 180,
    };

    return fallbackRates[currency.toUpperCase()] || 1300;
  }

  /**
   * Get latest exchange rates for all currencies
   */
  async getLatestRates(date?: Date): Promise<ExchangeRateEntity[]> {
    const targetDate = date || new Date();

    return this.exchangeRateRepository.find({
      where: {
        date: targetDate,
        status: 'active',
      },
      order: {
        currency: 'ASC',
        language: 'ASC',
        region: 'ASC',
      },
    });
  }

  /**
   * Deactivate old exchange rates
   */
  async deactivateOldRates(olderThanDays: number = 7): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

    await this.exchangeRateRepository.update(
      {
        date: LessThan(cutoffDate),
        status: 'active',
      },
      {
        status: 'expired',
      },
    );

    this.logger.log(
      `Deactivated exchange rates older than ${olderThanDays} days`,
    );
  }
}
