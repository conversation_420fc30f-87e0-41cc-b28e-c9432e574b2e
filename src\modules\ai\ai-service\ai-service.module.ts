import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';
import { AiApiGenStateManagementModule } from '../ai-api-gen-state-management/ai-api-gen-state-management.module';
import { NotificationGoogleModule } from 'src/modules/notification-google/notification-google.module';
import { AiService } from './ai-service.service';

@Module({
  imports: [
    HttpModule,
    AiApiGenStateManagementModule,
    NotificationGoogleModule,
  ],
  providers: [AiService],
  exports: [AiService],
})
export class AiServiceModule {}
