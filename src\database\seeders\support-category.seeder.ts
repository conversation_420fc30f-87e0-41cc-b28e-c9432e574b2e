import { DataSource } from 'typeorm';
import { SupportCategoryEntity } from '../entities/support-category.entity';
import { Seeder } from '@jorgebodega/typeorm-seeding';

export class SupportCategorySeeder extends Seeder {
  async run(dataSource: DataSource): Promise<void> {
    const categoryRepository = dataSource.getRepository(SupportCategoryEntity);

    // Check if categories already exist
    const existingCount = await categoryRepository.count();
    if (existingCount > 0) {
      console.log('Support categories already seeded');
      return;
    }

    const defaultCategories = [
      {
        name: 'payment-billing',
        title: 'Payment & Billing',
        description:
          'Issues related to payments, subscriptions, billing, and refunds',
        order: 1,
        expectedResponseTime: 12, // 12 hours for payment issues
        expectedResolutionTime: 48, // 48 hours for payment resolution
      },
      {
        name: 'content-access',
        title: 'Content Access Issues',
        description:
          'Problems accessing premium content, episodes, or subscription benefits',
        order: 2,
        expectedResponseTime: 24, // 24 hours for content issues
        expectedResolutionTime: 72, // 72 hours for content resolution
      },
      {
        name: 'account-management',
        title: 'Account Management',
        description:
          'Account settings, profile management, password reset, and account recovery',
        order: 3,
        expectedResponseTime: 24, // 24 hours for account issues
        expectedResolutionTime: 48, // 48 hours for account resolution
      },
      {
        name: 'technical-issues',
        title: 'Technical Issues',
        description:
          'App crashes, loading problems, performance issues, and technical bugs',
        order: 4,
        expectedResponseTime: 24, // 24 hours for technical issues
        expectedResolutionTime: 96, // 96 hours for technical resolution
      },
      {
        name: 'content-reporting',
        title: 'Content Reporting',
        description:
          'Report inappropriate content, copyright issues, or content quality problems',
        order: 5,
        expectedResponseTime: 48, // 48 hours for content reporting
        expectedResolutionTime: 168, // 1 week for content review
      },
      {
        name: 'feature-requests',
        title: 'Feature Requests',
        description:
          'Suggestions for new features, improvements, or platform enhancements',
        order: 6,
        expectedResponseTime: 72, // 72 hours for feature requests
        expectedResolutionTime: 720, // 30 days for feature evaluation
      },
      {
        name: 'general-inquiry',
        title: 'General Inquiry',
        description:
          'General questions, how-to guides, and other non-technical inquiries',
        order: 7,
        expectedResponseTime: 24, // 24 hours for general inquiries
        expectedResolutionTime: 72, // 72 hours for general resolution
      },
    ];

    try {
      const categories = categoryRepository.create(defaultCategories);
      await categoryRepository.save(categories);
      console.log(
        `✅ Successfully seeded ${categories.length} support categories`,
      );
    } catch (error) {
      console.error('❌ Error seeding support categories:', error.message);
      throw error;
    }
  }
}
