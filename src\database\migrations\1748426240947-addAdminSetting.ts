import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdminSetting1748426240947 implements MigrationInterface {
  name = 'AddAdminSetting1748426240947';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`settings\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`key\` varchar(255) NOT NULL, \`value\` varchar(255) NULL, \`group\` varchar(255) NOT NULL, \`order\` int NOT NULL DEFAULT '0', PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: Query<PERSON>unner): Promise<void> {
    await queryRunner.query(`DROP TABLE \`settings\``);
  }
}
