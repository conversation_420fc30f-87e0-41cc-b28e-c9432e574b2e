import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiWebtoonCutChapterService } from './ai-webtoon-cut-chapter.service';
import { AiWebtoonCutChapterController } from './ai-webtoon-cut-chapter.controller';
import { AiApiGenStateManagementModule } from '../ai-api-gen-state-management/ai-api-gen-state-management.module';
// import { SocketModule } from '../socket/socket.module';
import { AiWebtoonOrderGenerateCutChapterRepository } from 'src/database/repositories/ai-webtoon-order-generate-cut-chapter.repository';
import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { AiServiceModule } from '../ai-service/ai-service.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonChapterRepository,
      AiWebtoonCutChapterRepository,
      AiWebtoonSceneChapterRepository,
      AiWebtoonOrderGenerateCutChapterRepository,
    ]),
    AiServiceModule,
    AiApiGenStateManagementModule,
    // SocketModule,
  ],
  controllers: [AiWebtoonCutChapterController],
  providers: [AiWebtoonCutChapterService],
  exports: [AiWebtoonCutChapterService],
})
export class AiWebtoonCutChapterModule {}
