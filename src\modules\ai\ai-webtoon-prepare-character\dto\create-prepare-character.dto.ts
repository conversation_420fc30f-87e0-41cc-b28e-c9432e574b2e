import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsString } from 'class-validator';
import { EAiWebtoonCharacterGender } from 'src/common/ai-webtoon.enum';

export class CreateAiWebtoonPrepareCharacterDto {
  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  name: string;

  @ApiProperty({ enum: EAiWebtoonCharacterGender })
  @IsEnum(EAiWebtoonCharacterGender)
  gender: EAiWebtoonCharacterGender;
}
