import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Param,
  Body,
  Query,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { SupportCategoryAdminService } from './services/support-category-admin.service';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
import { MenuPermissions } from '../auth-admin/decorators/menu-permission.decorator';
import { MenuPermissionGuard } from '../auth-admin/guards/menu-permission.guard';

@ApiTags('Admin - Support Categories')
@Controller('admin/support/categories')
@UseGuards(JwtAdminAuthGuard, MenuPermissionGuard)
@MenuPermissions('support-management')
@ApiBearerAuth()
export class SupportCategoryAdminController {
  constructor(
    private readonly categoryAdminService: SupportCategoryAdminService,
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get Support Categories',
    description: 'Get paginated list of support categories with statistics',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 10 })
  @ApiResponse({
    status: 200,
    description: 'Categories retrieved successfully',
    example: {
      data: [
        {
          id: 1,
          name: 'payment-billing',
          title: 'Payment & Billing',
          description: 'Issues related to payments, subscriptions, and billing',
          status: 'active',
          order: 1,
          expectedResponseTime: 24,
          expectedResolutionTime: 72,
          totalTickets: 45,
          activeTickets: 12,
          createdAt: '2025-01-20T10:00:00.000Z',
          updatedAt: '2025-01-22T10:00:00.000Z',
        },
      ],
      pagination: { page: 1, limit: 10, total: 1, totalPages: 1 },
    },
  })
  async getCategories(
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 10,
  ) {
    return await this.categoryAdminService.getCategories(page, limit);
  }

  @Post()
  @ApiOperation({
    summary: 'Create Support Category',
    description: 'Create a new support category',
  })
  @ApiResponse({
    status: 201,
    description: 'Category created successfully',
    example: {
      success: true,
      category: {
        id: 4,
        name: 'technical-support',
        title: 'Technical Support',
        description: 'Technical issues with the platform',
        status: 'active',
        order: 4,
        expectedResponseTime: 24,
        expectedResolutionTime: 48,
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Category name already exists',
    example: {
      error: 'support/category-exists',
      message: 'Category with this name already exists',
    },
  })
  async createCategory(@Body() createDto: any) {
    // Will create proper DTO
    return await this.categoryAdminService.createCategory(createDto);
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update Support Category',
    description: 'Update an existing support category',
  })
  @ApiResponse({
    status: 200,
    description: 'Category updated successfully',
    example: {
      success: true,
      category: {
        id: 1,
        name: 'payment-billing',
        title: 'Payment & Billing Issues',
        description: 'Updated description...',
        status: 'active',
        order: 1,
        expectedResponseTime: 12,
        expectedResolutionTime: 48,
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Category not found' })
  async updateCategory(
    @Param('id', ParseIntPipe) categoryId: number,
    @Body() updateDto: any, // Will create proper DTO
  ) {
    return await this.categoryAdminService.updateCategory(
      categoryId,
      updateDto,
    );
  }

  @Delete()
  @ApiOperation({
    summary: 'Delete Support Categories',
    description: 'Soft delete multiple support categories',
  })
  @ApiResponse({
    status: 200,
    description: 'Categories deleted successfully',
    example: {
      success: true,
      deletedCount: 2,
      deletedIds: [3, 4],
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Categories have active tickets',
    example: {
      error: 'support/categories-in-use',
      message: 'Cannot delete categories with active tickets',
      categoriesInUse: [3],
    },
  })
  async deleteCategories(@Body() deleteDto: { ids: number[] }) {
    return await this.categoryAdminService.deleteCategories(deleteDto.ids);
  }

  @Get('active')
  @ApiOperation({
    summary: 'Get Active Support Categories',
    description: 'Get list of active support categories for dropdowns',
  })
  @ApiResponse({
    status: 200,
    description: 'Active categories retrieved successfully',
    example: {
      data: [
        {
          id: 1,
          name: 'payment-billing',
          title: 'Payment & Billing',
          description: 'Issues related to payments and subscriptions',
        },
        {
          id: 2,
          name: 'content-access',
          title: 'Content Access Issues',
          description: 'Problems accessing premium content or episodes',
        },
      ],
    },
  })
  async getActiveCategories() {
    return await this.categoryAdminService.getActiveCategories();
  }
}
