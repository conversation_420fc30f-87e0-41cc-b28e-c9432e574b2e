import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { SettingRepository } from 'src/database/repositories/setting.repository';
import { CreateSettingDto } from './dtos/create-setting.dto';
import { UpdateMultipleSettingsDto } from './dtos/update-setting.dto';
import { In } from 'typeorm';
import {
  SettingGroup,
  SettingGroupLimit,
  SettingGroupUploadAccess,
  LanguageKey,
} from '../../../common/setting.enum';
import { CreateMultipleSettingMetadataDto } from './dtos/create-metadata.dto';
import { SettingMetadataRepository } from 'src/database/repositories/setting-metadat.repository';
import { SettingStatus } from '../../../common/status.enum';
import { UploadImageDto } from './dtos/upload-image.dto';
import { CommonService } from 'src/common/common.service';
import {
  CreateLanguageDto,
  CreateMultipleLanguageDto,
} from './dtos/create-language.dto';
import {
  UpdateLanguageDto,
  UpdateMultipleLanguageDto,
} from './dtos/update-language.dto';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import { LanguageFilterDto } from './dtos/language-filter.dto';
import { CreateClassificationDto } from './dtos/create-classification.dto';
import { CreateDateDto } from './dtos/create-date.dto';
import { DeleteMultipleLanguageDto } from './dtos/delete-language.dto';
import { UpdateAutoReplyDto } from './dtos/update-auto-reply.dto';
@Injectable()
export class SettingService {
  constructor(
    private readonly settingRepository: SettingRepository,
    private readonly settingMetadataRepository: SettingMetadataRepository,
    private readonly commonService: CommonService,
  ) {}

  async createSetting(createSettingDto: CreateSettingDto) {
    if (!this.checkSettingGroup(createSettingDto.group)) {
      throw new BadRequestException(
        `Invalid setting group ${createSettingDto.group}`,
      );
    }

    if (SettingGroupLimit[createSettingDto.group]) {
      const count = await this.settingRepository.count({
        where: { group: createSettingDto.group },
      });
      if (count && SettingGroupLimit[createSettingDto.group]) {
        throw new BadRequestException(
          `Setting group ${createSettingDto.group} has reached the limit`,
        );
      }
    }
    return await this.createSettingData(
      createSettingDto,
      createSettingDto.group,
    );
  }

  async getSettings() {
    return await this.settingRepository.find({
      order: {
        order: 'ASC',
      },
    });
  }

  async updateMultipleSettings(updateSettingDto: UpdateMultipleSettingsDto) {
    try {
      const settingsToUpdate = updateSettingDto.settings;
      const keys = settingsToUpdate.map((setting) => setting.key);

      const existingSettings = await this.settingRepository.find({
        where: { key: In(keys) },
      });

      if (!existingSettings.length) {
        throw new NotFoundException('Settings not found');
      }

      await Promise.all(
        settingsToUpdate.map((settingToUpdate) => {
          const existingSetting = existingSettings.find(
            (setting) => setting.key === settingToUpdate.key,
          );
          if (existingSetting) {
            return this.settingRepository.update(existingSetting.id, {
              status: settingToUpdate.status as SettingStatus,
            });
          }
        }),
      );
      return {
        message: 'Settings updated successfully',
        data: settingsToUpdate,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async getSettingByGroup(group: SettingGroup) {
    // check setting group in setting group enum
    if (!this.checkSettingGroup(group)) {
      throw new BadRequestException(`Invalid setting group ${group}`);
    }

    if (SettingGroupLimit[group]) {
      return await this.settingRepository.findOne({
        where: { group },
        relations: ['metadata'],
      });
    }
    return await this.settingRepository.find({
      where: { group },
      order: {
        order: 'ASC',
      },
      relations: ['metadata'],
    });
  }

  checkSettingGroup(group: SettingGroup) {
    return Object.values(SettingGroup).includes(group);
  }

  async createMultipleSettingMetadata(
    createSettingMetadataDto: CreateMultipleSettingMetadataDto,
  ) {
    try {
      const metadata = createSettingMetadataDto.metadata;
      const setting = await this.settingRepository.findOne({
        where: { id: createSettingMetadataDto.settingId },
      });
      if (!setting) {
        throw new NotFoundException('Setting not found');
      }

      const metadataToSave = metadata.map((item) => ({
        key: item.key.toLowerCase().replace(/ /g, '_'),
        value: item.value,
        setting,
      }));
      metadataToSave.push({
        key: 'view',
        value: '0',
        setting,
      });
      metadataToSave.push({
        key: 'status',
        value: 'active',
        setting,
      });
      return await this.settingMetadataRepository.save(metadataToSave);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async uploadImage(file: any, body: UploadImageDto) {
    try {
      let setting = await this.settingRepository.findOne({
        where: { group: body.group },
      });

      if (!setting) {
        setting = await this.settingRepository.save({
          group: body.group,
          key: body.group,
          value: '',
          order: 0,
        });
      }

      // check group upload access
      if (!SettingGroupUploadAccess[body.group]) {
        throw new BadRequestException('Group upload access not allowed');
      }
      // create new setting

      // upload image
      const image = this.commonService.uploadImageToStorage(file, 'settings');

      //remove old image
      if (setting.value) {
        this.commonService.removeImageFromStorage(setting.value);
      }

      // update setting value
      await this.settingRepository.update(setting.id, {
        value: image,
      });

      return image;
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async deleteImageAccessPage() {
    try {
      const setting = await this.settingRepository.findOne({
        where: { group: SettingGroup.IMAGE_ACCESS_PAGE },
      });
      if (!setting) {
        throw new NotFoundException('Setting not found');
      }
      this.commonService.removeImageFromStorage(setting.value);
      await this.settingRepository.update(setting.id, {
        value: '',
      });
      return {
        message: 'Image access page deleted successfully',
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async createMultipleLanguage(
    createMultipleLanguageDto: CreateMultipleLanguageDto,
  ) {
    const languages = createMultipleLanguageDto.languages;
    await Promise.all(
      languages.map(async (language) => {
        await this.createLanguage(language);
      }),
    );
  }

  async createLanguage(createLanguageDto: CreateLanguageDto) {
    const metadatas = [
      {
        key: LanguageKey.LANGUAGE,
        value: createLanguageDto.language,
      },
      {
        key: LanguageKey.SUBSCRIPTION_STATUS,
        value: createLanguageDto.subscriptionStatus.toLowerCase(),
      },
      {
        key: LanguageKey.COINS_STATUS,
        value: createLanguageDto.coinsStatus.toLowerCase(),
      },
      {
        key: LanguageKey.CURRENCY,
        value: createLanguageDto.currency.toLowerCase(),
      },
    ];

    const checkLangName = await this.settingRepository.findOne({
      where: {
        group: SettingGroup.LANGUAGE,
        value: createLanguageDto.language,
      },
    });

    if (checkLangName) {
      throw new BadRequestException(MESSAGE_CONFIG.LANGUAGE_ALREADY_EXISTS);
    }

    let setting = await this.settingRepository.findOne({
      where: {
        group: SettingGroup.LANGUAGE,
        key:
          SettingGroup.LANGUAGE +
          '_' +
          createLanguageDto.language.toLowerCase().replace(/ /g, '_'),
      },
    });
    if (!setting) {
      setting = await this.settingRepository.save({
        group: SettingGroup.LANGUAGE,
        key:
          SettingGroup.LANGUAGE +
          '_' +
          createLanguageDto.language.toLowerCase().replace(/ /g, '_'),
        value: createLanguageDto.language,
        order: 0,
        status: createLanguageDto.status as SettingStatus,
      });
    } else {
      throw new BadRequestException(MESSAGE_CONFIG.LANGUAGE_ALREADY_EXISTS);
    }

    await Promise.all(
      metadatas.map(async (metadata) => {
        await this.settingMetadataRepository.save({
          ...metadata,
          setting,
        });
      }),
    );
    return setting;
  }

  async getLanguages(languageFilterDto: LanguageFilterDto) {
    const { id, status } = languageFilterDto;
    const query = this.settingRepository
      .createQueryBuilder('setting')
      .leftJoinAndSelect('setting.metadata', 'metadata')
      .where('setting.group = :group', { group: SettingGroup.LANGUAGE });

    if (id) {
      query.andWhere('setting.id = :id', { id });
    }
    if (status) {
      query.andWhere('setting.status = :status', { status });
    }
    return await query.getMany();
  }

  async updateMultipleLanguage(updateLanguageDto: UpdateMultipleLanguageDto) {
    const languages = updateLanguageDto.languages;
    await Promise.all(
      languages.map(async (language) => {
        await this.updateLanguage(language);
      }),
    );
  }

  async updateLanguage(updateLanguageDto: UpdateLanguageDto) {
    const setting = await this.settingRepository.findOne({
      where: {
        id: updateLanguageDto.id,
      },
    });
    if (!setting) {
      throw new NotFoundException('Language not found');
    }

    await this.settingRepository.update(updateLanguageDto.id, {
      status: updateLanguageDto.status as SettingStatus,
    });

    const existingMetadatas = await this.settingMetadataRepository.find({
      where: {
        setting: {
          id: updateLanguageDto.id,
        },
      },
    });

    const metadatas = [
      {
        key: LanguageKey.LANGUAGE,
        value:
          updateLanguageDto.language ||
          existingMetadatas.find((metadata) => metadata.key === 'language')
            ?.value,
      },
      {
        key: LanguageKey.SUBSCRIPTION_STATUS,
        value:
          updateLanguageDto.subscriptionStatus?.toLowerCase() ||
          existingMetadatas.find(
            (metadata) => metadata.key === 'subscriptionStatus',
          )?.value,
      },
      {
        key: LanguageKey.COINS_STATUS,
        value:
          updateLanguageDto.coinsStatus?.toLowerCase() ||
          existingMetadatas.find((metadata) => metadata.key === 'coinsStatus')
            ?.value,
      },
      {
        key: LanguageKey.CURRENCY,
        value:
          updateLanguageDto.currency?.toLowerCase() ||
          existingMetadatas.find((metadata) => metadata.key === 'currency')
            ?.value,
      },
    ];

    await Promise.all(
      metadatas.map(async (metadata) => {
        await this.settingMetadataRepository.update(
          {
            setting: {
              id: updateLanguageDto.id,
            },
            key: metadata.key,
          },
          {
            value: metadata.value,
          },
        );
      }),
    );
  }

  async deleteLanguage(id: number) {
    const setting = await this.settingRepository.findOne({
      where: { id },
    });
    if (!setting) {
      throw new NotFoundException('Language not found');
    }

    await this.settingMetadataRepository.softDelete({
      setting: {
        id,
      },
    });
    await this.settingRepository.softDelete(id);
    return {
      message: 'Language deleted successfully',
    };
  }

  async deleteMultipleLanguage(
    deleteMultipleLanguageDto: DeleteMultipleLanguageDto,
  ) {
    try {
      const ids = deleteMultipleLanguageDto.ids;
      const settings = await this.settingRepository.find({
        where: {
          id: In(ids),
          group: SettingGroup.LANGUAGE,
        },
        relations: ['metadata'],
      });
      if (settings.length !== ids.length) {
        throw new NotFoundException('Languages not found');
      }
      await Promise.all(
        settings.map(async (setting) => {
          await this.settingMetadataRepository.softDelete({
            setting: {
              id: setting.id,
            },
          });
        }),
      );
      await this.settingRepository.softDelete(ids);
      return {
        message: 'Languages deleted successfully',
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  async getContentClassification() {
    return await this.settingRepository.find({
      where: {
        group: SettingGroup.CONTENT_CLASSIFICATION,
        status: SettingStatus.ACTIVE,
      },
    });
  }

  async getContentDate() {
    return await this.settingRepository.find({
      where: {
        group: SettingGroup.CONTENT_DATE,
        status: SettingStatus.ACTIVE,
      },
    });
  }

  async createContentClassification(
    createClassificationDto: CreateClassificationDto,
  ) {
    return await this.createSettingData(
      createClassificationDto,
      SettingGroup.CONTENT_CLASSIFICATION,
    );
  }

  async createContentDate(createDateDto: CreateDateDto) {
    return await this.createSettingData(
      createDateDto,
      SettingGroup.CONTENT_DATE,
    );
  }

  async createSettingData(data: any, group: SettingGroup) {
    const setting = await this.settingRepository.findOne({
      where: {
        group,
        key: data.name.toLowerCase().replace(/ /g, '_'),
      },
    });
    if (setting) {
      throw new BadRequestException(MESSAGE_CONFIG.SETTING_ALREADY_EXISTS);
    }
    return await this.settingRepository.save({
      group,
      key: data.name.toLowerCase().replace(/ /g, '_'),
      value: data.name,
      order: 0,
      status: SettingStatus.ACTIVE,
    });
  }

  async updateAutoReply(updateAutoReplyDto: UpdateAutoReplyDto) {
    try {
      const autoReplySetting = await this.settingRepository.findOne({
        where: {
          group: SettingGroup.SUPPORT_AUTO_REPLY,
          key: 'qa_auto_reply_global',
        },
        relations: ['metadata'],
      });

      if (!autoReplySetting) {
        throw new NotFoundException('Auto reply setting not found');
      }

      const updates: { key: string; value: string }[] = [];

      if (updateAutoReplyDto.enabled !== undefined) {
        updates.push({
          key: 'enabled',
          value: updateAutoReplyDto.enabled.toString(),
        });
      }

      if (updateAutoReplyDto.content !== undefined) {
        updates.push({
          key: 'content',
          value: updateAutoReplyDto.content,
        });
      }

      if (updateAutoReplyDto.delay_minutes !== undefined) {
        updates.push({
          key: 'delay_minutes',
          value: updateAutoReplyDto.delay_minutes.toString(),
        });
      }

      if (updateAutoReplyDto.admin_username !== undefined) {
        updates.push({
          key: 'admin_username',
          value: updateAutoReplyDto.admin_username,
        });
      }

      if (updateAutoReplyDto.email_template !== undefined) {
        updates.push({
          key: 'email_template',
          value: updateAutoReplyDto.email_template,
        });
      }

      if (updateAutoReplyDto.subject_prefix !== undefined) {
        updates.push({
          key: 'subject_prefix',
          value: updateAutoReplyDto.subject_prefix,
        });
      }

      await Promise.all(
        updates.map(async (update) => {
          await this.settingMetadataRepository.update(
            {
              setting: { id: autoReplySetting.id },
              key: update.key,
            },
            {
              value: update.value,
            },
          );
        }),
      );

      return {
        message: 'Auto reply settings updated successfully',
        data: updates,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
