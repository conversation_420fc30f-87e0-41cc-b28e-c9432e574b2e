import {
  Controller,
  Get,
  // Post,
  // Body,
  // Patch,
  // Param,
  // Delete,
  // ParseIntPipe,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  // ApiParam,
  // ApiBody,
  // ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { PaymentService } from '../services/payment.service';
// import { CreatePaymentDto } from '../dto/create-payment.dto';
// import { UpdatePaymentDto } from '../dto/update-payment.dto';
// import { PaymentStatus } from '../../../../common/status.enum';
import { AdminRole } from '../../../../common/role.enum';
import { JwtAdminAuthGuard } from '../../auth-admin/guards/jwt-admin-auth.guard';
import { Roles } from '../../../auth/decorators/role.decorator';
import { PaymentHistoryQueryDto } from '../dto/payment-history-query.dto';
import { PaymentHistoryResponseDto } from '../dto/payment-history-response.dto';

@ApiTags('Admin - Payments')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
@Roles(AdminRole.ADMIN, AdminRole.SUPER_ADMIN)
@Controller('admin/payments')
export class PaymentController {
  constructor(private readonly paymentService: PaymentService) {}

  // @Post()
  // @ApiOperation({ summary: 'Create a new payment' })
  // @ApiBody({ type: CreatePaymentDto })
  // @ApiResponse({ status: 201, description: 'Payment created successfully' })
  // @ApiResponse({ status: 400, description: 'Bad request' })
  // async create(@Body() createPaymentDto: CreatePaymentDto) {
  //   return await this.paymentService.create(createPaymentDto);
  // }

  // @Get()
  // @ApiOperation({ summary: 'Get all payments' })
  // @ApiResponse({ status: 200, description: 'List of payments' })
  // async findAll() {
  //   return await this.paymentService.findAll();
  // }

  @Get('history')
  @ApiOperation({
    summary: 'Get payment history with filtering and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment history list',
    type: PaymentHistoryResponseDto,
  })
  async getPaymentHistory(
    @Query() query: PaymentHistoryQueryDto,
  ): Promise<PaymentHistoryResponseDto> {
    return await this.paymentService.getPaymentHistory(query);
  }

  // @Get('statistics')
  // @ApiOperation({ summary: 'Get payment statistics' })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Payment statistics',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       total: { type: 'number' },
  //       successful: { type: 'number' },
  //       failed: { type: 'number' },
  //       pending: { type: 'number' },
  //       canceled: { type: 'number' },
  //       totalRevenue: { type: 'number' },
  //     },
  //   },
  // })
  // async getStatistics() {
  //   return await this.paymentService.getPaymentStatistics();
  // }

  // @Get('revenue')
  // @ApiOperation({ summary: 'Get revenue by date range' })
  // @ApiQuery({
  //   name: 'startDate',
  //   type: 'string',
  //   description: 'Start date (YYYY-MM-DD)',
  // })
  // @ApiQuery({
  //   name: 'endDate',
  //   type: 'string',
  //   description: 'End date (YYYY-MM-DD)',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Revenue data',
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       revenue: { type: 'number' },
  //       startDate: { type: 'string' },
  //       endDate: { type: 'string' },
  //     },
  //   },
  // })
  // async getRevenue(
  //   @Query('startDate') startDate: string,
  //   @Query('endDate') endDate: string,
  // ) {
  //   const start = new Date(startDate);
  //   const end = new Date(endDate);
  //   const revenue = await this.paymentService.getTotalRevenueByDateRange(
  //     start,
  //     end,
  //   );
  //   return { revenue, startDate, endDate };
  // }

  // @Get('status/:status')
  // @ApiOperation({ summary: 'Get payments by status' })
  // @ApiParam({
  //   name: 'status',
  //   enum: PaymentStatus,
  //   description: 'Payment status',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'List of payments with specified status',
  // })
  // async findByStatus(@Param('status') status: PaymentStatus) {
  //   return await this.paymentService.findByStatus(status);
  // }

  // @Get('subscription/:subscriptionId')
  // @ApiOperation({ summary: 'Get payments by subscription ID' })
  // @ApiParam({
  //   name: 'subscriptionId',
  //   type: 'number',
  //   description: 'Subscription ID',
  // })
  // @ApiResponse({ status: 200, description: 'List of subscription payments' })
  // async findBySubscription(
  //   @Param('subscriptionId', ParseIntPipe) subscriptionId: number,
  // ) {
  //   return await this.paymentService.findBySubscription(subscriptionId);
  // }

  // @Get('provider/:providerTransactionId')
  // @ApiOperation({ summary: 'Get payment by provider transaction ID' })
  // @ApiParam({
  //   name: 'providerTransactionId',
  //   type: 'string',
  //   description: 'Provider transaction ID',
  // })
  // @ApiResponse({ status: 200, description: 'Payment details' })
  // @ApiResponse({ status: 404, description: 'Payment not found' })
  // async findByProviderTransactionId(
  //   @Param('providerTransactionId') providerTransactionId: string,
  // ) {
  //   return await this.paymentService.findByProviderTransactionId(
  //     providerTransactionId,
  //   );
  // }

  // @Get(':id')
  // @ApiOperation({ summary: 'Get payment by ID' })
  // @ApiParam({ name: 'id', type: 'number', description: 'Payment ID' })
  // @ApiResponse({ status: 200, description: 'Payment details' })
  // @ApiResponse({ status: 404, description: 'Payment not found' })
  // async findOne(@Param('id', ParseIntPipe) id: number) {
  //   return await this.paymentService.findOne(id);
  // }

  // @Patch(':id')
  // @ApiOperation({ summary: 'Update payment' })
  // @ApiParam({ name: 'id', type: 'number', description: 'Payment ID' })
  // @ApiBody({ type: UpdatePaymentDto })
  // @ApiResponse({ status: 200, description: 'Payment updated successfully' })
  // @ApiResponse({ status: 404, description: 'Payment not found' })
  // async update(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body() updatePaymentDto: UpdatePaymentDto,
  // ) {
  //   return await this.paymentService.update(id, updatePaymentDto);
  // }

  // @Delete(':id')
  // @ApiOperation({ summary: 'Delete payment' })
  // @ApiParam({ name: 'id', type: 'number', description: 'Payment ID' })
  // @ApiResponse({ status: 200, description: 'Payment deleted successfully' })
  // @ApiResponse({ status: 404, description: 'Payment not found' })
  // async remove(@Param('id', ParseIntPipe) id: number) {
  //   await this.paymentService.remove(id);
  //   return { message: 'Payment deleted successfully' };
  // }

  // @Patch(':id/mark-successful')
  // @ApiOperation({ summary: 'Mark payment as successful' })
  // @ApiParam({ name: 'id', type: 'number', description: 'Payment ID' })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       providerTransactionId: {
  //         type: 'string',
  //         description: 'Provider transaction ID',
  //       },
  //       providerTransactionStatus: {
  //         type: 'string',
  //         description: 'Provider transaction status',
  //       },
  //     },
  //   },
  // })
  // @ApiResponse({ status: 200, description: 'Payment marked as successful' })
  // @ApiResponse({ status: 404, description: 'Payment not found' })
  // async markAsSuccessful(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body()
  //   body: {
  //     providerTransactionId?: string;
  //     providerTransactionStatus?: string;
  //   },
  // ) {
  //   return await this.paymentService.markAsSuccessful(
  //     id,
  //     body.providerTransactionId,
  //     body.providerTransactionStatus,
  //   );
  // }

  // @Patch(':id/mark-failed')
  // @ApiOperation({ summary: 'Mark payment as failed' })
  // @ApiParam({ name: 'id', type: 'number', description: 'Payment ID' })
  // @ApiBody({
  //   schema: {
  //     type: 'object',
  //     properties: {
  //       providerTransactionStatus: {
  //         type: 'string',
  //         description: 'Provider transaction status',
  //       },
  //     },
  //   },
  // })
  // @ApiResponse({ status: 200, description: 'Payment marked as failed' })
  // @ApiResponse({ status: 404, description: 'Payment not found' })
  // async markAsFailed(
  //   @Param('id', ParseIntPipe) id: number,
  //   @Body() body: { providerTransactionStatus?: string },
  // ) {
  //   return await this.paymentService.markAsFailed(
  //     id,
  //     body.providerTransactionStatus,
  //   );
  // }

  // @Patch(':id/mark-canceled')
  // @ApiOperation({ summary: 'Mark payment as canceled' })
  // @ApiParam({ name: 'id', type: 'number', description: 'Payment ID' })
  // @ApiResponse({ status: 200, description: 'Payment marked as canceled' })
  // @ApiResponse({ status: 404, description: 'Payment not found' })
  // async markAsCanceled(@Param('id', ParseIntPipe) id: number) {
  //   return await this.paymentService.markAsCanceled(id);
  // }
}
