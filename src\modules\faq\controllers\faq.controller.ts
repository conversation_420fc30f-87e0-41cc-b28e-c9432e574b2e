import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { OptionalJwtAuthGuard } from '../../auth/guards/optional-jwt-auth.guard';
import { FaqService } from '../services/faq.service';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';

@ApiTags('FAQ')
@Controller('faq')
@UseGuards(OptionalJwtAuthGuard)
export class FaqController {
  constructor(private readonly faqService: FaqService) {}

  @Get()
  @ApiOperation({ summary: 'Get FAQ list for users' })
  @ApiResponse({ status: 200, description: 'FAQ list retrieved successfully' })
  async getFaqList(@Query() query: FaqListQueryDto) {
    return this.faqService.getFaqList(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get FAQ detail and increment view count' })
  @ApiResponse({
    status: 200,
    description: 'FAQ detail retrieved successfully',
  })
  async getFaqDetail(@Param('id', ParseIntPipe) id: number, @Req() req) {
    const userId = req.user?.id;
    return this.faqService.getFaqDetail(id, userId);
  }
}
