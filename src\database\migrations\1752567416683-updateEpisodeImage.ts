import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateEpisodeImage1752567416683 implements MigrationInterface {
  name = 'UpdateEpisodeImage1752567416683';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` ADD \`order\` int NULL DEFAULT '0'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode_image\` DROP COLUMN \`order\``,
    );
  }
}
