import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SubscriptionPlanEntity } from '../../../database/entities/subscription_plan.entity';
import { SubscriptionEntity } from '../../../database/entities/subscription.entity';
import { PaymentEntity } from '../../../database/entities/payment.entity';
import { SubscriptionPlanRepository } from '../../../database/repositories/subscription-plan.repository';
import { SubscriptionRepository } from '../../../database/repositories/subscription.repository';
import { PaymentRepository } from '../../../database/repositories/payment.repository';
import { SubscriptionPlanController } from './controllers/subscription-plan.controller';
// import { SubscriptionController } from './controllers/subscription.controller';
import { PaymentController } from './controllers/payment.controller';
import { SubscriptionPlanService } from './services/subscription-plan.service';
import { SubscriptionService } from './services/subscription.service';
import { PaymentService } from './services/payment.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SubscriptionPlanEntity,
      SubscriptionEntity,
      PaymentEntity,
    ]),
  ],
  controllers: [
    SubscriptionPlanController,
    // SubscriptionController,
    PaymentController,
  ],
  providers: [
    SubscriptionPlanRepository,
    SubscriptionRepository,
    PaymentRepository,
    SubscriptionPlanService,
    SubscriptionService,
    PaymentService,
  ],
  exports: [SubscriptionPlanService, SubscriptionService, PaymentService],
})
export class PaymentManagementModule {}
