import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { EpisodeEntity } from '../entities/episode.entity';
@Injectable()
export class EpisodeRepository extends Repository<EpisodeEntity> {
  constructor(private dataSource: DataSource) {
    super(EpisodeEntity, dataSource.createEntityManager());
  }

  async updateViewCount(id: number) {
    await this.update(id, { viewCount: () => 'viewCount + 1' });
  }
}
