import { Column, Entity, ManyToOne } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { UserEntity } from './user.entity';
import { CommonStatus } from '../../common/status.enum';
import { Exclude } from 'class-transformer';

@Entity('sessions')
export class SessionEntity extends DefaultEntity {
  @Column({ type: 'varchar', length: 255, nullable: true })
  uuid: string;

  @Column({ type: 'varchar', length: 255 })
  ipAddress: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  userAgent: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  platform: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  browser: string;

  @Exclude()
  @Column({ type: 'varchar', length: 255, nullable: true })
  refreshToken: string;

  @Exclude()
  @Column({ type: 'bigint', nullable: true })
  refreshTokenExpiresAt: number;

  @Column({ type: 'enum', enum: CommonStatus, default: CommonStatus.ACTIVE })
  status: CommonStatus;

  @Column({ type: 'bigint', nullable: true })
  lastUpdateTimestamp: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  os: string;

  @ManyToOne(() => UserEntity, (user) => user.sessions)
  user: UserEntity;
}
