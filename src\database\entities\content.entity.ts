import { Column, Entity, <PERSON>ToOne, OneToMany } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { GenreEntity } from './genre.entity';
import { ContentSettingEntity } from './content_setting.entity';
import { AuthorEntity } from './author.entity';
import { EpisodeEntity } from './episode.entity';
import { ContentStatus } from '../../common/status.enum';
import { DisplayEntity } from './display.entity';
import { UserContentEntity } from './user_content.entity';

@Entity('content')
export class ContentEntity extends DefaultEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  imagePc: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  imageMobile: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  expectedReleaseDate: string;

  @ManyToOne(() => AuthorEntity, (author) => author.contents)
  author: AuthorEntity;

  @ManyToOne(() => GenreEntity, (genre) => genre.contents)
  genre: GenreEntity;

  @Column({ type: 'boolean', default: true })
  isAdult: boolean;

  @Column({ type: 'enum', enum: ContentStatus, nullable: false })
  status: ContentStatus;

  @Column({ type: 'int', default: 0 })
  viewCount: number;

  @OneToMany(
    () => ContentSettingEntity,
    (contentSetting) => contentSetting.content,
  )
  contentSettings: ContentSettingEntity[];

  @OneToMany(() => EpisodeEntity, (episode) => episode.content)
  episodes: EpisodeEntity[];

  @OneToMany(() => DisplayEntity, (display) => display.content)
  displays: DisplayEntity[];

  @OneToMany(() => UserContentEntity, (userContent) => userContent.content)
  userContents: UserContentEntity[];
}
