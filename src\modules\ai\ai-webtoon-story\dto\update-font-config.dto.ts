import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsNumber, IsString, Min } from 'class-validator';

export class UpdateFontConfigDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  fontFamily: string;

  @ApiProperty()
  @IsNumber()
  @IsNotEmpty()
  fontSize: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fontStyle: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  fontWeight: string;

  @ApiProperty()
  @IsInt()
  @Min(1)
  @IsNotEmpty()
  aiLetteringLanguageId: number;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  dialogueColor: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  dialogueFontStyle: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bubbleFillColor: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  bubbleStrokeColor: string;
}
