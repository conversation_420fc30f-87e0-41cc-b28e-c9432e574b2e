import { Module } from '@nestjs/common';
import { UserService } from './user.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from '../../database/entities/user.entity';
import { UserRepository } from '../../database/repositories/user.repository';
import { UserController } from './user.controller';
import { SanctionRepository } from '../../database/repositories/sanction.repository';
import { SanctionEntity } from '../../database/entities/sanction.entity';
import { SessionRepository } from '../../database/repositories/session.repository';
import { SessionEntity } from '../../database/entities/session.entity';
import { PaymentRepository } from '../../database/repositories/payment.repository';
import { PaymentEntity } from '../../database/entities/payment.entity';
import { AccountDeletionAuditEntity } from '../../database/entities/account-deletion-audit.entity';
import { AccountDeletionAuditRepository } from '../../database/repositories/account-deletion-audit.repository';
import { RateLimitService } from './services/rate-limit.service';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      SanctionEntity,
      SessionEntity,
      PaymentEntity,
      AccountDeletionAuditEntity,
    ]),
    AnalyticsQueueModule,
  ],
  providers: [
    UserService,
    UserRepository,
    SanctionRepository,
    SessionRepository,
    PaymentRepository,
    AccountDeletionAuditRepository,
    RateLimitService,
  ],
  exports: [UserService, RateLimitService],
  controllers: [UserController],
})
export class UserModule {}
