import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';

export class GetContentSettingKeyDto {
  @ApiProperty({
    description: 'Setting key',
    required: false,
    default: '',
    enum: ['new', 'popular', 'completed'],
  })
  @IsOptional()
  @IsString()
  settingKey: string;

  @ApiProperty({
    description: 'Genre id',
    required: false,
    example: 1,
  })
  @IsOptional()
  @IsNumber()
  genreId: number;

  @ApiProperty({
    description: 'Limit',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit: number = 10;

  @ApiProperty({
    description: 'Page',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page: number = 1;
}
