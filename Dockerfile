FROM node:20-alpine AS builder
# Set timezone
ENV TZ=Asia/Ho_Chi_Minh
RUN apk add --no-cache tzdata
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
RUN npm run build

# Stage 2: Runtime (distroless)
FROM node:20-alpine
# Set timezone for runtime
ENV TZ=Asia/Ho_Chi_Minh
RUN apk add --no-cache tzdata
WORKDIR /app
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/.env.dev ./
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/tsconfig.json ./tsconfig.json
COPY --from=builder /app/uploads ./uploads
COPY --from=builder /app/mail-templates ./mail-templates
EXPOSE 3005
CMD ["dist/main"]