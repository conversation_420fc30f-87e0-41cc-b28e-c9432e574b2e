import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { TransformResponseInterceptor } from './transform.interceptor';
import { ValidationPipe } from '@nestjs/common';
import { json } from 'express';
import { urlencoded } from 'express';
async function bootstrap() {
  // Set timezone to UTC+7 (Asia/Ho_Chi_Minh)
  process.env.TZ = 'Asia/Ho_Chi_Minh';
  const app = await NestFactory.create(AppModule);
  app.use(json({ limit: '50mb' }));
  app.use(urlencoded({ extended: true, limit: '50mb' }));
  app.enableCors({
    origin: [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'http://localhost:3003',
      'http://localhost:3004',
      'http://localhost:3005',
      'http://localhost:5173',
      'http://localhost:4200',
      'http://localhost:3030',
      'https://webtoon.sotatek.works',
      'https://admin.webtoon.sotatek.works',
    ],
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
    credentials: true,
  });

  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: false,
    }),
  );
  app.useGlobalInterceptors(new TransformResponseInterceptor());

  const config = new DocumentBuilder()
    .setTitle('NestJS API')
    .setDescription('The NestJS API description')
    .setVersion('1.0')
    .addTag('NestJS')
    .addBearerAuth()
    .build();
  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, documentFactory);

  await app.listen(process.env.PORT ?? 3000).then(() => {
    console.log(`Server is running on port ${process.env.PORT ?? 3000}`);
    console.log(
      `API documentation is available at http://localhost:${process.env.PORT ?? 3000}/docs`,
    );
  });
}
bootstrap().catch((error) => {
  console.error(error);
});
