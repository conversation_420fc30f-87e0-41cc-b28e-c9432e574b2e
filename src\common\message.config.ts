export const MESSAGE_CONFIG = {
  PASSWORD_REGEX: {
    key: 'password_regex',
    message:
      'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character',
  },
  USER_NOT_FOUND: { key: 'user_not_found', message: 'User not found' },
  USER_NOT_FOUND_BY_EMAIL: {
    key: 'user_not_found_by_email',
    message: 'User not found by email',
  },
  USER_NOT_FOUND_BY_ID: {
    key: 'user_not_found_by_id',
    message: 'User not found by id',
  },
  USER_NOT_FOUND_BY_USERNAME: {
    key: 'user_not_found_by_username',
    message: 'User not found by username',
  },
  INVALID_TOKEN: { key: 'invalid_token', message: 'Invalid token' },
  TOKEN_EXPIRED: { key: 'token_expired', message: 'Token expired' },
  REFRESH_TOKEN_EXPIRED: {
    key: 'refresh_token_expired',
    message: 'Refresh token expired',
  },
  REFRESH_TOKEN_INVALID: {
    key: 'refresh_token_invalid',
    message: 'Refresh token invalid',
  },
  REFRESH_TOKEN_NOT_FOUND: {
    key: 'refresh_token_not_found',
    message: 'Refresh token not found',
  },
  EMAIL_ALREADY_EXISTS: {
    key: 'email_already_exists',
    message: 'Email already exists',
  },
  USERNAME_ALREADY_EXISTS: {
    key: 'username_already_exists',
    message: 'Username already exists',
  },
  INVALID_CREDENTIALS: {
    key: 'invalid_credentials',
    message: 'Invalid credentials',
  },
  FORGOT_PASSWORD_TOKEN_ALREADY_EXISTS: {
    key: 'forgot_password_token_already_exists',
    message: 'Forgot password token already exists',
  },
  FORGOT_PASSWORD_TOKEN_EXPIRED: {
    key: 'forgot_password_token_expired',
    message: 'Forgot password token expired',
  },
  FORGOT_PASSWORD_TOKEN_INVALID: {
    key: 'forgot_password_token_invalid',
    message: 'Forgot password token invalid',
  },
  FORGOT_PASSWORD_TOKEN_NOT_FOUND: {
    key: 'forgot_password_token_not_found',
    message: 'Forgot password token not found',
  },
  FORGOT_PASSWORD_TOKEN_USED: {
    key: 'forgot_password_token_used',
    message: 'Forgot password token used',
  },
  FORGOT_PASSWORD_TOKEN_NOT_USED: {
    key: 'forgot_password_token_not_used',
    message: 'Forgot password token not used',
  },
  SETTING_ALREADY_EXISTS: {
    key: 'setting_already_exists',
    message: 'Setting already exists',
  },
  LANGUAGE_ALREADY_EXISTS: {
    key: 'language_already_exists',
    message: 'Language already exists',
  },
  INVALID_FILE_EXTENSION: {
    key: 'invalid_file_extension',
    message: 'Invalid file extension',
  },
  SETTING_NOT_FOUND: { key: 'setting_not_found', message: 'Setting not found' },
  AUTHOR_NOT_FOUND: { key: 'author_not_found', message: 'Author not found' },
  AUTHOR_ALREADY_EXISTS: {
    key: 'author_already_exists',
    message: 'Author already exists',
  },
  GENRE_NOT_FOUND: { key: 'genre_not_found', message: 'Genre not found' },
  CONTENT_NOT_FOUND: { key: 'content_not_found', message: 'Content not found' },
  EPISODE_NOT_FOUND: { key: 'episode_not_found', message: 'Episode not found' },
  DISPLAY_NOT_FOUND: { key: 'display_not_found', message: 'Display not found' },
  DISPLAY_CREATED_SUCCESSFULLY: {
    key: 'display_created_successfully',
    message: 'Display created successfully',
  },
  AUTHOR_EMAIL_ALREADY_EXISTS: {
    key: 'author_email_already_exists',
    message: 'Author email already exists',
  },
  USER_SANCTION_NOT_EXPIRED: {
    key: 'user_sanction_not_expired',
    message: 'User is restricted',
  },
  EPISODE_NOT_FOR_NON_USER_VIEWING: {
    key: 'episode_not_for_non_user_viewing',
    message: 'Episode is not for non user viewing',
  },
  ADMIN_NOT_FOUND: { key: 'admin_not_found', message: 'Admin not found' },
  ADMIN_ALREADY_EXISTS: {
    key: 'admin_already_exists',
    message: 'Admin already exists',
  },
  ADMIN_NOT_FOUND_BY_EMAIL: {
    key: 'admin_not_found_by_email',
    message: 'Admin not found by email',
  },
  ADMIN_NOT_HAVE_PERMISSION: {
    key: 'admin_not_have_permission',
    message: 'Admin not have permission',
  },
  MAIL_NOT_SENT: { key: 'mail_not_sent', message: 'Mail not sent' },
  MAIL_SENT: { key: 'mail_sent', message: 'Mail sent' },
  FORGOT_PASSWORD_MAIL_SENT: {
    key: 'forgot_password_mail_sent',
    message: 'Forgot password mail sent',
  },
  FORGOT_PASSWORD_MAIL_NOT_SENT: {
    key: 'forgot_password_mail_not_sent',
    message: 'Forgot password mail not sent',
  },
  PASSWORD_RESET_SUCCESSFULLY: {
    key: 'password_reset_successfully',
    message: 'Password reset successfully',
  },
  PASSWORD_RESET_FAILED: {
    key: 'password_reset_failed',
    message: 'Password reset failed',
  },
  ADMIN_CREATED_SUCCESSFULLY: {
    key: 'admin_created_successfully',
    message: 'Admin created successfully, please check your email',
  },
  SESSION_NOT_FOUND: {
    key: 'session_not_found',
    message: 'Session not found, please login again',
  },
  INVALID_OLD_PASSWORD: {
    key: 'invalid_old_password',
    message: 'Invalid old password',
  },
  USER_ALREADY_HAS_ACTIVE_SUBSCRIPTION: {
    key: 'user_already_has_active_subscription',
    message: 'User already has an active subscription',
  },
  // Support/Ticket related messages
  TICKET_NOT_FOUND: {
    key: 'ticket_not_found',
    message: 'Ticket not found',
  },
  TICKET_ALREADY_CLOSED: {
    key: 'ticket_already_closed',
    message: 'Ticket is already closed',
  },
  TICKET_CLOSED: {
    key: 'ticket_closed',
    message: 'Cannot add follow-up to closed ticket',
  },
  RESPONSE_NOT_FOUND: {
    key: 'response_not_found',
    message: 'Response not found or access denied',
  },
  RESPONSE_TOO_OLD: {
    key: 'response_too_old',
    message: 'Cannot update response after 1 hour',
  },
  CANNOT_UPDATE_AUTO_REPLY: {
    key: 'cannot_update_auto_reply',
    message: 'Cannot update auto-reply responses',
  },
  NO_RESPONSE: {
    key: 'no_response',
    message: 'No completed response available',
  },
  SYSTEM_ERROR: {
    key: 'system_error',
    message: 'System configuration error',
  },
  // Category related messages
  CATEGORY_NOT_FOUND: {
    key: 'category_not_found',
    message: 'Category not found',
  },
  CATEGORY_EXISTS: {
    key: 'category_exists',
    message: 'Category with this name already exists',
  },
  NO_CATEGORIES: {
    key: 'no_categories',
    message: 'No category IDs provided',
  },
  CATEGORIES_IN_USE: {
    key: 'categories_in_use',
    message: 'Cannot delete categories with active tickets',
  },
  // FAQ related messages
  FAQ_NOT_FOUND: {
    key: 'faq_not_found',
    message: 'FAQ not found',
  },
  NO_IDS_PROVIDED: {
    key: 'no_ids_provided',
    message: 'No IDs provided',
  },
  // Service
  SERVICE_NOT_FOUND: {
    key: 'service_not_found',
    message: 'Service not found',
  },
  SERVICE_BUSY: {
    key: 'service_busy',
    message: 'Currently all services are busy',
  },
  SERVICE_MAINTENANCE: {
    key: 'service_maintenance',
    message: 'Service is under maintenance or not available',
  },
  SERVICE_STOP_FAILED: {
    key: 'service_stop_failed',
    message: 'Service stop failed',
  },
  TIMEZONE_INVALID: {
    key: 'timezone_invalid',
    message: 'Timezone invalid',
  },
  ITEM_NOT_FOUND: {
    key: 'item_not_found',
    message: 'Item not found',
  },
  ITEMS_NOT_FOUND: {
    key: 'items_not_found',
    message: 'One or more items not found',
  },
  INVALID_DATA: {
    key: 'invalid_data',
    message: 'Invalid data format',
  },
};

export const AI_MESSAGE_CONFIG = {
  //Lettering
  AI_LETTERING_NOT_FOUND: {
    key: 'ai_lettering_not_found',
    message: 'Lettering not found',
  },
  AI_LETTERING_EXISTS: {
    key: 'ai_lettering_exists',
    message: 'Lettering already exists',
  },
  AI_LETTERING_TYPE_NOT_FOUND: {
    key: 'ai_lettering_type_not_found',
    message: 'Lettering type not found',
  },
  AI_LETTERING_TYPE_CANNOT_DELETE_DEFAULT: {
    key: 'ai_lettering_type_cannot_delete_default',
    message: 'Cannot delete default lettering type',
  },
  AI_LETTERING_TYPE_EXISTS: {
    key: 'ai_lettering_type_exists',
    message: 'Lettering type already exists',
  },
  AI_LETTERING_TYPE_IN_USE: {
    key: 'ai_lettering_type_in_use',
    message: 'Cannot delete type that is in use',
  },
  AI_LETTERING_LANGUAGE_NOT_FOUND: {
    key: 'ai_lettering_language_not_found',
    message: 'Lettering language not found',
  },
  AI_LETTERING_LANGUAGE_EXISTS: {
    key: 'ai_lettering_language_exists',
    message: 'Lettering language already exists',
  },
  AI_LETTERING_LANGUAGE_IN_USE: {
    key: 'ai_lettering_language_in_use',
    message: 'Cannot delete language that is in use',
  },
  AI_LETTERING_LANGUAGE_CANNOT_DELETE_DEFAULT: {
    key: 'ai_lettering_language_cannot_delete_default',
    message: 'Cannot delete default language',
  },
  //Story
  AI_STORY_NOT_FOUND: {
    key: 'ai_story_not_found',
    message: 'story not found',
  },
  AI_STORY_CHAPTER_EXISTS: {
    key: 'ai_story_chapter_exists',
    message: 'story chapter exists',
  },
  AI_STORY_UNIQUE_CHARACTERS: {
    key: 'ai_story_unique_characters',
    message: 'story unique characters',
  },
  AI_STORY_CHARACTER_IN_USE: {
    key: 'ai_story_character_in_use',
    message: 'Character is currently in use',
  },
  AI_STORY_CANNOT_DELETE_WHILE_GENERATING: {
    key: 'ai_story_cannot_delete_while_generating',
    message: 'Cannot delete story while a chapter is generating images',
  },
  AI_STORY_SOME_CHAPTER_NOT_FOUND: {
    key: 'ai_story_some_chapter_not_found',
    message: 'Some chapters not found',
  },
  //chapter
  AI_CHAPTER_NOT_FOUND: {
    key: 'ai_chapter_not_found',
    message: 'chapter not found',
  },
  AI_CHAPTER_CANNOT_DELETE_WHILE_GENERATING: {
    key: 'ai_chapter_cannot_delete_while_generating',
    message: 'Cannot delete chapter while generating',
  },
  AI_CHAPTER_FAILED_TO_MERGE_PREVIEW_IMAGES: {
    key: 'ai_chapter_failed_to_merge_preview_images',
    message: 'Failed to merge preview images',
  },
  //character
  AI_CHARACTER_NOT_FOUND: {
    key: 'ai_character_not_found',
    message: 'character not found',
  },
  AI_CHARACTER_UUID_NOT_FOUND: {
    key: 'ai_character_uuid_not_found',
    message: 'Character UUID not found',
  },
  AI_CHARACTER_GENERATE_DESCRIPTION_FAILED: {
    key: 'ai_character_generate_description_failed',
    message: 'Failed to generate description label',
  },
  AI_CHARACTER_TRAINING_OR_SYNCING: {
    key: 'ai_character_training_or_syncing',
    message: 'Character is training or syncing',
  },
  AI_CHARACTER_NOT_WAITING: {
    key: 'ai_character_not_waiting',
    message: 'Character is not waiting',
  },
  AI_CHARACTER_TRAINING_SESSION_NOT_FOUND: {
    key: 'ai_character_training_session_not_found',
    message: 'Character training session not found',
  },
  AI_CHARACTER_TRAINING_LOG_MODEL_NOT_FOUND: {
    key: 'ai_character_training_log_model_not_found',
    message: 'Training log model not found',
  },
  AI_CHARACTER_MODEL_URL_NOT_FOUND: {
    key: 'ai_character_model_url_not_found',
    message: 'Model URL not found',
  },
  AI_CHARACTER_NOT_READY: {
    key: 'ai_character_not_ready',
    message: 'Character is not ready',
  },
  AI_CHARACTER_TYPE_NOT_ALLOWED: {
    key: 'ai_character_type_not_allowed',
    message: 'The character type is not allowed',
  },
  AI_CHARACTER_MODEL_ALREADY_APPLIED: {
    key: 'ai_character_model_already_applied',
    message: 'The model you want to apply has already been applied',
  },
  AI_CHARACTER_FAILED_TO_APPLY_MODEL_TRAINING: {
    key: 'ai_character_failed_to_apply_model_training',
    message: 'Failed to apply model training',
  },
  AI_CHARACTER_NOT_SYNCING: {
    key: 'ai_character_not_syncing',
    message: 'Character not syncing',
  },
  AI_CHARACTER_INVALID_TYPE: {
    key: 'ai_character_invalid_type',
    message: 'Invalid type character',
  },
  AI_CHARACTER_TESTING_FAILED: {
    key: 'ai_character_testing_failed',
    message: 'Testing failed',
  },
  AI_CHARACTER_NEEDS_TRAINING: {
    key: 'ai_character_needs_training',
    message: 'Character needs training',
  },
  AI_CHARACTER_GENERATE_SAMPLE_PROMPT_FAILED: {
    key: 'ai_character_generate_sample_prompt_failed',
    message: 'Failed to generate sample prompts',
  },
  AI_CHARACTER_CAN_NOT_DELETE_CHARACTER_THAT_IS_TRAINING_OR_SYNCING: {
    key: 'ai_character_can_not_delete_character_that_is_training_or_syncing',
    message: 'Cannot delete character that is training or syncing',
  },
  AI_CHARACTER_IN_USE: {
    key: 'ai_character_in_use',
    message: 'Cannot delete character that is being used in stories',
  },
  AI_CHARACTER_NOT_FLUX: {
    key: 'ai_character_not_flux',
    message: 'Character not flux',
  },
  //Scene
  AI_SCENE_NOT_FOUND: {
    key: 'ai_scene_not_found',
    message: 'Scene not found',
  },
  AI_SCENE_IMAGE_GENERATION_ALREADY_STARTED: {
    key: 'ai_scene_image_generation_already_started',
    message: 'Scene image generation already started',
  },
  AI_SCENE_IMAGE_GENERATION_PROMPT_NOT_FOUND: {
    key: 'ai_scene_image_generation_prompt_not_found',
    message: 'Scene image generation prompt not found',
  },

  //Cut
  AI_CUT_NOT_FOUND: {
    key: 'ai_cut_not_found',
    message: 'Cut not found',
  },
  AI_CUT_UUID_NOT_FOUND: {
    key: 'ai_cut_uuid_not_found',
    message: 'Cut UUID not found',
  },
  AI_CUT_IMAGE_NOT_FOUND: {
    key: 'ai_cut_image_not_found',
    message: 'Cut image not found',
  },
  AI_CUT_NO_IMAGES: {
    key: 'ai_cut_no_images',
    message: 'Cut has no images',
  },
  AI_CUT_NOT_SAME_LENGTH: {
    key: 'ai_cut_not_same_length',
    message: 'Cut not same length',
  },
  AI_CUT_CANNOT_DELETE_WHILE_GENERATING: {
    key: 'ai_cut_cannot_delete_while_generating',
    message: 'Cut cannot delete while generating',
  },
  AI_CUT_NOT_TYPE_NORMAL: {
    key: 'ai_cut_not_type_normal',
    message: 'Cut is not type normal',
  },
  AI_CUT_NOT_TYPE_INPAINT: {
    key: 'ai_cut_not_type_inpaint',
    message: 'Cut is not type inpaint',
  },
  AI_CUT_NOT_GENERATING: {
    key: 'ai_cut_not_generating',
    message: 'Cut is not generating',
  },
  AI_CUT_GENERATING_OR_WAITING: {
    key: 'ai_cut_generating_or_waiting',
    message: 'Cut is generating or waiting, please wait for it to finish',
  },
  AI_CUT_NOT_API_GEN_NAME: {
    key: 'ai_cut_not_api_gen_name',
    message: 'Cut not api gen name',
  },

  //Prepare character
  AI_PREPARE_CHARACTER_NOT_FOUND: {
    key: 'ai_prepare_character_not_found',
    message: 'Prepare character not found',
  },
  AI_PREPARE_CHARACTER_GENERATED_NOT_FOUND: {
    key: 'ai_prepare_character_generated_not_found',
    message: 'Prepare character generated not found',
  },

  //Common
  AI_DANBOORU_PROMPT_NOT_FOUND: {
    key: 'ai_danbooru_prompt_not_found',
    message: 'Danbooru prompt not found',
  },
  AI_GENERATE_FAILED: {
    key: 'ai_generate_failed',
    message: 'Generate failed',
  },

  AI_GENERATE_SKETCH_IMAGE_FAILED: {
    key: 'ai_generate_sketch_image_failed',
    message: 'Failed to generate sketch image',
  },
  AI_GENERATE_PROMPT_BY_DESCRIPTION_FAILED: {
    key: 'ai_generate_prompt_by_description_failed',
    message: 'Failed to generate prompt by description',
  },
  AI_IMAGE_INDEX_NOT_FOUND: {
    key: 'ai_image_index_not_found',
    message: 'Image index not found',
  },
  AI_NO_IMAGE_LEFT: {
    key: 'ai_no_image_left',
    message: 'No image left',
  },
  AI_GENERAL_PROMPT_NOT_FOUND: {
    key: 'ai_general_prompt_not_found',
    message: 'General prompt not found',
  },
  AI_NO_CUT_TO_GENERATE_IMAGE: {
    key: 'ai_no_cut_to_generate_image',
    message: 'No cut to generate image',
  },
  AI_NO_CUT_VALID_TO_GENERATE_IMAGE: {
    key: 'ai_no_cut_valid_to_generate_image',
    message: 'No valid cut to generate image',
  },
  AI_INPAINT_IMAGE_FAILED: {
    key: 'ai_inpaint_image_failed',
    message: 'Failed to inpaint image',
  },
  AI_NO_CUT_OR_CHAPTER: {
    key: 'ai_no_cut_or_chapter',
    message: 'No cut or chapter',
  },
  AI_ANOTHER_CUT_WITH_SAME_API_IS_CURRENTLY_GENERATING_IMAGES: {
    key: 'ai_another_cut_with_same_api_is_currently_generating_images',
    message: 'Another cut with the same API is currently generating images',
  },
  AI_ORDER_GENERATE_NOT_FOUND: {
    key: 'ai_order_generate_not_found',
    message: 'Order generate not found',
  },
  AI_COMBINE_IMAGES_FAILED: {
    key: 'ai_combine_images_failed',
    message: 'Failed to combine images',
  },
  AI_SKETCH_INPAINT_IMAGE_FAILED: {
    key: 'ai_sketch_inpaint_image_failed',
    message: 'Failed to sketch inpaint image',
  },
  AI_GENERATE_IMAGES_FAILED: {
    key: 'ai_generate_images_failed',
    message: 'Failed to generate images',
  },
  AI_SKETCH_IMAGE_FAILED: {
    key: 'ai_sketch_image_failed',
    message: 'Failed to sketch image',
  },
  AI_SCENE_IMAGE_GENERATION_NOT_STARTED: {
    key: 'ai_scene_image_generation_not_started',
    message: 'Scene image generation not started',
  },
  AI_GENERATE_CUT_IMAGES_BY_PREPARE_CHARACTER_FAILED: {
    key: 'ai_generate_cut_images_by_prepare_character_failed',
    message: 'Failed to generate cut images by prepare character',
  },
  AI_GENERATE_COLLECTION_IMAGES_FAILED: {
    key: 'ai_generate_collection_images_failed',
    message: 'Failed to generate collection images',
  },

  //Synthetic Data
  AI_SYNTHETIC_DATA_NOT_FOUND: {
    key: 'ai_synthetic_data_not_found',
    message: 'Ai webtoon synthetic data not found',
  },
  AI_SYNTHETIC_DATA_NOT_GENERATING: {
    key: 'ai_synthetic_data_not_generating',
    message: 'Ai webtoon synthetic data not generating',
  },
  AI_SYNTHETIC_DATA_UUID_NOT_FOUND: {
    key: 'ai_synthetic_data_uuid_not_found',
    message: 'UUID not found in synthetic data',
  },
  AI_SYNTHETIC_DATA_CHARACTER_NOT_FOUND: {
    key: 'ai_synthetic_data_character_not_found',
    message: 'Character not found',
  },
  AI_SYNTHETIC_DATA_CHARACTER_NOT_READY: {
    key: 'ai_synthetic_data_character_not_ready',
    message: 'Character not ready',
  },
  AI_SYNTHETIC_DATA_CHARACTER_NOT_TRAINED: {
    key: 'ai_synthetic_data_character_not_trained',
    message: 'Character not trained',
  },
  AI_SYNTHETIC_GENERATE_DATA_FAILED: {
    key: 'ai_synthetic_generate_data_failed',
    message: 'Failed to generate synthetic data',
  },
};
