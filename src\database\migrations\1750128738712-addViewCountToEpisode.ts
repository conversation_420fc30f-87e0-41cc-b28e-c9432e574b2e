import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddViewCountToEpisode1750128738712 implements MigrationInterface {
  name = 'AddViewCountToEpisode1750128738712';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`viewCount\` int NOT NULL DEFAULT '0'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_content\` ADD \`episodeId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_content\` ADD CONSTRAINT \`FK_7052fd554eb08282a429f56c355\` FOREIGN KEY (\`episodeId\`) REFERENCES \`episode\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`user_content\` DROP FOREIGN KEY \`FK_7052fd554eb08282a429f56c355\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`user_content\` DROP COLUMN \`episodeId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` DROP COLUMN \`viewCount\``,
    );
  }
}
