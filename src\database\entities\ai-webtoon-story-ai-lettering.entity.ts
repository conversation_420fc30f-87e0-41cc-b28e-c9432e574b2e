import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON> } from 'typeorm';
import { AiLetteringEntity } from './ai-lettering.entity';
import { AiWebtoonStoryEntity } from './ai-webtoon-story.entity';
import { DefaultEntity } from './default.entity';

@Entity({ name: 'ai_webtoon_story_ai_lettering' })
export class AiWebtoonStoryAiLetteringEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_story_id' })
  aiWebtoonStoryId: number;

  @Column({ name: 'ai_lettering_id' })
  aiLetteringId: number;

  @ManyToOne(
    () => AiWebtoonStoryEntity,
    (story) => story.aiWebtoonStoryAiLetterings,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'ai_webtoon_story_id' })
  aiWebtoonStory: AiWebtoonStoryEntity;

  @ManyToOne(
    () => AiLetteringEntity,
    (lettering) => lettering.aiWebtoonStoryAiLetterings,
    { onDelete: 'CASCADE' },
  )
  @JoinColumn({ name: 'ai_lettering_id' })
  aiLettering: AiLetteringEntity;
}
