import { <PERSON>du<PERSON> } from '@nestjs/common';
import { SettingController } from './setting.controller';
import { SettingService } from '../admin/setting/setting.service';
import { SettingRepository } from '../../database/repositories/setting.repository';
import { SettingEntity } from '../../database/entities/setting.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SettingMetadata } from '../../database/entities/setting_metadata.entity';
import { SettingMetadataRepository } from '../../database/repositories/setting-metadat.repository';
@Module({
  imports: [TypeOrmModule.forFeature([SettingEntity, SettingMetadata])],
  controllers: [SettingController],
  providers: [SettingService, SettingRepository, SettingMetadataRepository],
})
export class SettingModule {}
