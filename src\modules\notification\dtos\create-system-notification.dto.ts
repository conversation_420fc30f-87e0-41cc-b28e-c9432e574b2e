import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsEnum,
  IsOptional,
  MaxLength,
} from 'class-validator';
import { NotificationStatus } from '../../../database/entities/system-notification.entity';

export class CreateSystemNotificationDto {
  @ApiProperty({
    example: 'root',
    description: 'Notification name',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    example: 'System Maintenance Notice',
    description: 'Notification title',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiProperty({
    enum: NotificationStatus,
    example: NotificationStatus.ACTIVE,
    description: 'Notification status',
    required: false,
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @ApiProperty({
    example: '<p>System will be under maintenance from 2PM to 4PM.</p>',
    description: 'Notification content with HTML',
  })
  @IsNotEmpty()
  @IsString()
  content: string;
}
