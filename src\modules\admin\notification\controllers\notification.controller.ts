import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { NotificationService } from '../../../notification/services/notification.service';
import { CreateSystemNotificationDto } from '../../../notification/dtos/create-system-notification.dto';
import { CreateUserNotificationDto } from '../../../notification/dtos/create-user-notification.dto';
import { UpdateSystemNotificationDto } from '../../../notification/dtos/update-system-notification.dto';
import { UpdateUserNotificationDto } from '../../../notification/dtos/update-user-notification.dto';
import { FullUpdateSystemNotificationDto } from '../../../notification/dtos/full-update-system-notification.dto';
import { FullUpdateUserNotificationDto } from '../../../notification/dtos/full-update-user-notification.dto';
import { DeleteNotificationsDto } from '../../../notification/dtos/delete-notifications.dto';
import { AdminNotificationFilterDto } from '../../../notification/dtos/admin-notification-filter.dto';
import { JwtAdminAuthGuard } from '../../auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('Admin - Notifications')
@ApiBearerAuth()
@Controller('admin/notifications')
@UseGuards(JwtAdminAuthGuard)
export class AdminNotificationController {
  constructor(private readonly notificationService: NotificationService) {}

  // System Notification Endpoints
  @Post('system')
  @ApiOperation({ summary: 'Create system notification' })
  @ApiResponse({
    status: 201,
    description: 'System notification created successfully',
  })
  async createSystemNotification(
    @Body() createDto: CreateSystemNotificationDto,
  ) {
    const notification =
      await this.notificationService.createSystemNotification(createDto);
    return {
      status: 'success',
      message: 'System notification created successfully',
      data: notification,
    };
  }

  @Get('system')
  @ApiOperation({
    summary: 'Get system notifications with view count and status filter',
  })
  @ApiResponse({
    status: 200,
    description: 'System notifications retrieved successfully',
  })
  async getSystemNotifications(@Query() filterDto: AdminNotificationFilterDto) {
    const result =
      await this.notificationService.getSystemNotificationsWithViewCount(
        filterDto.page,
        filterDto.limit,
        filterDto.status,
      );
    return {
      status: 'success',
      message: 'System notifications retrieved successfully',
      ...result,
    };
  }

  @Put('system/:id')
  @ApiOperation({ summary: 'Update system notification (full update)' })
  @ApiResponse({
    status: 200,
    description: 'System notification updated successfully',
  })
  async updateSystemNotification(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: FullUpdateSystemNotificationDto,
  ) {
    const notification =
      await this.notificationService.updateSystemNotification(id, updateDto);
    return {
      status: 'success',
      message: 'System notification updated successfully',
      data: notification,
    };
  }

  @Put('system/:id/status')
  @ApiOperation({ summary: 'Update system notification status' })
  @ApiResponse({
    status: 200,
    description: 'System notification status updated successfully',
  })
  async updateSystemNotificationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateSystemNotificationDto,
  ) {
    const notification =
      await this.notificationService.updateSystemNotificationStatus(
        id,
        updateDto.status,
      );
    return {
      status: 'success',
      message: 'System notification status updated successfully',
      data: notification,
    };
  }

  @Get('system/:id')
  @ApiOperation({ summary: 'Get system notification detail' })
  @ApiResponse({
    status: 200,
    description: 'System notification retrieved successfully',
  })
  async getSystemNotificationDetail(@Param('id', ParseIntPipe) id: number) {
    const notification =
      await this.notificationService.getSystemNotificationDetailForAdmin(id);
    return {
      status: 'success',
      message: 'System notification retrieved successfully',
      data: notification,
    };
  }

  @Delete('system')
  @ApiOperation({
    summary: 'Delete multiple system notifications (soft delete)',
  })
  @ApiResponse({
    status: 200,
    description: 'System notifications deleted successfully',
  })
  async deleteSystemNotifications(@Body() deleteDto: DeleteNotificationsDto) {
    await this.notificationService.deleteSystemNotifications(deleteDto.ids);
    return {
      status: 'success',
      message: 'System notifications deleted successfully',
    };
  }

  // User Notification Endpoints
  @Post('user')
  @ApiOperation({ summary: 'Create user notification with recipient list' })
  @ApiResponse({
    status: 201,
    description: 'User notification created successfully',
  })
  async createUserNotification(@Body() createDto: CreateUserNotificationDto) {
    const notification =
      await this.notificationService.createUserNotification(createDto);
    return {
      status: 'success',
      message: 'User notification created successfully',
      data: notification,
    };
  }

  @Get('user')
  @ApiOperation({
    summary: 'Get user notifications with view count and status filter',
  })
  @ApiResponse({
    status: 200,
    description: 'User notifications retrieved successfully',
  })
  async getUserNotifications(@Query() filterDto: AdminNotificationFilterDto) {
    const result =
      await this.notificationService.getUserNotificationsWithViewCount(
        filterDto.page,
        filterDto.limit,
        filterDto.status,
      );
    return {
      status: 'success',
      message: 'User notifications retrieved successfully',
      ...result,
    };
  }

  @Put('user/:id')
  @ApiOperation({ summary: 'Update user notification (full update)' })
  @ApiResponse({
    status: 200,
    description: 'User notification updated successfully',
  })
  async updateUserNotification(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: FullUpdateUserNotificationDto,
  ) {
    const notification = await this.notificationService.updateUserNotification(
      id,
      updateDto,
    );
    return {
      status: 'success',
      message: 'User notification updated successfully',
      data: notification,
    };
  }

  @Put('user/:id/status')
  @ApiOperation({ summary: 'Update user notification status' })
  @ApiResponse({
    status: 200,
    description: 'User notification status updated successfully',
  })
  async updateUserNotificationStatus(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateUserNotificationDto,
  ) {
    const notification =
      await this.notificationService.updateUserNotificationStatus(
        id,
        updateDto.status,
      );
    return {
      status: 'success',
      message: 'User notification status updated successfully',
      data: notification,
    };
  }

  @Get('user/:id')
  @ApiOperation({ summary: 'Get user notification detail with recipients' })
  @ApiResponse({
    status: 200,
    description: 'User notification retrieved successfully',
  })
  async getUserNotificationDetail(@Param('id', ParseIntPipe) id: number) {
    const notification =
      await this.notificationService.getUserNotificationDetailForAdmin(id);
    return {
      status: 'success',
      message: 'User notification retrieved successfully',
      data: notification,
    };
  }

  @Delete('user')
  @ApiOperation({ summary: 'Delete multiple user notifications (soft delete)' })
  @ApiResponse({
    status: 200,
    description: 'User notifications deleted successfully',
  })
  async deleteUserNotifications(@Body() deleteDto: DeleteNotificationsDto) {
    await this.notificationService.deleteUserNotifications(deleteDto.ids);
    return {
      status: 'success',
      message: 'User notifications deleted successfully',
    };
  }
}
