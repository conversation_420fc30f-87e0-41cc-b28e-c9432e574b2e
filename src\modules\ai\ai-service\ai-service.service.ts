import { HttpService } from '@nestjs/axios';
import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import {
  EAiApiGenStateManagementType,
  EAiDirectionApi,
  EAiWebtoonCharacterType,
} from 'src/common/ai-webtoon.enum';
import {
  IAiWebtoonSyntheticDataSend,
  IGenerateSamplePromptsRequest,
  ISendCharacterTrainingSyncRequest,
  ISendGenerateCollectionImagesRequest,
  ISendGenerateDescriptionLabelByImagesRequest,
  ISendGeneratePromptByDescriptionRequest,
  ISendGenerateSceneImagesRequest,
  ISendInpaintRequest,
  ISendTestingDataRequest,
} from 'src/common/interfaces/ai-webtoon/ai-send-request.interface';
import { AI_MESSAGE_CONFIG, MESSAGE_CONFIG } from 'src/common/message.config';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { AiApiGenStateManagementService } from '../ai-api-gen-state-management/ai-api-gen-state-management.service';
import { MergePreviewImagesDto } from '../ai-webtoon-chapter/dto/merge-preview-images.dto';
import { CombineImagesDto } from '../ai-webtoon-cut-chapter/dto/combine-image.dto';
import { GenerateSketchImageDto } from '../ai-webtoon-cut-chapter/dto/generate-sketch-image.dto';
import { AiWebtoonSyntheticDataType } from '../ai-webtoon-synthetic-data/dto/add-images.dto';

@Injectable()
export class AiService {
  private readonly logger = new Logger(AiService.name);

  constructor(
    private readonly httpService: HttpService,
    private readonly aiApiGenStateManagementService: AiApiGenStateManagementService,
    private readonly notificationGoogleService: NotificationGoogleService,
  ) {}

  /**
   * Send inpaint image request
   */
  async sendInpaintRequest(
    data: ISendInpaintRequest,
  ): Promise<{ apiName: string }> {
    const apiItem =
      await this.aiApiGenStateManagementService.findOneApiGenByType(
        EAiApiGenStateManagementType.INPAINT_IMAGE,
      );

    const payload = {
      ...data,
      apiName: apiItem.name,
    };

    this.logger.debug(
      `🚀 inpaintImage ${apiItem.name}  ~ data >> `,
      JSON.stringify(payload),
    );

    const apiUrl = `${process.env[apiItem.fieldInEnv]}/inpaint`;

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, payload),
      );

      this.logger.log(
        `🚀 inpaintImage ${apiItem.name} ~ result >> `,
        JSON.stringify(response?.data),
      );

      await Promise.all([
        this.aiApiGenStateManagementService.updateItemsByNames(
          [apiItem.name],
          true,
          new Date(),
        ),
        this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
          {
            apiName: apiItem.name,
            apiUrl,
            directionApi: EAiDirectionApi.SENT,
            event: `Send Inpaint Image Request - Cut ${data.cutId}`,
            data: payload,
            isSuccess: true,
          },
        ),
      ]);

      return { apiName: apiItem.name };
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_INPAINT_IMAGE_FAILED.message;
      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiName: apiItem.name,
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Inpaint Image Request Failed - Cut ${data.cutId}`,
          data: payload,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_INPAINT_IMAGE_FAILED.key,
        message: errorDescription,
      });
    } finally {
      // await this.aiApiGenStateManagementService.removeLockedApiName(
      //   apiItem.name,
      // );
    }
  }

  /**
   * Send stop service request
   */
  async sendStopServiceRequest(apiName: string) {
    const apiItem =
      await this.aiApiGenStateManagementService.findOneByName(apiName);

    const apiUrl = `${process.env[apiItem.fieldInEnv]}/interrupt`;

    try {
      const response: any = await firstValueFrom(this.httpService.post(apiUrl));

      this.logger.log(
        `🚀 stopService ${apiItem.type} apiName:: ${apiItem.name} ~ result >> `,
        JSON.stringify(response?.data),
      );

      await Promise.all([
        this.aiApiGenStateManagementService.updateItemsByNames(
          [apiItem.name],
          false,
          null,
        ),
        this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
          {
            apiName,
            apiUrl,
            directionApi: EAiDirectionApi.SENT,
            event: `Send Stop Service Request`,
            data: {},
            isSuccess: true,
          },
        ),
      ]);

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        MESSAGE_CONFIG.SERVICE_STOP_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiName,
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Stop Service Request Failed`,
          data: {},
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: MESSAGE_CONFIG.SERVICE_STOP_FAILED.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send generate prompt by description request
   */
  async sendGeneratePromptByDescriptionRequest(
    data: ISendGeneratePromptByDescriptionRequest,
  ) {
    const apiUrl = `${process.env.AI_GENERATE_PROMPT_BY_DESCRIPTION_URL}/generate-tags`;

    this.logger.debug(
      `🚀 generatePromptByDescription: >> `,
      JSON.stringify(data),
    );

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        `🚀 generatePromptByDescription ~ result >> `,
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Prompt By Description Request - Cut IDs: ${data.data
            .map((item) => item.cutId)
            .join(',')}`,
          data,
          isSuccess: true,
        },
      );

      return response.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_GENERATE_PROMPT_BY_DESCRIPTION_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Prompt By Description Request Failed - Cut IDs: ${data.data
            .map((item) => item.cutId)
            .join(',')}`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_GENERATE_PROMPT_BY_DESCRIPTION_FAILED.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send generate description label by images request
   */
  async sendGenerateDescriptionLabelByImagesRequest(
    data: ISendGenerateDescriptionLabelByImagesRequest,
  ) {
    this.logger.log(
      '🚀 generateDescriptionLabelByImages ~ data >> ',
      JSON.stringify(data),
    );

    const apiUrl = `${process.env.AI_LLM_URL}/image-description-gpt`;

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        '🚀 generateDescriptionLabelByImages ~ result >> ',
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Description Label By Images Request - ${
            data.image_links?.length || 0
          } Image(s)`,
          data,
          isSuccess: true,
        },
      );

      return response?.data?.tags_strs.map((e) => ({
        url: e.image_link,
        prompt: e.tag,
      }));
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_CHARACTER_GENERATE_DESCRIPTION_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Description Label By Images Request Failed - ${
            data.image_links?.length || 0
          } Image(s)`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_CHARACTER_GENERATE_DESCRIPTION_FAILED.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send character training sync request
   */
  async sendCharacterTrainingSyncRequest(
    data: ISendCharacterTrainingSyncRequest,
  ) {
    const apiUrl =
      data.characterType === EAiWebtoonCharacterType.SDXL
        ? `${process.env.AI_TRAINING_SDXL_URL}/sync`
        : `${process.env.AI_TRAINING_FLUX_URL}/sync`;

    this.logger.debug(
      `🚀 applyModelTraining ${data.characterType} >> `,
      JSON.stringify(data),
    );

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        `🚀 applyModelTraining ${data.characterType} >> `,
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Apply Model Training Request - Character ${data.modelId} - Type: ${data.characterType}`,
          data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_CHARACTER_FAILED_TO_APPLY_MODEL_TRAINING.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Apply Model Training Request Failed - Character ${data.modelId} - Type: ${data.characterType}`,
          data: data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_CHARACTER_FAILED_TO_APPLY_MODEL_TRAINING.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send generate cut images request
   */
  async sendGenerateCutImagesByPrepareCharacterRequest(data: {
    image: string;
    aiWebtoonPrepareCharacterGeneratedId: number;
  }) {
    this.logger.debug(
      '🚀 sendGenerateCutImagesByPrepareCharacterRequest ~ data >> ',
      JSON.stringify(data),
    );
    const apiUrl = `${process.env.AI_CUT_IMAGE_BY_PREPARE_CHARACTER_URL}/detect-crop-person`;
    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        '🚀 sendGenerateCutImagesByPrepareCharacterRequest ~ result >> ',
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Cut Images By Prepare Character Request - Generated ID ${data.aiWebtoonPrepareCharacterGeneratedId}`,
          data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_GENERATE_CUT_IMAGES_BY_PREPARE_CHARACTER_FAILED
          .message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Cut Images By Prepare Character Request Failed - Generated ID ${data.aiWebtoonPrepareCharacterGeneratedId}`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG
          .AI_GENERATE_CUT_IMAGES_BY_PREPARE_CHARACTER_FAILED.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send generate collection images request
   */
  async sendGenerateCollectionImagesRequest(
    data: ISendGenerateCollectionImagesRequest,
  ) {
    this.logger.debug(
      '🚀 generateCollectionImages ~ data >> ',
      JSON.stringify(data),
    );

    const apiUrl = `${process.env.AI_CREATE_CHARACTER_URL}/create-character-sheet`;
    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        '🚀 generateCollectionImages ~ result >> ',
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Collection Images Request - ${
            data.numberOfImages
          } Image(s)${
            data.characterId ? ` - Character ${data.characterId}` : ''
          }`,
          data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_GENERATE_COLLECTION_IMAGES_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Collection Images Request Failed - ${
            data.numberOfImages
          } Image(s)${
            data.characterId ? ` - Character ${data.characterId}` : ''
          }`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_GENERATE_COLLECTION_IMAGES_FAILED.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send generate scene images request
   */
  async sendGenerateSceneImagesRequest(data: ISendGenerateSceneImagesRequest) {
    const apiItem =
      await this.aiApiGenStateManagementService.findOneApiGenByType(
        EAiApiGenStateManagementType.GEN_BACKGROUND_IMAGE,
      );

    const payload = {
      ...data,
      apiName: apiItem.name,
    };

    this.logger.debug(
      `🚀 generateSceneImages ${apiItem.name} ~ data >> `,
      JSON.stringify(payload),
    );

    const apiUrl = `${process.env[apiItem.fieldInEnv]}/generate`;

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, payload),
      );

      this.logger.log(
        `🚀 generateSceneImages ${data.apiName} ~ result >> `,
        JSON.stringify(response?.data),
      );

      await Promise.all([
        this.aiApiGenStateManagementService.updateItemsByNames(
          [apiItem.name],
          true,
          new Date(),
        ),
        this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
          {
            apiUrl,
            apiName: data.apiName,
            directionApi: EAiDirectionApi.SENT,
            event: `Send Generate Scene Images Request - Scene ${data.sceneId}`,
            data: payload,
            isSuccess: true,
          },
        ),
      ]);

      return { apiName: data.apiName };
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_GENERATE_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          apiName: data.apiName,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Scene Images Request Failed - Scene ${data.sceneId}`,
          data: payload,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_GENERATE_FAILED.key,
        message: errorDescription,
      });
    } finally {
      // await this.aiApiGenStateManagementService.removeLockedApiName(
      //   apiItem.name,
      // );
    }
  }

  /**
   * Send combine cut images request
   */
  async sendCombineImagesRequest(data: CombineImagesDto) {
    this.logger.debug('🚀 combineCutImages ~ data >> ', JSON.stringify(data));

    const apiUrl = `${process.env.AI_COMBINE_IMAGES_URL}/combine`;

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        '🚀 combineImages ~ result >> ',
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Combine Images Request`,
          data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_COMBINE_IMAGES_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Combine Images Request Failed`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_COMBINE_IMAGES_FAILED.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send merge preview images request
   */
  async sendMergePreviewImagesRequest(data: MergePreviewImagesDto) {
    this.logger.log(`mergePreviewImages ~ body >> ${JSON.stringify(data)}`);

    const apiUrl = `${process.env.AI_MERGE_PREVIEW_IMAGES_URL}/merge-upload`;

    try {
      const response = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        `🚀 mergePreviewImages ~ result >> `,
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Merge Preview Images Request - Chapter ${data.chapterId} - ${data.images.length} Image(s)`,
          data: data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_CHAPTER_FAILED_TO_MERGE_PREVIEW_IMAGES.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Merge Preview Images Request Failed - Chapter ${data.chapterId} - ${data.images.length} Image(s)`,
          data: data,
          isSuccess: false,
          errorDescription: errorMessage,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_CHAPTER_FAILED_TO_MERGE_PREVIEW_IMAGES.key,
        message: errorMessage,
      });
    }
  }

  /**
   * Send generate sketch image request
   */
  async sendGenerateSketchImageRequest(data: GenerateSketchImageDto) {
    this.logger.debug(
      '🚀 generateSketchImage ~ data >> ',
      JSON.stringify(data),
    );

    const apiUrl = `${process.env.AI_SKETCH_URL}/sketch_image`;

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        '🚀 generateSketchImage ~ result >> ',
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Sketch Image Request - Image: ${data.image}`,
          data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_SKETCH_IMAGE_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Sketch Image Request Failed - Image: ${data.image}`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_SKETCH_IMAGE_FAILED.key,
        message: errorDescription,
      });
    }
  }

  /**
   * Send generate sample prompts request
   */
  async sendGenerateSamplePromptsRequest(data: IGenerateSamplePromptsRequest) {
    this.logger.debug(
      '🚀 generateSamplePrompts ~ data >> ',
      JSON.stringify(data),
    );

    const apiUrl = `${process.env.AI_LLM_URL}/gen-prompts`;

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        '🚀 generateSamplePrompts ~ result >> ',
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Sample Prompts Request - ${data.countResult} Result(s)`,
          data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_CHARACTER_GENERATE_SAMPLE_PROMPT_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Generate Sample Prompts Request Failed - ${data.countResult} Result(s)`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_CHARACTER_GENERATE_SAMPLE_PROMPT_FAILED.key,
        message: errorDescription,
      });
    }
  }

  async sendTestingCharacterRequest(
    apiUrl: string,
    data: ISendTestingDataRequest,
  ) {
    try {
      this.logger.debug(
        `🚀 sendTestingCharacterRequest ~ apiUrl :: ${apiUrl}  ~ data >> `,
        JSON.stringify(data),
      );

      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, data),
      );

      this.logger.log(
        '🚀 sendTestingCharacterRequest ~ result >> ',
        JSON.stringify(response?.data),
      );

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Testing Character Request`,
          data,
          isSuccess: true,
        },
      );

      return response?.data;
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_CHARACTER_TESTING_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Send Testing Character Request Failed`,
          data,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_CHARACTER_TESTING_FAILED.key,
        message: errorDescription,
      });
    }
  }

  async sendGenerateSyntheticDataImagesRequest(
    data: IAiWebtoonSyntheticDataSend,
  ): Promise<{ apiName: string }> {
    const apiItem =
      await this.aiApiGenStateManagementService.findOneApiGenByType(
        EAiApiGenStateManagementType.GEN_IMAGE_FLUX,
      );

    const payload: IAiWebtoonSyntheticDataSend = {
      ...data,
      apiName: apiItem.name,
    };

    this.logger.debug(
      `🚀 sendGenerateSyntheticDataImagesRequest ~ ${apiItem.name} ~ data >> `,
      JSON.stringify(payload),
    );

    const apiUrl =
      data.type === AiWebtoonSyntheticDataType.FluxContextImages
        ? `${process.env[apiItem.fieldInEnv]}/kontext`
        : `${process.env[apiItem.fieldInEnv]}/style_transfer`;

    try {
      const response: any = await firstValueFrom(
        this.httpService.post(apiUrl, payload),
      );

      const result = response?.data;

      this.logger.log(
        `🚀 sendGenerateSyntheticDataImagesRequest ${apiItem.name} ~ result >> `,
        JSON.stringify(result),
      );

      await Promise.all([
        this.aiApiGenStateManagementService.updateItemsByNames(
          [apiItem.name],
          true,
          new Date(),
        ),
        this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
          {
            apiName: apiItem.name,
            apiUrl,
            directionApi: EAiDirectionApi.SENT,
            event: `Synthetic Data - Send Generate Images Request - ${data.syntheticDataId}`,
            data: payload,
            isSuccess: true,
          },
        ),
      ]);

      return { apiName: apiItem.name };
    } catch (error) {
      const errorDescription =
        error.response?.data?.message ||
        error.message ||
        AI_MESSAGE_CONFIG.AI_SYNTHETIC_GENERATE_DATA_FAILED.message;

      await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
        {
          apiName: apiItem.name,
          apiUrl,
          directionApi: EAiDirectionApi.SENT,
          event: `Synthetic Data - Send Generate Images Request Failed - ${data.syntheticDataId}`,
          data: payload,
          isSuccess: false,
          errorDescription,
        },
      );

      throw new BadRequestException({
        key: AI_MESSAGE_CONFIG.AI_SYNTHETIC_GENERATE_DATA_FAILED.key,
        message: errorDescription,
      });
    } finally {
      // await this.aiApiGenStateManagementService.removeLockedApiName(
      //   apiItem.name,
      // );
    }
  }
}
