import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';
import { AiWebtoonCharacterService } from './ai-webtoon-character.service';
import { CharacterTrainingDto } from './dto/character-training.dto';
import { CreateAiWebtoonCharacterDto } from './dto/create-ai-webtoon-character.dto';
import { GenerateDescriptionLabelByImagesDto } from './dto/generate-description-label-by-images.dto';
import { GetSampleTestingDto } from './dto/get-sample-testing.dto';
import {
  ListAiWebtoonCharacterDto,
  ListAllAiWebtoonCharacterDto,
} from './dto/list-ai-character.dto';
import { ListTrainingSessionByCharacterDto } from './dto/list-training-session-by-character.dto';
import { ReceiveLogCharacterTrainingDto } from './dto/receive-log-character-training.dto';
import {
  ReceiveLogModelSyncSampleDto,
  ReceiveLogModelSyncUrlModelDto,
} from './dto/receive-log-model-sync.dto';
import { ReceiveSyncedCharacterDto } from './dto/receive-synced-character.dto';
import { ReceiveTestingDto } from './dto/receive-testing.dto';
import {
  CreateSamplePromptDto,
  DeleteSamplePromptDto,
} from './dto/sample-prompts.dto';
import {
  CharacterTestingDto,
  SaveCharacterTestingDto,
} from './dto/save-character-testing.dto';

@ApiTags('ai-webtoon-character')
@Controller('ai-webtoon-character')
export class AiWebtoonCharacterController {
  constructor(
    private readonly aiWebtoonCharacterService: AiWebtoonCharacterService,
  ) {}

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/all')
  listAll(@Query() dto: ListAllAiWebtoonCharacterDto) {
    return this.aiWebtoonCharacterService.listAllByType(dto.type);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/all-have-been-trained-by-type')
  listAllHaveBeenTrainedByType(@Query() dto: ListAllAiWebtoonCharacterDto) {
    return this.aiWebtoonCharacterService.listAllHaveBeenTrained(dto.type);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('list-training-session-by-character')
  listTrainingSessionByCharacter(
    @Query() dto: ListTrainingSessionByCharacterDto,
  ) {
    return this.aiWebtoonCharacterService.listTrainingSessionByCharacter(dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('character-tested')
  listCharacterTested(@Query() dto: ListAiWebtoonCharacterDto) {
    return this.aiWebtoonCharacterService.listCharacterTested(dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('get-sample-prompts-by-character/:id')
  getSamplePromptsByCharacter(
    @Param('id', ParseIntPipe) id: number,
    @Query() dto: GetSampleTestingDto,
  ) {
    return this.aiWebtoonCharacterService.getSamplePromptsByCharacter(id, dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/generate-description-label-by-images')
  generateDescriptionLabelByImages(
    @Body() body: GenerateDescriptionLabelByImagesDto,
  ) {
    return this.aiWebtoonCharacterService.generateDescriptionLabelByImages(
      body,
    );
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/order-training')
  orderTraining(@Body() body: CharacterTrainingDto) {
    return this.aiWebtoonCharacterService.orderTraining(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/cancel-order-training/:id')
  cancelOrderTraining(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCharacterService.cancelOrderTraining(id);
  }

  @Post('/receive-log-training')
  receiveLogTraining(@Body() body: ReceiveLogCharacterTrainingDto) {
    return this.aiWebtoonCharacterService.receiveLogTraining(body);
  }

  @Post('/receive-log-model-sync-sample')
  receiveLogModelSyncSample(@Body() body: ReceiveLogModelSyncSampleDto) {
    return this.aiWebtoonCharacterService.receiveLogModelSyncSample(body);
  }

  @Post('/receive-log-model-url')
  receiveLogModelUrl(@Body() body: ReceiveLogModelSyncUrlModelDto) {
    return this.aiWebtoonCharacterService.receiveLogModelUrl(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/list-log-model-training-by-character/:id')
  listLogModelTrainingByCharacter(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCharacterService.listLogModelTrainingByCharacter(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/apply-model-training/:aiWebtoonTrainingLogModelId')
  applyModelTraining(
    @Param('aiWebtoonTrainingLogModelId', ParseIntPipe)
    aiWebtoonTrainingLogModelId: number,
  ) {
    return this.aiWebtoonCharacterService.applyModelTraining(
      aiWebtoonTrainingLogModelId,
    );
  }

  @Post('/receive-end-training/:id')
  receiveEndTraining(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCharacterService.receiveEndTraining(id);
  }

  @Post('/receive-synced')
  receiveSynced(@Body() body: ReceiveSyncedCharacterDto) {
    return this.aiWebtoonCharacterService.receiveSynced(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/testing')
  testing(@Body() body: CharacterTestingDto) {
    return this.aiWebtoonCharacterService.testing(body);
  }

  @Post('/receive-testing')
  receiveTesting(@Body() body: ReceiveTestingDto) {
    return this.aiWebtoonCharacterService.receiveTesting(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('images-testing-by-uuid/:uuid')
  imagesTestingByUuid(@Param('uuid') uuid: string) {
    return this.aiWebtoonCharacterService.imagesTestingByUuid(uuid);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/save-testing')
  saveTesting(@Body() body: SaveCharacterTestingDto, @Req() req) {
    return this.aiWebtoonCharacterService.saveTesting(body, req.user.id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post('/sample-prompts/:id')
  createSamplePrompt(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: CreateSamplePromptDto,
  ) {
    return this.aiWebtoonCharacterService.createSamplePrompt(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/sample-prompts/:id')
  deleteSamplePrompt(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: DeleteSamplePromptDto,
  ) {
    return this.aiWebtoonCharacterService.deleteSamplePrompt(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get()
  list(@Query() dto: ListAiWebtoonCharacterDto) {
    return this.aiWebtoonCharacterService.list(dto);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Get('/:id')
  detail(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCharacterService.detail(id);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Post()
  create(@Body() body: CreateAiWebtoonCharacterDto) {
    return this.aiWebtoonCharacterService.create(body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Put('/:id')
  update(
    @Body() body: CreateAiWebtoonCharacterDto,
    @Param('id', ParseIntPipe) id: number,
  ) {
    return this.aiWebtoonCharacterService.update(id, body);
  }

  @ApiBearerAuth()
  @UseGuards(JwtAdminAuthGuard)
  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.aiWebtoonCharacterService.delete(id);
  }
}
