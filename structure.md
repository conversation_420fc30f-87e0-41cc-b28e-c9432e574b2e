# NestJS Digital Content Platform - Project Structure

## 📋 Project Overview

This is a **professional-grade digital content platform** (webtoon/manga/comic platform) built with **NestJS** featuring subscription-based monetization, content management, and comprehensive analytics system. The platform is designed for episodic content delivery with robust payment processing and admin management capabilities.

## 🏗️ Architecture Overview

```
nestjs-base/
├── 📁 src/                          # Source code directory
│   ├── 📁 modules/                  # Feature modules
│   │   ├── 📁 admin/               # Admin panel modules (Protected)
│   │   ├── 📁 analytics/           # Real-time analytics system
│   │   ├── 📁 auth/                # User authentication
│   │   ├── 📁 content/             # Content browsing & viewing
│   │   ├── 📁 faq/                 # FAQ system (user-facing)
│   │   ├── 📁 legal-document/      # Legal documents (Terms/Privacy)
│   │   ├── 📁 notification/        # Notification system
│   │   ├── 📁 payment/             # Payment processing
│   │   ├── 📁 setting/             # Settings management
│   │   ├── 📁 support/             # Support & Q&A system
│   │   └── 📁 user/                # User profile management
│   ├── 📁 database/                # Database layer
│   │   ├── 📁 entities/            # TypeORM entities
│   │   ├── 📁 repositories/        # Custom repositories
│   │   ├── 📁 migrations/          # Database migrations
│   │   └── 📁 seeders/             # Database seeders
│   ├── 📁 common/                  # Shared utilities
│   ├── 📁 config/                  # Configuration files
│   └── 📄 main.ts                  # Application entry point
├── 📁 mail-templates/              # Email templates (Handlebars)
├── 📁 uploads/                     # File uploads directory
├── 📄 docker-compose-dev.yml       # Development environment
├── 📄 docker-compose.yml           # Production environment
└── 📄 package.json                 # Dependencies & scripts
```

## 🛠️ Technology Stack

### Core Technologies
- **NestJS** (v11.0.1) - Node.js framework with TypeScript
- **TypeORM** (v0.3.24) - Database ORM with migration support
- **MySQL** (v8.0) - Primary database
- **Redis** - Caching, session management, and BullMQ backend
- **BullMQ** - Queue processing for real-time analytics

### Authentication & Security
- **JWT** (@nestjs/jwt) - Token-based authentication (dual strategies)
- **Passport** - Authentication strategies
- **bcrypt** - Password hashing
- **Role-based Access Control** - Menu-based permissions

### Additional Technologies
- **Swagger/OpenAPI** - API documentation
- **Class Validator/Transformer** - DTO validation
- **Nodemailer** - Email functionality with Handlebars templates
- **Docker** - Containerization
- **DOMPurify** - HTML sanitization for notifications

## 📁 Module Structure & Functionality

### 🔐 Admin Modules (`src/modules/admin/`)
**Purpose**: Complete admin panel with role-based access control for platform management.

#### 🔑 `auth-admin/` - Admin Authentication
**Files**:
- `auth-admin.controller.ts` - Admin login/logout endpoints
- `auth-admin.service.ts` - Admin authentication logic
- `guards/jwt-admin-auth.guard.ts` - JWT guard for admin routes
- `guards/menu-permission.guard.ts` - Menu-based permission control
- `decorators/menu-permission.decorator.ts` - Permission decorator

**Functionality**:
- Separate JWT strategy for admin authentication
- Menu-based role permissions system
- Admin session management
- Password reset for admin accounts

#### 📊 `content-management/` - Content CRUD Operations
**Files**:
- `content-management.controller.ts` - Content, episode, author, genre management
- `content-management.service.ts` - Business logic for content operations
- `services/content-validation.service.ts` - Content validation rules
- `services/episode-image.service.ts` - Episode image processing

**Functionality**:
- Create/Read/Update/Delete content series
- Episode management with image uploads
- Author and genre management
- Content settings and metadata
- Multi-language content support

#### 💳 `payment-management/` - Payment & Subscription Management
**Files**:
- `controllers/payment.controller.ts` - Payment administration
- `controllers/subscription-plan.controller.ts` - Subscription plan management
- `controllers/subscription.controller.ts` - User subscription management
- `services/payment.service.ts` - Payment processing logic

**Functionality**:
- Subscription plan creation and management
- Payment transaction oversight
- Revenue analytics and reporting
- Discount and pricing management
- Payment provider integration

#### 📈 `content-stats/` - Analytics Dashboard
**Files**:
- `content-stats.controller.ts` - Analytics API endpoints
- `content-stats.service.ts` - Analytics data processing
- `dtos/content-stats-response.dto.ts` - Analytics response structures

**Functionality**:
- Content consumption analytics
- Revenue analytics by content
- User engagement metrics
- Date range and language filtering
- Adult vs non-adult content breakdown

#### 👥 `user-admin/` - User Administration
**Files**:
- `user-admin.controller.ts` - User management endpoints
- `dtos/user-sanction-request.dto.ts` - User sanction management

**Functionality**:
- User account management
- User sanctions and restrictions
- User activity monitoring
- Account verification and moderation

#### 🔧 `setting/` - Platform Configuration
**Files**:
- `setting.controller.ts` - Settings management endpoints
- `setting.service.ts` - Settings business logic

**Functionality**:
- Platform-wide configuration
- Language and localization settings
- Content classification settings
- System metadata management

#### 🎛️ `menu/` - Admin Menu Management
**Files**:
- `menu.controller.ts` - Menu management endpoints
- `menu.service.ts` - Menu logic and permissions

**Functionality**:
- Dynamic admin menu creation
- Permission-based menu visibility
- Menu hierarchy management

#### 🆘 `support-admin/` - Support System Administration
**Files**:
- `support-admin.controller.ts` - Support ticket management
- `support-category-admin.controller.ts` - Support category management
- `qa-admin.controller.ts` - Q&A system administration
- `services/support-admin.service.ts` - Support ticket processing
- `services/support-category-admin.service.ts` - Category management

**Functionality**:
- Support ticket management and assignment
- Q&A system administration
- Auto-reply configuration
- Support analytics and reporting
- Category and prioritization management

#### ❓ `faq-admin/` - FAQ Management
**Files**:
- `faq-admin.controller.ts` - FAQ CRUD operations
- `faq-admin.service.ts` - FAQ business logic

#### ⚖️ `legal-admin/` - Legal Document Management
**Files**:
- `legal-admin.controller.ts` - Terms of Use and Privacy Policy CRUD operations
- `legal-admin.service.ts` - Legal document business logic with HTML sanitization
- `dtos/create-legal-document.dto.ts` - Legal document creation validation
- `dtos/update-legal-document.dto.ts` - Legal document update validation
- `dtos/legal-document-filter.dto.ts` - Legal document filtering and pagination
- `dtos/delete-legal-documents.dto.ts` - Bulk delete validation

**Functionality**:
- Create, read, update, delete legal documents (Terms of Use, Privacy Policy)
- HTML content sanitization with DOMPurify
- Version management and status control (active/inactive)
- Name uniqueness validation and search functionality
- View count tracking and analytics integration
- Bulk operations and soft delete support

**Functionality**:
- Create, read, update, delete FAQs
- HTML content management with DOMPurify sanitization
- Category-based organization using support categories
- Advanced filtering (date range, category, status, search)
- Bulk operations (soft delete multiple FAQs)
- Status management (active/inactive)
- Display order management

### 📊 Analytics Module (`src/modules/analytics/`)
**Purpose**: Real-time analytics system using BullMQ queues for high-performance data processing.

**Files**:
- `analytics-queue.module.ts` - BullMQ configuration
- `services/analytics-queue.service.ts` - Analytics event dispatcher
- `processors/content-analytics.processor.ts` - Content view processing
- `processors/payment-analytics.processor.ts` - Payment analytics processing
- `processors/user-analytics.processor.ts` - User activity processing
- `services/exchange-rate.service.ts` - Currency conversion service

**Functionality**:
- **Real-time Analytics**: BullMQ-powered asynchronous processing
- **Content Analytics**: View tracking, unique viewers, reading time
- **Payment Analytics**: Revenue tracking with KRW conversion, purchase rates
- **User Analytics**: Activity tracking, platform usage, behavior analysis
- **Performance Optimization**: Redis backend, batch processing

### 🔐 User Authentication (`src/modules/auth/`)
**Files**:
- `auth.controller.ts` - User authentication endpoints
- `auth.service.ts` - Authentication business logic
- `strategy/jwt.strategy.ts` - User JWT strategy
- `guards/jwt-auth.guard.ts` - User JWT guard
- `guards/optional-jwt-auth.guard.ts` - Optional authentication

**Functionality**:
- User registration with email verification
- JWT-based authentication
- Password reset functionality
- Session management with Redis
- Optional authentication for public content

### 📖 Content Module (`src/modules/content/`)
**Files**:
- `content.controller.ts` - Content browsing and viewing
- `dtos/get-content-list.dto.ts` - Content filtering and pagination

**Functionality**:
- Content browsing and search
- Episode viewing with payment validation
- Recently watched tracking
- Content recommendations
- Free vs premium content access

### 💰 Payment Module (`src/modules/payment/`)
**Files**:
- `payment.controller.ts` - Payment processing endpoints
- `dtos/create-payment-intent.dto.ts` - Payment creation

**Functionality**:
- Payment intent creation
- Subscription management
- Payment provider integration (Stripe, PayPal ready)
- Transaction tracking and analytics

### 🔔 Notification Module (`src/modules/notification/`)
**Files**:
- `controllers/notification.controller.ts` - Notification endpoints
- `services/notification.service.ts` - Notification logic
- `dtos/create-system-notification.dto.ts` - System notification creation
- `dtos/create-user-notification.dto.ts` - User-specific notifications

**Functionality**:
- **System Notifications**: Global announcements to all users
- **User Notifications**: Targeted notifications with time-based display
- **HTML Content Support**: Rich content with sanitization
- **View Tracking**: Mark notifications as read/unread
- **Time-based Display**: Unix timestamp control for display periods

### 🆘 Support Module (`src/modules/support/`)
**Files**:
- `support.controller.ts` - Support ticket creation and management
- `qa.controller.ts` - Q&A system for users
- `services/support.service.ts` - Support ticket logic
- `services/qa-auto-reply.service.ts` - Automated response system
- `services/support-email.service.ts` - Email notification service

**Functionality**:
- Support ticket creation and tracking
- Q&A system with auto-reply capability
- User follow-up on support tickets
- Email notifications for support interactions
- Integration with admin support management

### ❓ FAQ Module (`src/modules/faq/`)
**Files**:
- `faq.controller.ts` - Public FAQ endpoints
- `faq.service.ts` - FAQ business logic for users
- `dtos/faq-list-query.dto.ts` - FAQ filtering and pagination
- `dtos/faq-response.dto.ts` - FAQ response structures

**Functionality**:
- Browse active FAQs with category filtering
- FAQ detail view with automatic view count increment
- Analytics integration for FAQ view tracking
- Public access (no authentication required)
- Pagination support for large FAQ lists
- Integration with support category system

### ⚖️ Legal Document Module (`src/modules/legal-document/`)
**Files**:
- `legal-document.controller.ts` - Public legal document access
- `legal-document.service.ts` - User-facing legal document operations
- `dtos/user-legal-document-filter.dto.ts` - User filtering options

**Functionality**:
- Public access to Terms of Use and Privacy Policy
- Document filtering by type (terms_of_use/privacy_policy)
- View count tracking with atomic increment
- Analytics integration for authenticated users (legal_document_view events)
- Optional authentication (public access with enhanced features for logged-in users)

### 👤 User Module (`src/modules/user/`)
**Files**:
- `user.controller.ts` - User profile management
- `user.service.ts` - User profile logic
- `dtos/update-profile.dto.ts` - Profile update validation

**Functionality**:
- User profile management
- Preference settings
- Viewing history
- Account settings

## 🗄️ Database Structure

### Core Entities (`src/database/entities/`)

#### User Management
- **`user.entity.ts`** - User accounts with subscription and payment history
- **`session.entity.ts`** - User session management with Redis integration
- **`sanction.entity.ts`** - User sanctions and restrictions
- **`admin.entity.ts`** - Admin accounts with role-based permissions

#### Content Management
- **`content.entity.ts`** - Main content items (series/comics) with metadata
- **`episode.entity.ts`** - Individual episodes with payment models
- **`episode_image.entity.ts`** - Episode image assets
- **`author.entity.ts`** - Content creators and authors
- **`genre.entity.ts`** - Content categorization
- **`content_setting.entity.ts`** - Content configuration and metadata
- **`display.entity.ts`** - Content display settings

#### Payment & Subscription System
- **`payment.entity.ts`** - Payment transactions with provider integration
- **`subscription.entity.ts`** - User subscription instances
- **`subscription_plan.entity.ts`** - Subscription plans with pricing tiers
- **`user_content.entity.ts`** - User content access and viewing history

#### Analytics System
- **`content-analytics.entity.ts`** - Content view tracking with hourly aggregation
- **`user-analytics.entity.ts`** - User activity and behavior tracking
- **`payment-analytics.entity.ts`** - Payment events and revenue analytics
- **`content-statistics-cache.entity.ts`** - Cached analytics for performance
- **`content-ranking-stats.entity.ts`** - Content ranking and popularity metrics
- **`exchange-rate.entity.ts`** - Currency exchange rates for payment analytics

#### Notification System
- **`system-notification.entity.ts`** - Global notifications for all users
- **`user-notification.entity.ts`** - Targeted notifications for specific users
- **`user-notification-recipient.entity.ts`** - Junction table for user notifications
- **`notification-view.entity.ts`** - Notification view tracking

#### Support & Q&A System
- **`support-ticket.entity.ts`** - Support ticket management
- **`support-response.entity.ts`** - Support responses and interactions
- **`support-category.entity.ts`** - Support ticket categorization
- **`faq.entity.ts`** - Frequently asked questions with HTML content and view tracking
- **`legal-document.entity.ts`** - Terms of Use and Privacy Policy with version management and view tracking

#### Settings & Configuration
- **`setting.entity.ts`** - Platform-wide settings
- **`setting_metadata.entity.ts`** - Setting metadata and validation
- **`menu.entity.ts`** - Admin menu structure
- **`menu-admin.entity.ts`** - Admin menu permissions

### Database Relationships

```mermaid
erDiagram
    USER ||--o{ SUBSCRIPTION : has
    USER ||--o{ PAYMENT : makes
    USER ||--o{ USER_CONTENT : tracks
    USER ||--o{ SUPPORT_TICKET : creates
    
    CONTENT ||--o{ EPISODE : contains
    CONTENT ||--o{ CONTENT_SETTING : has
    CONTENT }o--|| AUTHOR : created_by
    CONTENT }o--|| GENRE : belongs_to
    
    EPISODE ||--o{ EPISODE_IMAGE : has
    EPISODE ||--o{ USER_CONTENT : tracked_in
    
    SUBSCRIPTION_PLAN ||--o{ SUBSCRIPTION : used_in
    PAYMENT }o--|| SUBSCRIPTION : pays_for
    
    SUPPORT_TICKET }o--|| SUPPORT_CATEGORY : categorized_by
    SUPPORT_TICKET ||--o{ SUPPORT_RESPONSE : has
    
    FAQ }o--|| SUPPORT_CATEGORY : categorized_by
    FAQ }o--|| ADMIN : created_by
    
    ADMIN ||--o{ MENU_ADMIN : has_access
    MENU ||--o{ MENU_ADMIN : grants_access
```

## 🔧 Configuration & Setup

### Environment Configuration
```env
# Database
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=your_password
DB_DATABASE=nestjs_content_platform

# Redis (BullMQ & Sessions)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# JWT
JWT_SECRET=your_jwt_secret
JWT_ADMIN_SECRET=your_admin_jwt_secret

# Email
MAIL_HOST=smtp.gmail.com
MAIL_USER=your_email
MAIL_PASSWORD=your_app_password

# Admin Configuration
ADMIN_EMAIL=["<EMAIL>"]
CLIENT_ADMIN_DOMAIN=https://admin.platform.com
```

### Development Commands
```bash
# Development
npm install
npm run start:dev

# Database
npm run migration:run
npm run seed:run

# Testing
npm run test
npm run test:e2e

# Production
npm run build
npm run start:prod

# Docker
docker-compose -f docker-compose-dev.yml up --build
```

## 🚀 Key Features & Capabilities

### Multi-tenant Content System
- **Flexible Payment Models**: Free, pay-per-episode, subscription-based
- **Content Classification**: Adult content handling, genre categorization
- **Multi-language Support**: Localized content and metadata
- **Episode Management**: Image assets, payment requirements, view tracking

### Advanced Analytics System
- **Real-time Processing**: BullMQ-powered asynchronous analytics
- **Multi-dimensional Tracking**: Content, user, payment analytics
- **Currency Conversion**: Automatic KRW conversion with daily exchange rates
- **Performance Optimized**: Redis caching, database indexing, batch processing

### Comprehensive Admin Panel
- **Role-based Access**: Menu-based permission system
- **Content Management**: Full CRUD operations for content, episodes, authors
- **User Administration**: Account management, sanctions, moderation
- **Analytics Dashboard**: Revenue tracking, content performance, user engagement
- **Support Management**: Ticket assignment, auto-reply configuration

### Notification & Communication
- **Dual Notification System**: System-wide and user-specific notifications
- **Email Integration**: Handlebars templates, automated notifications
- **Support System**: Ticket management, Q&A with auto-reply
- **HTML Content Support**: Rich content with automatic sanitization

### Security & Performance
- **Dual Authentication**: Separate JWT strategies for users and admins
- **Session Management**: Redis-backed session tracking
- **Error Handling**: Centralized message configuration, standardized responses
- **Performance Optimization**: Caching, indexing, queue processing

### Scalability Features
- **Docker Ready**: Development and production containerization
- **Queue Processing**: BullMQ for high-volume async operations
- **Database Optimization**: Proper indexing, migration-driven schema
- **Modular Architecture**: Clear separation of concerns, reusable components

## 📊 API Documentation

### Swagger Integration
- **Endpoint**: `/api` (when server is running)
- **Separate Documentation**: User and admin endpoints
- **DTO Validation**: Class-validator integration
- **Response Types**: TypeScript interfaces

### Key API Patterns
```typescript
// Admin endpoints (protected)
@MenuPermission('content-management')
@UseGuards(JwtAdminAuthGuard, MenuPermissionGuard)

// User endpoints (optional auth)
@UseGuards(OptionalJwtAuthGuard)

// Analytics integration
await this.analyticsQueueService.trackContentView({
  userId, contentId, episodeId, platform
});
```

## 🔄 Development Workflow

### Code Quality
- **ESLint**: Code linting and formatting
- **TypeScript**: Type safety and development experience
- **Testing**: Unit tests and E2E tests with Jest
- **Migration System**: Database schema version control

### Error Handling
- **Centralized Messages**: `MESSAGE_CONFIG` for consistent error responses
- **Exception Filters**: NestJS exception handling
- **Analytics Safety**: Non-blocking analytics with error isolation

### Performance Monitoring
- **Queue Dashboard**: BullMQ job monitoring
- **Database Optimization**: Query analysis and indexing
- **Caching Strategy**: Redis-based caching for frequently accessed data

## 📈 Recent Enhancements (January 2025)

### Content Analytics API
- **Data Source Migration**: From `user_content` to `content_analytics` for real-time data
- **Enhanced Filtering**: Date range and language-based filtering
- **Adult Content Breakdown**: Separate analytics for adult vs non-adult content
- **Performance Optimization**: Optimized queries with proper JOINs

### Notification System
- **Dual Notification Types**: System-wide and user-specific notifications
- **Time-based Display**: Unix timestamp controls for targeted messaging
- **HTML Sanitization**: DOMPurify integration for secure rich content
- **View Tracking**: Comprehensive read/unread status management

### Support & Q&A System
- **Unified Support**: Integration of ticket system with Q&A functionality
- **Auto-reply Capability**: Configurable automated responses
- **Email Integration**: Comprehensive email notification system
- **Admin Workflow**: Complete admin interface for support management

### FAQ System Implementation
- **Comprehensive FAQ Management**: Admin interface for creating and managing FAQs
- **Category Integration**: Uses support category system for organization
- **HTML Content Support**: Rich text content with DOMPurify sanitization
- **Public Access**: User-friendly FAQ browsing without authentication requirement
- **Analytics Integration**: View tracking and engagement metrics
- **Status Management**: Active/inactive status with soft delete functionality

---

This document provides a comprehensive overview of the NestJS Digital Content Platform structure. For specific implementation details, refer to the individual module documentation and source code comments.