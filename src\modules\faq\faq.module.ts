import { <PERSON>du<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FaqController } from './controllers/faq.controller';
import { FaqService } from './services/faq.service';
import { FaqRepository } from '../../database/repositories/faq.repository';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';
import { FaqEntity } from '../../database/entities/faq.entity';

@Module({
  imports: [TypeOrmModule.forFeature([FaqEntity]), AnalyticsQueueModule],
  controllers: [FaqController],
  providers: [FaqService, FaqRepository],
  exports: [FaqService],
})
export class FaqModule {}
