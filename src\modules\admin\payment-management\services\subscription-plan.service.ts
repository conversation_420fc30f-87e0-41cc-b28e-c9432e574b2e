import { Injectable, NotFoundException } from '@nestjs/common';
import { SubscriptionPlanRepository } from '../../../../database/repositories/subscription-plan.repository';
import { CreateSubscriptionPlanDto } from '../dto/create-subscription-plan.dto';
import { UpdateSubscriptionPlanDto } from '../dto/update-subscription-plan.dto';
import { SubscriptionPlanEntity } from '../../../../database/entities/subscription_plan.entity';
import { CommonStatus } from '../../../../common/status.enum';

@Injectable()
export class SubscriptionPlanService {
  constructor(
    private readonly subscriptionPlanRepository: SubscriptionPlanRepository,
  ) {}

  async create(
    createSubscriptionPlanDto: CreateSubscriptionPlanDto,
  ): Promise<SubscriptionPlanEntity> {
    const subscriptionPlan = this.subscriptionPlanRepository.create({
      ...createSubscriptionPlanDto,
      status: createSubscriptionPlanDto.status || CommonStatus.ACTIVE,
    });

    return await this.subscriptionPlanRepository.save(subscriptionPlan);
  }

  async findAll(): Promise<SubscriptionPlanEntity[]> {
    return await this.subscriptionPlanRepository.find({
      relations: ['subscriptions'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: number): Promise<SubscriptionPlanEntity> {
    const subscriptionPlan = await this.subscriptionPlanRepository.findOne({
      where: { id },
      relations: ['subscriptions'],
    });

    if (!subscriptionPlan) {
      throw new NotFoundException(`Subscription plan with ID ${id} not found`);
    }

    return subscriptionPlan;
  }

  async update(
    id: number,
    updateSubscriptionPlanDto: UpdateSubscriptionPlanDto,
  ): Promise<SubscriptionPlanEntity> {
    const subscriptionPlan = await this.findOne(id);

    Object.assign(subscriptionPlan, updateSubscriptionPlanDto);

    return await this.subscriptionPlanRepository.save(subscriptionPlan);
  }

  async remove(id: number): Promise<void> {
    const subscriptionPlan = await this.findOne(id);
    await this.subscriptionPlanRepository.remove(subscriptionPlan);
  }

  async findActive(): Promise<SubscriptionPlanEntity[]> {
    return await this.subscriptionPlanRepository.find({
      where: { status: CommonStatus.ACTIVE },
      order: { createdAt: 'DESC' },
    });
  }

  async deactivate(id: number): Promise<SubscriptionPlanEntity> {
    const subscriptionPlan = await this.findOne(id);
    subscriptionPlan.status = CommonStatus.INACTIVE;
    return await this.subscriptionPlanRepository.save(subscriptionPlan);
  }

  async activate(id: number): Promise<SubscriptionPlanEntity> {
    const subscriptionPlan = await this.findOne(id);
    subscriptionPlan.status = CommonStatus.ACTIVE;
    return await this.subscriptionPlanRepository.save(subscriptionPlan);
  }

  async getPaymentPlans(currency: string): Promise<SubscriptionPlanEntity[]> {
    return await this.subscriptionPlanRepository.find({
      where: { currency: currency, status: CommonStatus.ACTIVE },
      order: { createdAt: 'DESC' },
    });
  }
}
