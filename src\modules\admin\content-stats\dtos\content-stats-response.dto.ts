import { ApiProperty } from '@nestjs/swagger';

export class EpisodeViewStatsDto {
  @ApiProperty({
    description: 'Number of adult content views/reads',
    example: 150,
  })
  adult: number;

  @ApiProperty({
    description: 'Number of non-adult content views/reads',
    example: 300,
  })
  nonAdult: number;
}

export class ContentAnalyticsStatsDto {
  @ApiProperty({
    description: 'Date of the statistics (YYYY-MM-DD)',
    example: '2025-01-18',
  })
  date: string;

  @ApiProperty({
    description: 'Free episode views from content_analytics',
    type: EpisodeViewStatsDto,
  })
  freeEpisodeViews: EpisodeViewStatsDto;

  @ApiProperty({
    description: 'Paid episode reads from content_analytics',
    type: EpisodeViewStatsDto,
  })
  paidEpisodeReads: EpisodeViewStatsDto;

  @ApiProperty({
    description: 'Combined statistics by section (free + paid)',
    type: EpisodeViewStatsDto,
  })
  combinedBySection: EpisodeViewStatsDto;

  @ApiProperty({
    description: 'Total combined all views/reads for the day',
    example: 725,
  })
  totalCombined: number;
}

export class ContentStatsResponseDto {
  @ApiProperty({
    description:
      'Array of daily content analytics statistics, sorted by date descending',
    type: [ContentAnalyticsStatsDto],
  })
  data: ContentAnalyticsStatsDto[];

  @ApiProperty({
    description: 'Total number of days in the result',
    example: 31,
  })
  total: number;

  @ApiProperty({
    description: 'Query parameters used',
    example: {
      startDate: '2025-01-01',
      endDate: '2025-01-31',
      language: 'all',
    },
  })
  queryInfo: {
    startDate: string;
    endDate: string;
    language: string;
  };
}

// Keep legacy DTOs for backward compatibility if needed
export class EpisodeReadStatsDto {
  @ApiProperty({
    description: 'Number of adult episode reads',
    example: 150,
  })
  adult: number;

  @ApiProperty({
    description: 'Number of non-adult episode reads',
    example: 300,
  })
  nonAdult: number;
}

export class DailyStatsDto {
  @ApiProperty({
    description: 'Date of the statistics (YYYY-MM-DD)',
    example: '2025-01-14',
  })
  date: string;

  @ApiProperty({
    description: 'Free episodes read statistics',
    type: EpisodeReadStatsDto,
  })
  freeEpisodesRead: EpisodeReadStatsDto;

  @ApiProperty({
    description: 'Paid episodes read statistics',
    type: EpisodeReadStatsDto,
  })
  paidEpisodesRead: EpisodeReadStatsDto;

  @ApiProperty({
    description: 'Combined episodes read by section',
    type: EpisodeReadStatsDto,
  })
  combinedBySection: EpisodeReadStatsDto;

  @ApiProperty({
    description: 'Total reads for the day',
    example: 725,
  })
  totalReads: number;
}
