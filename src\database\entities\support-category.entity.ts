import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
} from 'typeorm';

export enum CategoryStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('support_category')
export class SupportCategoryEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string; // 'payment-billing', 'content-access', etc.

  @Column()
  title: string; // 'Payment & Billing', 'Content Access', etc.

  @Column({ nullable: true })
  description: string;

  @Column({
    type: 'enum',
    enum: CategoryStatus,
    default: CategoryStatus.ACTIVE,
  })
  status: CategoryStatus;

  @Column({ default: 1 })
  order: number; // Thứ tự hiển thị

  // SLA settings
  @Column({ name: 'expected_response_time', nullable: true })
  expectedResponseTime: number; // hours

  @Column({ name: 'expected_resolution_time', nullable: true })
  expectedResolutionTime: number; // hours

  // Auto-assignment settings
  @Column({ name: 'default_assignee_id', nullable: true })
  defaultAssigneeId: number;

  @Column({ name: 'auto_assign', default: false })
  autoAssign: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
