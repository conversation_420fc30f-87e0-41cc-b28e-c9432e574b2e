import { IsEmail, IsOptional, IsString } from 'class-validator';
import { AdminRole } from '../../../../common/role.enum';
import { AdminStatus } from '../../../../common/status.enum';
import { ApiProperty } from '@nestjs/swagger';

export class UpdateAdminDto {
  @ApiProperty({
    description: 'The email of the admin',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsOptional()
  email: string;

  @ApiProperty({
    description: 'The password of the admin',
    example: 'password',
  })
  @IsString()
  @IsOptional()
  password: string;

  @ApiProperty({
    description: 'The role of the admin',
    example: 'admin',
  })
  @IsString()
  @IsOptional()
  role: AdminRole;

  @ApiProperty({
    description: 'The status of the admin',
    example: 'active',
  })
  @IsString()
  @IsOptional()
  status: AdminStatus;
}
