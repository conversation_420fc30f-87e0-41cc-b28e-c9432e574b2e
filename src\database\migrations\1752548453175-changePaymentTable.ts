import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangePaymentTable1752548453175 implements MigrationInterface {
  name = 'ChangePaymentTable1752548453175';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payments\` ADD \`ip\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`payments\` DROP COLUMN \`ip\``);
  }
}
