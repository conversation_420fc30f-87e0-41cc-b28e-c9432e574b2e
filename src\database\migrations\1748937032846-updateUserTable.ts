import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTable1748937032846 implements MigrationInterface {
  name = 'UpdateUserTable1748937032846';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`type\` enum ('general', 'restricted') NULL DEFAULT 'general'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`type\``);
  }
}
