import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsEnum,
  IsOptional,
  IsString,
  IsUrl,
  NotEquals,
} from 'class-validator';
import {
  EAiWebtoonCharacterGender,
  EAiWebtoonCharacterType,
} from 'src/common/ai-webtoon.enum';

export class CreateAiWebtoonCharacterDto {
  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  name: string;

  @ApiProperty({ enum: EAiWebtoonCharacterGender, required: false })
  @IsOptional()
  @IsEnum(EAiWebtoonCharacterGender)
  gender?: EAiWebtoonCharacterGender;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsUrl()
  avatar: string;

  @ApiProperty({ enum: EAiWebtoonCharacterType })
  @IsEnum(EAiWebtoonCharacterType)
  @NotEquals(EAiWebtoonCharacterType.DREAM_BOOTH)
  type: EAiWebtoonCharacterType;
}
