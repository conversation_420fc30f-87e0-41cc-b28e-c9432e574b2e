import {
  registerDecorator,
  ValidationOptions,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from 'class-validator';

@ValidatorConstraint({ async: false })
class MaxArraySizeConstraint implements ValidatorConstraintInterface {
  validate(items: string[], args: any) {
    const maxSize = args.constraints[0]; // Lấy giá trị tối đa từ constraints
    return Array.isArray(items) && items.length <= maxSize;
  }

  defaultMessage(args: any) {
    return `The array can contain a maximum of ${args.constraints[0]} items.`;
  }
}

export function MaxArraySize(
  maxSize: number,
  validationOptions?: ValidationOptions,
) {
  return function (object: Object, propertyName: string) {
    registerDecorator({
      name: 'maxArraySize',
      target: object.constructor,
      propertyName: propertyName,
      constraints: [maxSize], // Truyền giá trị tối đa vào constraints
      options: validationOptions,
      validator: MaxArraySizeConstraint,
    });
  };
}
