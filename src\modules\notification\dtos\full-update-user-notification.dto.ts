import { ApiProperty } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsEnum,
  MaxLength,
  IsArray,
  IsInt,
  Min,
  ArrayMinSize,
  Validate,
} from 'class-validator';
import { Type } from 'class-transformer';
import { NotificationStatus } from '../../../database/entities/user-notification.entity';
import { IsTimeRangeValid } from '../validators/time-range.validator';

export class FullUpdateUserNotificationDto {
  @ApiProperty({
    description: 'Notification name',
    example: 'special_offer',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    description: 'Notification title',
    example: 'Special Offer for You!',
    required: false,
    maxLength: 255,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  title?: string;

  @ApiProperty({
    description: 'Start time (Unix timestamp)',
    example: 1642809600,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  startTime?: number;

  @ApiProperty({
    description: 'End time (Unix timestamp)',
    example: 1642896000,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  endTime?: number;

  @ApiProperty({
    description: 'Notification content (HTML allowed)',
    example: '<p>Special discount available for premium members!</p>',
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    enum: NotificationStatus,
    example: NotificationStatus.ACTIVE,
    description: 'Notification status',
    required: false,
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @ApiProperty({
    description: 'User IDs to receive this notification',
    example: [1, 2, 3, 4, 5],
    type: [Number],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  @Min(1, { each: true })
  @Type(() => Number)
  userIds?: number[];

  @Validate(IsTimeRangeValid)
  _timeRangeValidation?: any;
}
