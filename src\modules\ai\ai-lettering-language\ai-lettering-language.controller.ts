import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';

import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';
import { AiLetteringLanguageService } from './ai-lettering-language.service';
import { CreateAiLetteringLanguageDto } from './dto/create-ai-lettering-language.dto';
import { SortAiLetteringLanguageDto } from './dto/sort-ai-lettering-language.dto';

@ApiTags('ai-lettering-language')
@Controller('ai-lettering-language')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
export class AiLetteringLanguageController {
  constructor(
    private readonly aiLetteringLanguageService: AiLetteringLanguageService,
  ) {}

  @Get()
  list() {
    return this.aiLetteringLanguageService.list();
  }

  @Post()
  create(@Body() body: CreateAiLetteringLanguageDto) {
    return this.aiLetteringLanguageService.create(body);
  }

  @Put()
  sort(@Body() body: SortAiLetteringLanguageDto) {
    return this.aiLetteringLanguageService.sort(body);
  }

  @Delete('/:id')
  delete(@Param('id', ParseIntPipe) id: number) {
    return this.aiLetteringLanguageService.delete(id);
  }
}
