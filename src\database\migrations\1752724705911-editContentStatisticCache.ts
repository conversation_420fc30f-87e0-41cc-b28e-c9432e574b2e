import { MigrationInterface, QueryRunner } from 'typeorm';

export class EditContentStatisticCache1752724705911
  implements MigrationInterface
{
  name = 'EditContentStatisticCache1752724705911';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_c37a64f44a1fd0ed49854f70c4\` ON \`content_statistics_cache\``,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE INDEX \`IDX_c37a64f44a1fd0ed49854f70c4\` ON \`content_statistics_cache\` (\`contentId\`)`,
    );
  }
}
