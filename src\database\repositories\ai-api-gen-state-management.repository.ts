import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiApiGenStateManagementEntity } from '../entities/ai-api-gen-state-management.entity';

@Injectable()
export class AiApiGenStateManagementRepository extends Repository<AiApiGenStateManagementEntity> {
  constructor(private dataSource: DataSource) {
    super(AiApiGenStateManagementEntity, dataSource.createEntityManager());
  }
}