import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsEnum,
  IsOptional,
  MaxLength,
  IsArray,
  ArrayMinSize,
  IsNumber,
  IsPositive,
  Min,
} from 'class-validator';
import { NotificationStatus } from '../../../database/entities/user-notification.entity';

export class CreateUserNotificationDto {
  @ApiProperty({
    example: 'root',
    description: 'Notification name',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiProperty({
    example: 'Special Offer for You!',
    description: 'Notification title',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiProperty({
    example: 1642809600,
    description: 'Start display time (Unix timestamp from 00:00)',
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  @Min(0)
  startTime: number;

  @ApiProperty({
    example: 1642896000,
    description: 'End display time (Unix timestamp until 23:59)',
  })
  @IsNotEmpty()
  @IsNumber()
  @IsPositive()
  @Min(0)
  endTime: number;

  @ApiProperty({
    enum: NotificationStatus,
    example: NotificationStatus.ACTIVE,
    description: 'Notification status',
    required: false,
  })
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus;

  @ApiProperty({
    example: '<p>Special discount available for premium members!</p>',
    description: 'Notification content with HTML',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    example: [1, 2, 3],
    description: 'Array of user IDs to receive this notification',
    type: [Number],
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayMinSize(1)
  @IsNumber({}, { each: true })
  @IsPositive({ each: true })
  userIds: number[];
}
