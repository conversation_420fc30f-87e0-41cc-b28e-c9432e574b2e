import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsIn, IsNotEmpty } from 'class-validator';
import { EAiFrameImageSizeType } from 'src/common/ai-webtoon.enum';

export class UnselectSketchImagesDto {
  @ApiProperty({
    enum: EAiFrameImageSizeType,
    example: EAiFrameImageSizeType.CONTROLNET_CANNY,
  })
  @IsNotEmpty()
  @IsEnum(EAiFrameImageSizeType)
  @IsIn([
    EAiFrameImageSizeType.CONTROLNET_CANNY,
    EAiFrameImageSizeType.CONTROLNET_DEPTH,
  ])
  imageSizeType: EAiFrameImageSizeType;
}
