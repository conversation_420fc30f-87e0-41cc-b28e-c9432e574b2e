import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeAdminPassword1750736786389 implements MigrationInterface {
  name = 'ChangeAdminPassword1750736786389';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`admins\` CHANGE \`password\` \`password\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`admins\` CHANGE \`password\` \`password\` varchar(255) NOT NULL`,
    );
  }
}
