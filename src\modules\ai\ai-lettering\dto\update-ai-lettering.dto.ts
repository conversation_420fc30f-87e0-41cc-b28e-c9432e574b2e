import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  Min,
} from 'class-validator';
import { EAiLetteringClassificationType } from 'src/common/ai-webtoon.enum';

export class UpdateAiLetteringDto {
  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ enum: EAiLetteringClassificationType })
  @IsEnum(EAiLetteringClassificationType)
  @IsNotEmpty()
  classification: EAiLetteringClassificationType;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  @IsNotEmpty()
  extension: string;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsUrl()
  @IsNotEmpty()
  url: string;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsUrl()
  @IsNotEmpty()
  thumbnailUrl: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  aiLetteringLanguageId: number;

  @ApiProperty()
  @IsInt()
  @Min(1)
  aiLetteringTypeId: number;
}
