import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, IsNull } from 'typeorm';
import { Job, Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { ContentAnalyticsEntity } from '../../../database/entities/content-analytics.entity';
import { ContentViewEventDto } from '../dtos/analytics-events.dto';
import { ANALYTICS_QUEUES } from '../constants/analytics-queues.constant';

@Injectable()
@Processor(ANALYTICS_QUEUES.CONTENT_ANALYTICS)
export class ContentAnalyticsProcessor extends WorkerHost {
  private readonly logger = new Logger(ContentAnalyticsProcessor.name);

  constructor(
    @InjectRepository(ContentAnalyticsEntity)
    private contentAnalyticsRepository: Repository<ContentAnalyticsEntity>,
    @InjectQueue('content-statistics')
    private contentStatisticsQueue: Queue,
  ) {
    super();
  }

  async process(job: Job<ContentViewEventDto>): Promise<void> {
    const { data } = job;
    this.logger.debug(`Processing content view: ${JSON.stringify(data)}`);

    try {
      const viewDate = new Date(data.viewTime);
      const date = new Date(
        viewDate.getFullYear(),
        viewDate.getMonth(),
        viewDate.getDate(),
      );
      const hour = viewDate.getHours();

      // Tìm hoặc tạo analytics record
      let analytics = await this.contentAnalyticsRepository.findOne({
        where: {
          date,
          hour,
          contentId: data.contentId,
          episodeId: data.episodeId ?? IsNull(),
        },
      });

      if (!analytics) {
        analytics = this.contentAnalyticsRepository.create({
          date,
          hour,
          contentId: data.contentId,
          episodeId: data.episodeId ?? null,
          viewCount: 0,
          uniqueViewers: 0,
          totalReadTime: 0,
          averageReadTime: 0,
          metadata: {},
        });
      }

      // Update metrics
      analytics.viewCount += 1;

      // Update unique viewers (simplified approach)
      await this.updateUniqueViewers(analytics, data.userId);

      // Update read time
      if (data.readDuration) {
        analytics.totalReadTime += data.readDuration;
        analytics.averageReadTime =
          analytics.totalReadTime / analytics.viewCount;
      }

      // Update metadata
      if (data.metadata) {
        analytics.metadata = { ...analytics.metadata, ...data.metadata };
      }

      await this.contentAnalyticsRepository.save(analytics);

      // Trigger daily ranking stats update with debounce (only update once per minute)
      await this.triggerDailyRankingUpdate(analytics.date);

      this.logger.debug(
        `Updated content analytics and ranking stats for content ${data.contentId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error processing content view: ${error.message}`,
        error.stack,
      );
      throw error; // Re-throw để trigger retry
    }
  }

  private async updateUniqueViewers(
    analytics: ContentAnalyticsEntity,
    userId: number,
  ): Promise<void> {
    // Simplified approach - check if user already viewed in this hour
    const existingView = await this.contentAnalyticsRepository.query(
      `
      SELECT COUNT(*) as count 
      FROM user_content uc 
      WHERE uc.userId = ? 
        AND uc.contentId = ? 
        AND uc.episodeId = ?
        AND DATE(FROM_UNIXTIME(uc.lastUpdateTime/1000)) = ?
        AND HOUR(FROM_UNIXTIME(uc.lastUpdateTime/1000)) = ?
        AND uc.type = 'recently_read'
    `,
      [
        userId,
        analytics.contentId,
        analytics.episodeId,
        analytics.date,
        analytics.hour,
      ],
    );

    if (existingView[0].count === 0) {
      analytics.uniqueViewers += 1;
    }
  }

  /**
   * Trigger daily ranking stats update with debounce
   * Only update once per minute per date to avoid performance issues
   */
  private async triggerDailyRankingUpdate(date: Date | string): Promise<void> {
    // Handle both Date objects and string dates
    let dateStr: string;
    if (date instanceof Date) {
      dateStr = date.toISOString().split('T')[0];
    } else if (typeof date === 'string') {
      dateStr = date.split('T')[0]; // Handle ISO string format
    } else {
      // Fallback to current date
      dateStr = new Date().toISOString().split('T')[0];
    }

    // const jobId = `daily-ranking-${dateStr}`;

    try {
      console.log('hehe');

      // Add job with delay and deduplication
      await this.contentStatisticsQueue.add(
        'update-daily-stats',
        { date: dateStr },
        {
          // jobId, // This ensures only one job per date exists
          delay: 0,
          removeOnComplete: 5,
          removeOnFail: 3,
          attempts: 2,
        },
      );
    } catch (error) {
      console.log(
        `Failed to trigger daily ranking update for ${dateStr}:`,
        error,
      );
      // Ignore duplicate job errors
      if (!error.message.includes('already exists')) {
        this.logger.error(
          `Failed to trigger daily ranking update: ${error.message}`,
        );
      }
    }
  }
}
