import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { Job } from 'bullmq';
import { ContentStatisticsService } from '../services/content-statistics.service';

@Injectable()
@Processor('content-statistics')
export class ContentStatisticsProcessor extends WorkerHost {
  private readonly logger = new Logger(ContentStatisticsProcessor.name);

  constructor(
    private readonly contentStatisticsService: ContentStatisticsService,
  ) {
    super();
  }

  async process(job: Job): Promise<void> {
    this.logger.debug(`Processing content statistics job: ${job.name}`);

    try {
      switch (job.name) {
        case 'refresh-cache':
          await this.contentStatisticsService.refreshContentStatisticsCache();
          break;
        case 'update-daily-stats': {
          console.log('kakak');

          const date = job.data.date || new Date().toISOString().split('T')[0];
          await this.contentStatisticsService.updateDailyRankingStats(date);
          break;
        }
        case 'update-all-time-stats':
          await this.contentStatisticsService.updateAllTimeRankingStats();
          break;
        default:
          this.logger.warn(`Unknown job name: ${job.name}`);
      }
    } catch (error) {
      this.logger.error(
        `Error processing content statistics job ${job.name}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
