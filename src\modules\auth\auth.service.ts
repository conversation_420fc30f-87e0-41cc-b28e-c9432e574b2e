import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UserService } from '../user/user.service';
import * as bcrypt from 'bcrypt';
import { UserRepository } from '../../database/repositories/user.repository';
import { BadRequestException } from '@nestjs/common';
import { LoginDto } from './dtos/login.dto';
import { plainToInstance } from 'class-transformer';
import { UserEntity } from '../../database/entities/user.entity';
import { RegisterDto } from './dtos/register.dto';
import { ForgotPasswordDto } from './dtos/forgot-password.dto';
import * as moment from 'moment';
import { ResetPasswordDto } from './dtos/reset-password.dto';
import configuration from '../../config/configuration.global';
import { RefreshTokenDto } from './dtos/refresh-token.dto';
import { MESSAGE_CONFIG } from '../../common/message.config';
import { Request } from 'express';
import { CommonService } from '../../common/common.service';
import { Platform } from '../../common/common.config';
import { SessionRepository } from '../../database/repositories/session.repository';
import { SessionEntity } from '../../database/entities/session.entity';
import { v4 as uuidv4 } from 'uuid';
import { CommonStatus } from '../../common/status.enum';
import { LogoutByUuidDto } from './dtos/logout-uuid.dto';
import { AnalyticsQueueService } from '../analytics/services/analytics-queue.service';

@Injectable()
export class AuthService {
  constructor(
    private jwtService: JwtService,
    private usersService: UserService,
    private userRepository: UserRepository,
    private commonService: CommonService,
    private sessionRepository: SessionRepository,
    private analyticsQueueService: AnalyticsQueueService,
  ) {}

  async validateUser(username: string, password: string) {
    const user = await this.usersService.findByEmail(username);
    if (user && (await bcrypt.compare(password, user.password))) {
      return user;
    }
    return null;
  }

  async login(loginDto: LoginDto, req: Request) {
    try {
      let ip = req?.ip?.replace('::ffff:', '');
      ip = ip?.split(':').shift();
      const payload = {
        email: loginDto.email,
        password: loginDto.password,
      };
      const platform = this.commonService.detectPlatform(req);
      const browser = this.commonService.detectBrowser(
        platform.userAgent || '',
      );
      const os = this.commonService.detectOs(platform.userAgent || '');
      const user = await this.validateUser(payload.email, payload.password);
      if (!user) {
        throw new UnauthorizedException(MESSAGE_CONFIG.INVALID_CREDENTIALS);
      }

      const uuid = uuidv4();
      const userData = {
        id: user.id,
        email: user.email,
        uuid: uuid,
      };
      const refreshToken = this.jwtService.sign(userData, {
        expiresIn: `${configuration().auth.refreshToken.expiresIn} ${configuration().auth.refreshToken.expiresUnit}`,
      });

      const session = new SessionEntity();
      session.ipAddress = ip || '';
      session.userAgent = platform.userAgent || '';
      session.platform = platform.platform as Platform;
      session.browser = browser;
      session.user = user;
      session.uuid = uuid;
      session.lastUpdateTimestamp = moment().toDate().getTime();
      session.refreshToken = refreshToken;
      session.refreshTokenExpiresAt = moment()
        .add(configuration().auth.refreshToken.expiresIn, 'days')
        .toDate()
        .getTime();
      session.os = os;
      await this.sessionRepository.save(session);

      // Track login analytics
      try {
        await this.analyticsQueueService.trackUserActivity({
          userId: user.id,
          activityType: 'login',
          timestamp: Date.now(),
          platform:
            platform.platform === Platform.MOBILE.toString() ? 'mobile' : 'pc',
          metadata: {
            source: 'login',
            userAgent: platform.userAgent,
            ipAddress: ip,
            browser,
            os,
          },
        });
      } catch (error) {
        // Don't fail login if analytics fails
        console.error('Failed to track login analytics:', error);
      }

      return {
        access_token: this.jwtService.sign(userData),
        refresh_token: refreshToken,
      };
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  async logout(req: Request) {
    try {
      const session = await this.sessionRepository.findOne({
        where: { refreshToken: req.headers.authorization },
      });
      if (!session) {
        throw new UnauthorizedException(MESSAGE_CONFIG.REFRESH_TOKEN_INVALID);
      }
      session.lastUpdateTimestamp = moment().toDate().getTime();
      session.status = CommonStatus.INACTIVE;
      await this.sessionRepository.save(session);
      return {
        message: 'Logout successfully',
      };
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  async checkForgotPasswordToken(token: string) {
    const user = await this.userRepository.findByForgotPasswordToken(token);
    if (!user) {
      throw new BadRequestException(
        MESSAGE_CONFIG.FORGOT_PASSWORD_TOKEN_NOT_FOUND,
      );
    }

    if (moment(user.forgotPasswordTokenExpiresAt).isBefore(moment())) {
      throw new BadRequestException(MESSAGE_CONFIG.TOKEN_EXPIRED);
    }
    return plainToInstance(UserEntity, user);
  }

  async logoutByUuid(logoutByUuidDto: LogoutByUuidDto, userId: number) {
    const session = await this.sessionRepository.findOne({
      where: {
        uuid: logoutByUuidDto.uuid,
        status: CommonStatus.ACTIVE,
        user: {
          id: userId,
        },
      },
    });
    if (!session) {
      throw new UnauthorizedException(MESSAGE_CONFIG.SESSION_NOT_FOUND);
    }
    session.lastUpdateTimestamp = moment().toDate().getTime();
    session.status = CommonStatus.INACTIVE;
    await this.sessionRepository.save(session);
    return {
      message: `Logout ${session.uuid} successfully`,
    };
  }

  async refreshToken(refreshTokenDto: RefreshTokenDto) {
    try {
      const payload = this.jwtService.verify(refreshTokenDto.refresh_token);
      const session = await this.sessionRepository.findOne({
        where: { uuid: payload.uuid, status: CommonStatus.ACTIVE },
        relations: ['user'],
      });
      if (!session) {
        throw new UnauthorizedException(MESSAGE_CONFIG.REFRESH_TOKEN_INVALID);
      }

      if (moment(payload.exp.toString()).isBefore(moment())) {
        throw new UnauthorizedException(MESSAGE_CONFIG.REFRESH_TOKEN_EXPIRED);
      }

      const userData = {
        id: session.user.id,
        email: session.user.email,
        uuid: session.uuid,
      };

      const refreshToken = this.jwtService.sign(userData, {
        expiresIn: configuration().auth.refreshToken.expiresIn,
      });
      session.refreshToken = refreshToken;
      session.refreshTokenExpiresAt = moment()
        .add(configuration().auth.refreshToken.expiresIn, 'days')
        .toDate()
        .getTime();
      session.lastUpdateTimestamp = moment().toDate().getTime();
      await this.sessionRepository.save(session);
      return {
        access_token: this.jwtService.sign(userData),
        refresh_token: refreshToken,
      };
    } catch (error) {
      throw new UnauthorizedException(error.message);
    }
  }

  async register(registerDto: RegisterDto, req: Request) {
    const user = await this.userRepository.findByEmail(registerDto.email);
    if (user) {
      throw new BadRequestException(MESSAGE_CONFIG.EMAIL_ALREADY_EXISTS);
    }

    let ip = req?.ip?.replace('::ffff:', '');
    ip = ip?.split(':').shift();

    const newUser = await this.userRepository.createUser(
      registerDto.email,
      registerDto.password,
      ip,
    );

    // Track registration analytics
    try {
      const platform = this.commonService.detectPlatform(req);
      await this.analyticsQueueService.trackUserActivity({
        userId: newUser.id,
        activityType: 'register',
        timestamp: Date.now(),
        platform:
          platform.platform === Platform.MOBILE.toString() ? 'mobile' : 'pc',
        metadata: {
          source: 'registration',
          userAgent: platform.userAgent,
          ipAddress: ip,
          email: registerDto.email,
        },
      });
    } catch (error) {
      // Don't fail registration if analytics fails
      console.error('Failed to track registration analytics:', error);
    }

    return plainToInstance(UserEntity, newUser);
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto, isResend = false) {
    try {
      const user = await this.userRepository.findByEmail(
        forgotPasswordDto.email,
      );
      if (!user) {
        throw new BadRequestException(MESSAGE_CONFIG.USER_NOT_FOUND);
      }

      if (user.forgotPasswordToken && !isResend) {
        if (moment(user.forgotPasswordTokenExpiresAt).isAfter(moment())) {
          throw new BadRequestException(
            MESSAGE_CONFIG.FORGOT_PASSWORD_TOKEN_ALREADY_EXISTS,
          );
        }
      }
      const configExpiresIn = configuration().auth.forgotPassword.expiresIn;

      const token = this.jwtService.sign(
        { email: user.email },
        { expiresIn: configExpiresIn },
      );
      const expiresIn = moment().add(configExpiresIn, 'hours').toDate();
      user.forgotPasswordToken = token;
      user.forgotPasswordTokenExpiresAt = expiresIn;
      await this.commonService.sendEmail(
        user.email,
        'Forgot Password',
        'forgot-password',
        {
          url: `${configuration().url.user}/reset-password/${token}`,
          urlLogo: `${configuration().url.user}/logo.png`,
        },
      );
      await this.userRepository.save(user);

      return {
        message: MESSAGE_CONFIG.FORGOT_PASSWORD_MAIL_SENT,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto) {
    const user = await this.userRepository.findByForgotPasswordToken(
      resetPasswordDto.token,
    );
    if (!user) {
      throw new BadRequestException(
        MESSAGE_CONFIG.FORGOT_PASSWORD_TOKEN_NOT_FOUND,
      );
    }
    if (user.forgotPasswordToken !== resetPasswordDto.token) {
      throw new BadRequestException(MESSAGE_CONFIG.INVALID_TOKEN);
    }
    if (moment(user.forgotPasswordTokenExpiresAt).isBefore(moment())) {
      throw new BadRequestException(MESSAGE_CONFIG.TOKEN_EXPIRED);
    }

    user.password = await bcrypt.hash(resetPasswordDto.password, 10);
    user.forgotPasswordToken = '';
    user.forgotPasswordTokenExpiresAt = new Date();
    await this.userRepository.save(user);
    return {
      message: MESSAGE_CONFIG.PASSWORD_RESET_SUCCESSFULLY,
    };
  }

  async resendForgotPassword(forgotPasswordDto: ForgotPasswordDto) {
    return this.forgotPassword(forgotPasswordDto, true);
  }
}
