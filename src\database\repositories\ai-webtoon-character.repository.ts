import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonCharacterEntity } from '../entities/ai-webtoon-character.entity';

@Injectable()
export class AiWebtoonCharacterRepository extends Repository<AiWebtoonCharacterEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonCharacterEntity, dataSource.createEntityManager());
  }
}