import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { SubscriptionRepository } from '../../../../database/repositories/subscription.repository';
import { CreateSubscriptionDto } from '../dto/create-subscription.dto';
import { UpdateSubscriptionDto } from '../dto/update-subscription.dto';
import { SubscriptionEntity } from '../../../../database/entities/subscription.entity';
import { CommonStatus } from '../../../../common/status.enum';

@Injectable()
export class SubscriptionService {
  constructor(
    private readonly subscriptionRepository: SubscriptionRepository,
  ) {}

  async create(
    createSubscriptionDto: CreateSubscriptionDto,
  ): Promise<SubscriptionEntity> {
    // Validate dates
    if (createSubscriptionDto.startedAt >= createSubscriptionDto.expiresAt) {
      throw new BadRequestException('Expiration date must be after start date');
    }

    const subscription = this.subscriptionRepository.create({
      ...createSubscriptionDto,
      status: createSubscriptionDto.status || CommonStatus.ACTIVE,
      autoRenew: createSubscriptionDto.autoRenew || false,
      isTrial: createSubscriptionDto.isTrial || false,
      trialDays: createSubscriptionDto.trialDays || 0,
      user: { id: createSubscriptionDto.userId },
      subscriptionPlan: { id: createSubscriptionDto.subscriptionPlanId },
    });

    return await this.subscriptionRepository.save(subscription);
  }

  async findAll(): Promise<SubscriptionEntity[]> {
    return await this.subscriptionRepository.find({
      relations: ['user', 'subscriptionPlan', 'payments'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: number): Promise<SubscriptionEntity> {
    const subscription = await this.subscriptionRepository.findOne({
      where: { id },
      relations: ['user', 'subscriptionPlan', 'payments'],
    });

    if (!subscription) {
      throw new NotFoundException(`Subscription with ID ${id} not found`);
    }

    return subscription;
  }

  async update(
    id: number,
    updateSubscriptionDto: UpdateSubscriptionDto,
  ): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(id);

    // Validate dates if provided
    const startedAt = updateSubscriptionDto.startedAt || subscription.startedAt;
    const expiresAt = updateSubscriptionDto.expiresAt || subscription.expiresAt;

    if (startedAt >= expiresAt) {
      throw new BadRequestException('Expiration date must be after start date');
    }

    Object.assign(subscription, updateSubscriptionDto);

    // Update relations if provided
    if (updateSubscriptionDto.userId) {
      subscription.user = { id: updateSubscriptionDto.userId } as any;
    }
    if (updateSubscriptionDto.subscriptionPlanId) {
      subscription.subscriptionPlan = {
        id: updateSubscriptionDto.subscriptionPlanId,
      } as any;
    }

    return await this.subscriptionRepository.save(subscription);
  }

  async remove(id: number): Promise<void> {
    const subscription = await this.findOne(id);
    await this.subscriptionRepository.remove(subscription);
  }

  async findActive(): Promise<SubscriptionEntity[]> {
    return await this.subscriptionRepository.find({
      where: { status: CommonStatus.ACTIVE },
      relations: ['user', 'subscriptionPlan'],
      order: { createdAt: 'DESC' },
    });
  }

  async findExpired(): Promise<SubscriptionEntity[]> {
    const now = Date.now();
    return await this.subscriptionRepository
      .createQueryBuilder('subscription')
      .leftJoinAndSelect('subscription.user', 'user')
      .leftJoinAndSelect('subscription.subscriptionPlan', 'subscriptionPlan')
      .where('subscription.expiresAt < :now', { now })
      .andWhere('subscription.status = :status', {
        status: CommonStatus.ACTIVE,
      })
      .orderBy('subscription.expiresAt', 'ASC')
      .getMany();
  }

  async findByUser(userId: number): Promise<SubscriptionEntity[]> {
    return await this.subscriptionRepository.find({
      where: { user: { id: userId } },
      relations: ['subscriptionPlan', 'payments'],
      order: { createdAt: 'DESC' },
    });
  }

  async cancel(id: number): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(id);
    subscription.status = CommonStatus.INACTIVE;
    subscription.autoRenew = false;
    return await this.subscriptionRepository.save(subscription);
  }

  async renew(id: number, expiresAt: number): Promise<SubscriptionEntity> {
    const subscription = await this.findOne(id);

    if (expiresAt <= Date.now()) {
      throw new BadRequestException(
        'New expiration date must be in the future',
      );
    }

    subscription.expiresAt = expiresAt;
    subscription.status = CommonStatus.ACTIVE;

    return await this.subscriptionRepository.save(subscription);
  }
}
