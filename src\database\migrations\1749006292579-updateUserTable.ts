import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTable1749006292579 implements MigrationInterface {
  name = 'UpdateUserTable1749006292579';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`type\``);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`type\` enum ('general', 'restricted') NULL DEFAULT 'general'`,
    );
  }
}
