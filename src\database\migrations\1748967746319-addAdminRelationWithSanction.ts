import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAdminRelationWithSanction1748967746319
  implements MigrationInterface
{
  name = 'AddAdminRelationWithSanction1748967746319';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_38a664c1a40c384e79aa19fc71\` ON \`users\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD \`adminId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD UNIQUE INDEX \`IDX_9fd33f14c5f0eb084160ff5757\` (\`adminId\`)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_9fd33f14c5f0eb084160ff5757\` ON \`sanction\` (\`adminId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD CONSTRAINT \`FK_9fd33f14c5f0eb084160ff5757e\` FOREIGN KEY (\`adminId\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sanction\` DROP FOREIGN KEY \`FK_9fd33f14c5f0eb084160ff5757e\``,
    );
    await queryRunner.query(
      `DROP INDEX \`REL_9fd33f14c5f0eb084160ff5757\` ON \`sanction\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sanction\` DROP INDEX \`IDX_9fd33f14c5f0eb084160ff5757\``,
    );
    await queryRunner.query(`ALTER TABLE \`sanction\` DROP COLUMN \`adminId\``);
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_38a664c1a40c384e79aa19fc71\` ON \`users\` (\`sanctionId\`)`,
    );
  }
}
