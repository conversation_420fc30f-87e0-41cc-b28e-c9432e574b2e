import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateContentTable1749110378464 implements MigrationInterface {
  name = 'UpdateContentTable1749110378464';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP FOREIGN KEY \`FK_93951308013e484eb6ab7888a1b\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP FOREIGN KEY \`FK_ef067ee3c41ac74d4f4f109d712\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_93951308013e484eb6ab7888a1\` ON \`content\``,
    );
    await queryRunner.query(
      `DROP INDEX \`REL_93951308013e484eb6ab7888a1\` ON \`content\``,
    );
    await queryRunner.query(
      `DROP INDEX \`REL_ef067ee3c41ac74d4f4f109d71\` ON \`content\``,
    );
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`genreId\``);
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`authorId\``);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`authorId\` int NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`content\` ADD \`genreId\` int NULL`);
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_ef067ee3c41ac74d4f4f109d71\` ON \`content\` (\`genreId\`)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_93951308013e484eb6ab7888a1\` ON \`content\` (\`authorId\`)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_93951308013e484eb6ab7888a1\` ON \`content\` (\`authorId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD CONSTRAINT \`FK_ef067ee3c41ac74d4f4f109d712\` FOREIGN KEY (\`genreId\`) REFERENCES \`genre\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD CONSTRAINT \`FK_93951308013e484eb6ab7888a1b\` FOREIGN KEY (\`authorId\`) REFERENCES \`author\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
