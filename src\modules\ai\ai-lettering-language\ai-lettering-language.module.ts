import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiLetteringLanguageRepository } from 'src/database/repositories/ai-lettering-language.repository';
import { AiLetteringLanguageController } from './ai-lettering-language.controller';
import { AiLetteringLanguageService } from './ai-lettering-language.service';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiLetteringLanguageRepository,
      AiLetteringRepository,
    ]),
  ],
  controllers: [AiLetteringLanguageController],
  providers: [AiLetteringLanguageService],
  exports: [AiLetteringLanguageService],
})
export class AiLetteringLanguageModule {}
