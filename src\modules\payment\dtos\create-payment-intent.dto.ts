import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString } from 'class-validator';

export class CreatePaymentIntentDto {
  @IsNotEmpty()
  @IsNumber()
  @ApiProperty({
    description: 'Plan ID',
    example: 1,
  })
  planId: number;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'Payment method',
    example: 'paypal',
  })
  paymentMethod: string;

  @IsNotEmpty()
  @IsString()
  @ApiProperty({
    description: 'Currency',
    example: 'USD',
  })
  currency: string;
}
