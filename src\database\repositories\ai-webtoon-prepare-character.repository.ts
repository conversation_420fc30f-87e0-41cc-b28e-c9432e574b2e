import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonPrepareCharacterEntity } from '../entities/ai-webtoon-prepare-character.entity';

@Injectable()
export class AiWebtoonPrepareCharacterRepository extends Repository<AiWebtoonPrepareCharacterEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonPrepareCharacterEntity, dataSource.createEntityManager());
  }
}