import { Entity, Column, Index } from 'typeorm';
import { DefaultEntity } from './default.entity';

export enum UserPlatform {
  PC = 'pc',
  MOBILE = 'mobile',
}

@Entity('user_analytics')
@Index(['date', 'hour', 'platform'], { unique: true })
export class UserAnalyticsEntity extends DefaultEntity {
  @Column({ type: 'date' })
  @Index()
  date: Date;

  @Column({ type: 'tinyint', default: 0 })
  hour: number;

  @Column({ type: 'enum', enum: UserPlatform, nullable: true })
  platform: UserPlatform;

  @Column({ type: 'int', default: 0 })
  activeUsers: number;

  @Column({ type: 'int', default: 0 })
  newRegistrations: number;

  @Column({ type: 'int', default: 0 })
  deletedAccounts: number;

  @Column({ type: 'int', default: 0 })
  loginCount: number;

  @Column({ type: 'int', default: 0 })
  uniqueLogins: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;
}
