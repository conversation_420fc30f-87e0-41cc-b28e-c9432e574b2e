import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsBoolean, IsNumber, Min } from 'class-validator';
import { Transform } from 'class-transformer';

export class UpdateAutoReplyDto {
  @ApiProperty({ 
    description: 'Enable or disable auto reply', 
    required: false,
    example: true
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value === 'true';
    }
    return value;
  })
  enabled?: boolean;

  @ApiProperty({ 
    description: 'Auto reply message content', 
    required: false,
    example: 'Thank you for contacting us. We have received your inquiry and will respond within 24 hours.'
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  content?: string;

  @ApiProperty({ 
    description: 'Delay in minutes before sending auto reply', 
    required: false,
    example: 2
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return parseInt(value, 10);
    }
    return value;
  })
  delay_minutes?: number;

  @ApiProperty({ 
    description: 'Admin username for auto replies', 
    required: false,
    example: 'admin'
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  admin_username?: string;

  @ApiProperty({ 
    description: 'Email template name for auto replies', 
    required: false,
    example: 'qa-auto-reply'
  })
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  email_template?: string;

  @ApiProperty({ 
    description: 'Subject prefix for auto reply emails', 
    required: false,
    example: 'Re: '
  })
  @IsOptional()
  @IsString()
  subject_prefix?: string;
}