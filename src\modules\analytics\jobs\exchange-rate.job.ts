import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { ExchangeRateDbService } from '../services/exchange-rate-db.service';
import {
  ExchangeRateEntity,
  SupportedCurrency,
  SupportedLanguage,
  SupportedRegion,
} from '../../../database/entities/exchange-rate.entity';

@Injectable()
export class ExchangeRateJob {
  private readonly logger = new Logger(ExchangeRateJob.name);

  constructor(private readonly exchangeRateDbService: ExchangeRateDbService) {}

  /**
   * Update exchange rates every minute (for testing)
   * In production, this should be daily: @Cron('0 1 * * *')
   */
  @Cron('*/1 * * * *') // Every 30 minutes for testing
  async updateExchangeRates() {
    this.logger.log('Starting exchange rate update job...');

    try {
      const targetDate = new Date();

      // Generate fake exchange rates with some variation
      const baseRates = await this.generateFakeRates();
      const ratesToUpsert: Partial<ExchangeRateEntity>[] = [];

      // Create rates for different currencies and regions
      for (const [currency, baseRate] of Object.entries(baseRates)) {
        if (currency === 'KRW') continue; // Skip KRW to KRW

        // Global rate (no language/region)
        ratesToUpsert.push({
          date: targetDate,
          currency: currency as SupportedCurrency,
          rateToKRW: baseRate,
          originalRate: baseRate,
          adjustmentPercentage: 0,
          source: 'fake-api',
          status: 'active' as const,
          metadata: {
            updateTime: new Date().toISOString(),
            variation: this.getRandomVariation(),
          },
        });

        // Region-specific rates with adjustments
        const regionalAdjustments = [
          {
            language: SupportedLanguage.KO,
            region: SupportedRegion.KR,
            adjustment: -0.5,
          }, // Better rate for Korea
          {
            language: SupportedLanguage.JA,
            region: SupportedRegion.JP,
            adjustment: -0.3,
          }, // Better rate for Japan
          {
            language: SupportedLanguage.EN,
            region: SupportedRegion.US,
            adjustment: 0.2,
          }, // Slightly worse for US
          {
            language: SupportedLanguage.DE,
            region: SupportedRegion.DE,
            adjustment: -0.1,
          }, // Slightly better for Germany
          {
            language: SupportedLanguage.ZH,
            region: SupportedRegion.CN,
            adjustment: 0.1,
          }, // Slightly worse for China
        ];

        for (const { language, region, adjustment } of regionalAdjustments) {
          const adjustedRate = baseRate * (1 + adjustment / 100);

          ratesToUpsert.push({
            date: targetDate,
            currency: currency as SupportedCurrency,
            rateToKRW: Number(adjustedRate.toFixed(4)),
            language,
            region,
            originalRate: baseRate,
            adjustmentPercentage: adjustment,
            source: 'fake-api',
            status: 'active' as const,
            metadata: {
              updateTime: new Date().toISOString(),
              variation: this.getRandomVariation(),
              regionalAdjustment: adjustment,
            },
          });
        }
      }

      // Upsert all rates
      await this.exchangeRateDbService.upsertExchangeRates(ratesToUpsert);

      // Deactivate old rates (older than 3 days for testing)
      await this.exchangeRateDbService.deactivateOldRates(3);

      this.logger.log(
        `Successfully updated ${ratesToUpsert.length} exchange rates for ${Object.keys(baseRates).length} currencies`,
      );

      // Log sample rates for debugging
      this.logSampleRates(baseRates);
    } catch (error) {
      this.logger.error(
        'Failed to update exchange rates:',
        error.message,
        error.stack,
      );
    }
  }

  /**
   * Generate fake exchange rates with realistic variations
   */
  private generateFakeRates(): Promise<Record<string, number>> {
    // Base rates (realistic as of 2025)
    const baseRates = {
      USD: 1320.0,
      EUR: 1430.0,
      JPY: 9.2,
      GBP: 1650.0,
      CNY: 185.0,
    };

    // Add small random variations (-2% to +2%)
    const rates: Record<string, number> = {};
    for (const [currency, baseRate] of Object.entries(baseRates)) {
      const variation = (Math.random() - 0.5) * 0.04; // -2% to +2%
      const newRate = baseRate * (1 + variation);
      rates[currency] = Number(newRate.toFixed(4));
    }

    return Promise.resolve(rates);
  }

  /**
   * Get random variation percentage for metadata
   */
  private getRandomVariation(): number {
    return Number(((Math.random() - 0.5) * 0.04 * 100).toFixed(2)); // -2% to +2%
  }

  /**
   * Log sample rates for debugging
   */
  private logSampleRates(rates: Record<string, number>): void {
    this.logger.debug('Generated exchange rates:');
    for (const [currency, rate] of Object.entries(rates)) {
      this.logger.debug(`  ${currency}: ${rate} KRW`);
    }
  }

  /**
   * Manual trigger for testing (can be called from controller)
   */
  async triggerManualUpdate(): Promise<{
    success: boolean;
    message: string;
    ratesCount?: number;
  }> {
    try {
      this.logger.log('Manual exchange rate update triggered');
      await this.updateExchangeRates();

      const latestRates = await this.exchangeRateDbService.getLatestRates();

      return {
        success: true,
        message: 'Exchange rates updated successfully',
        ratesCount: latestRates.length,
      };
    } catch (error) {
      this.logger.error('Manual update failed:', error);
      return {
        success: false,
        message: `Update failed: ${error.message}`,
      };
    }
  }
}
