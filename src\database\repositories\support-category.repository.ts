import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  SupportCategoryEntity,
  CategoryStatus,
} from '../entities/support-category.entity';
import { In } from 'typeorm';

@Injectable()
export class SupportCategoryRepository extends Repository<SupportCategoryEntity> {
  constructor(
    @InjectRepository(SupportCategoryEntity)
    private repository: Repository<SupportCategoryEntity>,
  ) {
    super(repository.target, repository.manager, repository.queryRunner);
  }

  async findActiveCategories() {
    return await this.find({
      where: { status: CategoryStatus.ACTIVE },
      order: { order: 'ASC', title: 'ASC' },
    });
  }

  async findActiveCategoryById(id: number) {
    return await this.findOne({
      where: {
        id,
        status: CategoryStatus.ACTIVE,
      },
    });
  }

  async findCategoryByName(name: string) {
    return await this.findOne({
      where: { name },
    });
  }

  async findWithStats(page: number = 1, limit: number = 10) {
    const queryBuilder = this.createQueryBuilder('category')
      .leftJoin(
        'support_ticket',
        'ticket',
        'ticket.categoryId = category.id AND ticket.deletedAt IS NULL',
      )
      .select([
        'category.id',
        'category.name',
        'category.title',
        'category.description',
        'category.status',
        'category.order',
        'category.expectedResponseTime',
        'category.expectedResolutionTime',
        'category.createdAt',
        'category.updatedAt',
      ])
      .addSelect('COUNT(ticket.id)', 'totalTickets')
      .addSelect(
        'COUNT(CASE WHEN ticket.status IN (:...activeStatuses) THEN 1 END)',
        'activeTickets',
      )
      .where('category.deletedAt IS NULL')
      .setParameters({
        activeStatuses: ['open', 'assigned', 'in_progress', 'pending_user'],
      })
      .groupBy('category.id')
      .orderBy('category.order', 'ASC')
      .addOrderBy('category.createdAt', 'DESC');

    const [data, total] = await Promise.all([
      queryBuilder
        .skip((page - 1) * limit)
        .take(limit)
        .getRawAndEntities(),
      this.count({ where: { deletedAt: undefined } }),
    ]);

    const results = data.entities.map((category, index) => ({
      ...category,
      totalTickets: parseInt(data.raw[index].totalTickets) || 0,
      activeTickets: parseInt(data.raw[index].activeTickets) || 0,
    }));

    return [results, total];
  }

  async updateCategoryStatus(id: number, status: CategoryStatus) {
    const category = await this.findOne({ where: { id } });
    if (!category) {
      return null;
    }

    category.status = status;
    return await this.save(category);
  }

  async softDeleteMany(ids: number[]) {
    return await this.softDelete({ id: In(ids) });
  }

  async validateCategoryExists(
    categoryId: number,
  ): Promise<SupportCategoryEntity | null> {
    if (!categoryId) return null;

    const activeCategory = await this.findOne({
      where: { status: CategoryStatus.ACTIVE },
    });

    if (!activeCategory) return null;

    const categoryById = await this.findOne({
      where: { id: categoryId },
    });

    return categoryById || null;
  }
}
