import { Column, <PERSON>tity, OneToMany } from 'typeorm';
import { AiWebtoonTrainingCharacterSessionEntity } from './ai-webtoon-training-character-session.entity';
import { AiWebtoonTrainingLogEntity } from './ai-webtoon-training-log.entity';
import { DefaultEntity } from './default.entity';
import {
  EAiWebtoonCharacterGender,
  EAiWebtoonCharacterStatus,
  EAiWebtoonCharacterType,
} from 'src/common/ai-webtoon.enum';
import {
  IAiItemDataTrainCharacter,
  IAiItemSamplePromptCharacter,
} from 'src/common/interfaces/ai-webtoon/ai-webtoon-character.interface';

@Entity('ai_webtoon_character')
export class AiWebtoonCharacterEntity extends DefaultEntity {
  @Column({ type: 'varchar' })
  name: string;

  @Column({ type: 'varchar' })
  avatar: string;

  @Column({
    type: 'enum',
    enum: EAiWebtoonCharacterGender,
    nullable: true,
  })
  gender?: EAiWebtoonCharacterGender;

  @Column({
    type: 'enum',
    enum: EAiWebtoonCharacterStatus,
    default: EAiWebtoonCharacterStatus.NEW,
  })
  status: EAiWebtoonCharacterStatus;

  @Column({ name: 'images_traineds', type: 'json', nullable: true })
  imagesTraineds: IAiItemDataTrainCharacter[] | null;

  @Column({ name: 'images_preparing_trainings', type: 'json', nullable: true })
  imagesPreparingTrainings: IAiItemDataTrainCharacter[] | null;

  @Column({
    type: 'enum',
    enum: EAiWebtoonCharacterType,
    default: EAiWebtoonCharacterType.FLUX,
  })
  type: EAiWebtoonCharacterType;

  @Column({ name: 'sample_prompts', type: 'json', nullable: true })
  samplePrompts: IAiItemSamplePromptCharacter[] | null;

  @Column({ name: 'model_url', type: 'varchar', nullable: true })
  modelUrl?: string | null;

  @Column({ name: 'stories_id_used', type: 'json', nullable: true })
  storiesIdUsed: number[] | null;

  @Column({ name: 'api_gen_image_synceds', type: 'json', nullable: true })
  apiGenImageSynceds: string[] | null;

  @Column({
    name: 'description_prompt_preparing_training',
    type: 'text',
    nullable: true,
  })
  descriptionPromptPreparingTraining: string | null;

  @Column({ name: 'training_order_time', type: 'datetime', nullable: true })
  trainingOrderTime?: Date | null;

  @OneToMany(
    () => AiWebtoonTrainingCharacterSessionEntity,
    (aiWebtoonTrainingCharacterSession) =>
      aiWebtoonTrainingCharacterSession.aiWebtoonCharacter,
  )
  session?: AiWebtoonTrainingCharacterSessionEntity[];

  @OneToMany(
    () => AiWebtoonTrainingLogEntity,
    (aiWebtoonTrainingLog) => aiWebtoonTrainingLog.aiWebtoonCharacter,
  )
  logs: AiWebtoonTrainingLogEntity[];

  countImage: number;
  countStoryUsed: number;
  loss: number | null;
  percent: number | null;
  isTesting: boolean;
}
