import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserAnalyticsController } from './user-analytics.controller';
import { UserAnalyticsService } from './user-analytics.service';
import { UserAnalyticsEntity } from '../../../database/entities/user-analytics.entity';

@Module({
  imports: [TypeOrmModule.forFeature([UserAnalyticsEntity])],
  controllers: [UserAnalyticsController],
  providers: [UserAnalyticsService],
  exports: [UserAnalyticsService],
})
export class UserAnalyticsModule {}
