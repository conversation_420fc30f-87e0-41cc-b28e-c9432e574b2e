import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateContentTable1749109737791 implements MigrationInterface {
  name = 'UpdateContentTable1749109737791';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`content\``);
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`image\``);
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`imagePc\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`imageMobile\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`expectedReleaseDate\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`authorId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD UNIQUE INDEX \`IDX_93951308013e484eb6ab7888a1\` (\`authorId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP COLUMN \`description\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`description\` text NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` CHANGE \`type\` \`type\` enum ('classification', 'date', 'language', 'payment_format') NOT NULL DEFAULT 'classification'`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_93951308013e484eb6ab7888a1\` ON \`content\` (\`authorId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD CONSTRAINT \`FK_93951308013e484eb6ab7888a1b\` FOREIGN KEY (\`authorId\`) REFERENCES \`author\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP FOREIGN KEY \`FK_93951308013e484eb6ab7888a1b\``,
    );
    await queryRunner.query(
      `DROP INDEX \`REL_93951308013e484eb6ab7888a1\` ON \`content\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content_setting\` CHANGE \`type\` \`type\` enum ('classification', 'date') NOT NULL DEFAULT 'classification'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP COLUMN \`description\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`description\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP INDEX \`IDX_93951308013e484eb6ab7888a1\``,
    );
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`authorId\``);
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP COLUMN \`expectedReleaseDate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP COLUMN \`imageMobile\``,
    );
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`imagePc\``);
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`image\` varchar(255) NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`content\` longtext NOT NULL`,
    );
  }
}
