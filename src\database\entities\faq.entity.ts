import { <PERSON><PERSON><PERSON>, Column, <PERSON>T<PERSON><PERSON>ne, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { AdminEntity } from './admin.entity';
import { SupportCategoryEntity } from './support-category.entity';
import { DefaultEntity } from './default.entity';

export enum FaqStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('faq')
@Index(['status', 'categoryId'])
@Index(['createdAt'])
@Index(['title'])
export class FaqEntity extends DefaultEntity {
  @Column({ name: 'admin_id' })
  adminId: number;

  @Column()
  title: string;

  @Column({ name: 'category_id', nullable: true })
  categoryId: number;

  @Column({
    type: 'enum',
    enum: FaqStatus,
    default: FaqStatus.ACTIVE,
  })
  status: FaqStatus;

  @Column({ type: 'text' })
  content: string; // HTML content

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ default: 1 })
  order: number; // Display order

  // Relations
  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;

  @ManyToOne(() => SupportCategoryEntity)
  @JoinColumn({ name: 'category_id' })
  category: SupportCategoryEntity;
}
