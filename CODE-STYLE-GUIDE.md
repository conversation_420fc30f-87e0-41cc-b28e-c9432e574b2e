# Code Style Guide

## NestJS Digital Content Platform - Coding Standards & Best Practices

This document outlines the coding standards, conventions, and best practices for the **NestJS Digital Content Platform** project. All team members should adhere to these guidelines to maintain code consistency, readability, and maintainability.

---

## 📋 Table of Contents

1. [Project Architecture](#project-architecture)
2. [File & Directory Naming](#file--directory-naming)
3. [Code Naming Conventions](#code-naming-conventions)
4. [Module Structure](#module-structure)
5. [Controller Guidelines](#controller-guidelines)
6. [Service Layer Standards](#service-layer-standards)
7. [Database & Repository Patterns](#database--repository-patterns)
8. [DTO & Validation Standards](#dto--validation-standards)
9. [API Design Guidelines](#api-design-guidelines)
10. [Security Best Practices](#security-best-practices)
11. [Error Handling](#error-handling)
12. [Analytics Integration](#analytics-integration)
13. [Documentation Standards](#documentation-standards)
14. [Testing Guidelines](#testing-guidelines)
15. [Performance Optimization](#performance-optimization)

---

## 🏗️ Project Architecture

### Modular Design Principles

**✅ DO:**
```typescript
// Clear separation between user and admin modules
src/modules/
├── admin/          # Admin panel modules (Protected)
├── auth/          # User authentication  
├── content/       # User-facing content
├── analytics/     # Real-time analytics
└── user/         # User profile management
```

**❌ DON'T:**
```typescript
// Mixing admin and user logic in same module
src/modules/
├── mixed-content/  # ❌ Unclear separation
└── everything/     # ❌ Monolithic structure
```

### Layered Architecture

Follow the **Controller → Service → Repository** pattern:

```typescript
@Controller('faq')
export class FaqController {
  constructor(private readonly faqService: FaqService) {}
  
  @Get()
  async getFaqs(@Query() query: FaqQueryDto) {
    return this.faqService.getFaqs(query);
  }
}

@Injectable()
export class FaqService {
  constructor(private readonly faqRepository: FaqRepository) {}
  
  async getFaqs(query: FaqQueryDto) {
    return this.faqRepository.findWithFilters(query);
  }
}
```

---

## 📁 File & Directory Naming

### File Naming Conventions

| File Type | Convention | Example |
|-----------|------------|---------|
| **Controllers** | `{module}.controller.ts` | `faq.controller.ts` |
| **Services** | `{module}.service.ts` | `faq.service.ts` |
| **DTOs** | `{action}-{entity}.dto.ts` | `create-faq.dto.ts` |
| **Entities** | `{entity}.entity.ts` | `faq.entity.ts` |
| **Repositories** | `{entity}.repository.ts` | `faq.repository.ts` |
| **Guards** | `{type}-{purpose}.guard.ts` | `jwt-auth.guard.ts` |
| **Decorators** | `{purpose}.decorator.ts` | `user.decorator.ts` |
| **Modules** | `{module}.module.ts` | `faq.module.ts` |

### Directory Structure

```typescript
// Standard module structure
src/modules/{module}/
├── controllers/          # API endpoints
├── services/            # Business logic
├── dtos/               # Data Transfer Objects  
├── guards/             # Authentication/Authorization
├── decorators/         # Custom decorators
├── interfaces/         # TypeScript interfaces
├── enums/             # Enumerations
└── {module}.module.ts  # Module definition
```

---

## 🏷️ Code Naming Conventions

### TypeScript Naming Standards

| Element | Convention | Example |
|---------|------------|---------|
| **Classes** | PascalCase | `FaqAdminService` |
| **Interfaces** | PascalCase + "Interface" | `UserInterface` |
| **Methods** | camelCase | `createFaq()` |
| **Variables** | camelCase | `faqList` |
| **Constants** | UPPER_SNAKE_CASE | `MESSAGE_CONFIG` |
| **Enums** | PascalCase | `FaqStatus` |
| **Enum Values** | UPPER_SNAKE_CASE | `ACTIVE`, `INACTIVE` |

### Method Naming Patterns

**✅ DO:**
```typescript
// Clear, descriptive method names
async createFaq(createFaqDto: CreateFaqDto): Promise<FaqEntity>
async getFaqById(id: number): Promise<FaqEntity>
async updateFaqStatus(id: number, status: FaqStatus): Promise<void>
async deleteFaq(id: number): Promise<boolean>
```

**❌ DON'T:**
```typescript
// Vague or abbreviated method names
async create(data: any): Promise<any>         // ❌ Too generic
async getFAQ(id: number): Promise<any>        // ❌ Inconsistent casing
async updateStat(id: number): Promise<void>   // ❌ Unclear abbreviation
```

---

## 🧩 Module Structure

### Standard Module Template

```typescript
// faq.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FaqEntity } from '../../database/entities/faq.entity';
import { FaqRepository } from '../../database/repositories/faq.repository';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';

// Controllers
import { FaqController } from './controllers/faq.controller';

// Services
import { FaqService } from './services/faq.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([FaqEntity]),
    AnalyticsQueueModule,
  ],
  controllers: [FaqController],
  providers: [
    FaqRepository,
    FaqService,
  ],
  exports: [FaqService],
})
export class FaqModule {}
```

### Module Dependencies

**✅ DO:**
- Import only necessary modules
- Export services that other modules need
- Use `forFeature()` for entity registration

**❌ DON'T:**
- Create circular dependencies
- Import entire modules when only specific services are needed

---

## 🎮 Controller Guidelines

### Controller Structure

```typescript
@Controller('faq')
@UseGuards(OptionalJwtAuthGuard)
@ApiTags('FAQ')
export class FaqController {
  constructor(private readonly faqService: FaqService) {}

  @Get()
  @ApiOperation({
    summary: 'Get FAQs',
    description: 'Retrieve list of active FAQs with optional category filtering',
  })
  @ApiQuery({ name: 'categoryId', required: false, type: Number })
  @ApiResponse({
    status: 200,
    description: 'FAQs retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: { type: 'array' },
        pagination: { type: 'object' },
      },
    },
  })
  async getFaqs(
    @Query() query: FaqListQueryDto,
    @Request() req?: any,
  ) {
    const result = await this.faqService.getFaqs(query);
    
    // Track analytics (non-blocking)
    if (req?.user?.id) {
      this.trackFaqView(req.user.id, query);
    }
    
    return result;
  }

  private async trackFaqView(userId: number, query: any): Promise<void> {
    try {
      await this.analyticsQueueService.trackUserActivity({
        userId,
        activityType: 'faq_browse',
        metadata: query,
      });
    } catch (error) {
      // Analytics failure doesn't block main flow
      console.error('Analytics tracking failed:', error);
    }
  }
}
```

### Controller Best Practices

**✅ DO:**
- Use descriptive HTTP status codes
- Add comprehensive Swagger documentation
- Handle both success and error responses
- Keep controllers thin (delegate to services)
- Use DTOs for request/response validation

**❌ DON'T:**
- Put business logic in controllers
- Use `any` types for parameters
- Skip error handling
- Forget Swagger documentation

---

## ⚙️ Service Layer Standards

### Service Structure

```typescript
@Injectable()
export class FaqService {
  constructor(
    private readonly faqRepository: FaqRepository,
    private readonly supportCategoryRepository: SupportCategoryRepository,
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}

  async createFaq(
    createFaqDto: CreateFaqDto,
    adminId: number,
  ): Promise<FaqEntity> {
    // 1. Validation
    await this.validateCreateFaq(createFaqDto);
    
    // 2. Business logic
    const faq = await this.faqRepository.save({
      ...createFaqDto,
      adminId,
      content: DOMPurify.sanitize(createFaqDto.content),
      status: FaqStatus.ACTIVE,
    });
    
    // 3. Side effects (non-blocking)
    this.trackFaqCreation(adminId, faq.id).catch(console.error);
    
    return faq;
  }

  private async validateCreateFaq(dto: CreateFaqDto): Promise<void> {
    if (dto.categoryId) {
      const category = await this.supportCategoryRepository.findOne({
        where: { id: dto.categoryId },
      });
      
      if (!category) {
        throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
      }
    }
  }

  private async trackFaqCreation(adminId: number, faqId: number): Promise<void> {
    await this.analyticsQueueService.trackAdminActivity({
      adminId,
      activityType: 'faq_created',
      metadata: { faqId },
    });
  }
}
```

### Service Guidelines

**✅ DO:**
- Implement comprehensive error handling
- Use transactions for multi-step operations
- Sanitize user input (HTML content)
- Track analytics for user actions
- Validate input data before processing

**❌ DON'T:**
- Let analytics failures block main operations
- Skip input validation
- Expose internal errors to users
- Mix database operations with business logic

---

## 🗄️ Database & Repository Patterns

### Entity Definition

```typescript
@Entity('faq')
@Index(['status', 'categoryId'])
@Index(['createdAt'])
export class FaqEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'int', nullable: false })
  adminId: number;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'int', nullable: true })
  categoryId: number;

  @Column({
    type: 'enum',
    enum: FaqStatus,
    default: FaqStatus.ACTIVE,
  })
  status: FaqStatus;

  @Column({ type: 'text', nullable: false })
  content: string;

  @Column({ type: 'int', default: 0 })
  viewCount: number;

  @Column({ type: 'int', default: 1 })
  order: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  // Relations
  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'adminId' })
  admin: AdminEntity;

  @ManyToOne(() => SupportCategoryEntity)
  @JoinColumn({ name: 'categoryId' })
  category: SupportCategoryEntity;
}
```

### Repository Pattern

```typescript
@Injectable()
export class FaqRepository extends Repository<FaqEntity> {
  constructor(private dataSource: DataSource) {
    super(FaqEntity, dataSource.createEntityManager());
  }

  async findAdminFaqList(
    page: number,
    limit: number,
    filters?: FaqFilters,
  ): Promise<[FaqEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('faq')
      .leftJoinAndSelect('faq.category', 'category')
      .leftJoinAndSelect('faq.admin', 'admin');

    // Apply filters
    if (filters?.status) {
      queryBuilder.andWhere('faq.status = :status', { status: filters.status });
    }

    if (filters?.categoryId) {
      queryBuilder.andWhere('faq.categoryId = :categoryId', { 
        categoryId: filters.categoryId 
      });
    }

    if (filters?.search) {
      queryBuilder.andWhere(
        '(faq.title LIKE :search OR faq.content LIKE :search)',
        { search: `%${filters.search}%` }
      );
    }

    return queryBuilder
      .orderBy('faq.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .getManyAndCount();
  }

  async findUserFaqDetail(id: number): Promise<FaqEntity | null> {
    return this.findOne({
      where: { 
        id, 
        status: FaqStatus.ACTIVE 
      },
      relations: ['category'],
    });
  }

  async incrementViewCount(id: number): Promise<void> {
    await this.increment({ id }, 'viewCount', 1);
  }
}
```

---

## 📝 DTO & Validation Standards

### DTO Structure

```typescript
export class CreateFaqDto {
  @ApiProperty({
    description: 'FAQ title',
    example: 'How to reset password?',
    maxLength: 255,
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiProperty({
    description: 'FAQ content (HTML allowed)',
    example: '<p>Follow these steps to reset your password...</p>',
  })
  @IsNotEmpty()
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Support category ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  categoryId?: number;

  @ApiProperty({
    description: 'FAQ status',
    enum: FaqStatus,
    default: FaqStatus.ACTIVE,
    required: false,
  })
  @IsOptional()
  @IsEnum(FaqStatus)
  status?: FaqStatus;

  @ApiProperty({
    description: 'Display order',
    example: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Min(1)
  order?: number;
}
```

### Validation Guidelines

**✅ DO:**
- Use class-validator decorators
- Provide meaningful error messages
- Validate all input parameters
- Use appropriate validation rules (email, min/max, etc.)
- Document all DTO properties with Swagger

**❌ DON'T:**
- Skip validation on sensitive operations
- Use `any` types in DTOs
- Forget to validate optional parameters

---

## 🌐 API Design Guidelines

### REST API Standards

| HTTP Method | Purpose | Example |
|-------------|---------|---------|
| **GET** | Retrieve data | `GET /faq` |
| **POST** | Create new resource | `POST /faq` |
| **PUT** | Update existing resource | `PUT /faq/:id` |
| **DELETE** | Delete resource | `DELETE /faq/:id` |

### Response Format Standards

```typescript
// Success Response
{
  "success": true,
  "data": [...],
  "message": "Operation successful",
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5
  }
}

// Error Response
{
  "success": false,
  "error": {
    "code": "FAQ_NOT_FOUND",
    "message": "FAQ not found",
    "statusCode": 404
  }
}
```

### Swagger Documentation

```typescript
@ApiOperation({
  summary: 'Create FAQ',
  description: 'Create a new frequently asked question',
})
@ApiResponse({
  status: 201,
  description: 'FAQ created successfully',
  type: FaqEntity,
})
@ApiResponse({
  status: 400,
  description: 'Invalid input data',
})
@ApiResponse({
  status: 401,
  description: 'Unauthorized access',
})
```

---

## 🔒 Security Best Practices

### Authentication Patterns

```typescript
// User Authentication
@UseGuards(JwtAuthGuard)
export class UserController {}

// Admin Authentication with Permissions
@MenuPermissions('faq-management')
@UseGuards(JwtAdminAuthGuard, MenuPermissionGuard)
export class FaqAdminController {}

// Optional Authentication
@UseGuards(OptionalJwtAuthGuard)
export class PublicController {}
```

### Data Sanitization

```typescript
// HTML Content Sanitization
import * as DOMPurify from 'isomorphic-dompurify';

const sanitizedContent = DOMPurify.sanitize(userInput.content);
```

### Security Guidelines

**✅ DO:**
- Sanitize all user input (especially HTML)
- Use JWT for authentication
- Implement role-based access control
- Validate file uploads (type, size, content)
- Use HTTPS in production

**❌ DON'T:**
- Store plaintext passwords
- Skip input validation
- Expose internal error details
- Use weak JWT secrets

---

## ❌ Error Handling

### Centralized Error Messages

```typescript
// src/common/message.config.ts
export const MESSAGE_CONFIG = {
  // FAQ errors
  FAQ_NOT_FOUND: {
    key: 'faq_not_found',
    message: 'FAQ not found',
  },
  FAQ_ALREADY_EXISTS: {
    key: 'faq_already_exists', 
    message: 'FAQ with this title already exists',
  },
  
  // User errors
  USER_NOT_FOUND: {
    key: 'user_not_found',
    message: 'User not found',
  },
  
  // Validation errors
  INVALID_FILE_TYPE: {
    key: 'invalid_file_type',
    message: 'File type not supported',
  },
};
```

### Exception Handling

```typescript
// Service Layer
async getFaqById(id: number): Promise<FaqEntity> {
  const faq = await this.faqRepository.findOne({ where: { id } });
  
  if (!faq) {
    throw new NotFoundException(MESSAGE_CONFIG.FAQ_NOT_FOUND);
  }
  
  return faq;
}

// Global Exception Filter
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    
    const status = exception instanceof HttpException 
      ? exception.getStatus() 
      : 500;
      
    response.status(status).json({
      success: false,
      error: {
        statusCode: status,
        message: exception.message || 'Internal server error',
        timestamp: new Date().toISOString(),
      },
    });
  }
}
```

---

## 📊 Analytics Integration

### Analytics Tracking Pattern

```typescript
// Non-blocking analytics tracking
async trackUserActivity(
  userId: number, 
  activityType: string, 
  metadata?: any
): Promise<void> {
  try {
    await this.analyticsQueueService.trackUserActivity({
      userId,
      activityType,
      platform: this.detectPlatform(),
      timestamp: Date.now(),
      metadata,
    });
  } catch (error) {
    // Analytics failure should never block main application flow
    console.error('Analytics tracking failed:', error);
  }
}
```

### Analytics Guidelines

**✅ DO:**
- Track all significant user interactions
- Use BullMQ for asynchronous processing
- Include relevant metadata
- Handle analytics failures gracefully

**❌ DON'T:**
- Let analytics failures block user operations
- Track sensitive user data
- Process analytics synchronously

---

## 📚 Documentation Standards

### Code Comments

```typescript
/**
 * Creates a new FAQ entry with proper validation and sanitization
 * 
 * @param createFaqDto - FAQ creation data
 * @param adminId - ID of the admin creating the FAQ
 * @returns Promise<FaqEntity> - Created FAQ entity
 * @throws BadRequestException - When category doesn't exist
 * @throws UnauthorizedException - When admin lacks permissions
 */
async createFaq(
  createFaqDto: CreateFaqDto,
  adminId: number,
): Promise<FaqEntity> {
  // Implementation...
}
```

### README Documentation

Each module should include:
- **Purpose**: What the module does
- **API Endpoints**: Available endpoints with examples  
- **Dependencies**: Required modules and services
- **Configuration**: Environment variables and settings

---

## 🧪 Testing Guidelines

### Unit Test Structure

```typescript
describe('FaqService', () => {
  let service: FaqService;
  let repository: FaqRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        FaqService,
        {
          provide: FaqRepository,
          useValue: {
            save: jest.fn(),
            findOne: jest.fn(),
            findAndCount: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<FaqService>(FaqService);
    repository = module.get<FaqRepository>(FaqRepository);
  });

  describe('createFaq', () => {
    it('should create FAQ successfully', async () => {
      // Arrange
      const createDto: CreateFaqDto = {
        title: 'Test FAQ',
        content: 'Test content',
      };
      const expectedFaq = { id: 1, ...createDto };
      
      jest.spyOn(repository, 'save').mockResolvedValue(expectedFaq);

      // Act
      const result = await service.createFaq(createDto, 1);

      // Assert
      expect(result).toEqual(expectedFaq);
      expect(repository.save).toHaveBeenCalledWith({
        ...createDto,
        adminId: 1,
        content: expect.any(String), // Sanitized content
      });
    });
  });
});
```

### Testing Standards

**✅ DO:**
- Write unit tests for all services
- Test both success and error scenarios
- Mock external dependencies
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)

**❌ DON'T:**
- Test implementation details
- Skip error case testing
- Use real database in unit tests

---

## ⚡ Performance Optimization

### Database Optimization

```typescript
// Proper indexing
@Index(['status', 'categoryId'])
@Index(['createdAt'])
@Entity('faq')
export class FaqEntity {}

// Efficient queries with pagination
async findFaqs(page: number, limit: number): Promise<[FaqEntity[], number]> {
  return this.createQueryBuilder('faq')
    .where('faq.status = :status', { status: FaqStatus.ACTIVE })
    .orderBy('faq.createdAt', 'DESC')
    .skip((page - 1) * limit)
    .take(limit)
    .getManyAndCount();
}
```

### Caching Strategy

```typescript
// Redis caching for frequently accessed data
@Injectable()
export class FaqService {
  async getFaqs(): Promise<FaqEntity[]> {
    const cacheKey = 'active_faqs';
    
    // Try cache first
    const cached = await this.redisService.get(cacheKey);
    if (cached) {
      return JSON.parse(cached);
    }
    
    // Fetch from database
    const faqs = await this.faqRepository.findActive();
    
    // Cache result
    await this.redisService.setex(cacheKey, 300, JSON.stringify(faqs)); // 5 min
    
    return faqs;
  }
}
```

---

## 🔧 Development Tools

### Required Tools

- **Node.js** (v18+)
- **TypeScript** (v5+)
- **ESLint** + **Prettier** for code formatting
- **Husky** for Git hooks
- **Jest** for testing

### Development Commands

```bash
# Development
npm run start:dev      # Start with hot reload
npm run start:debug    # Start in debug mode

# Code Quality  
npm run lint          # Check code style
npm run format        # Format code
npm run test          # Run tests
npm run test:cov      # Test coverage

# Database
npm run migration:generate  # Generate migration
npm run migration:run      # Run migrations
```

---

## 📋 Code Review Checklist

### Before Committing

- [ ] **Code compiles** without errors or warnings
- [ ] **Tests pass** and coverage is maintained
- [ ] **ESLint/Prettier** formatting applied
- [ ] **TypeScript types** are properly defined
- [ ] **Error handling** is comprehensive
- [ ] **Security considerations** addressed
- [ ] **Documentation** updated
- [ ] **Analytics integration** added where appropriate

### Code Review Focus Areas

- [ ] **Architecture**: Follows established patterns
- [ ] **Security**: Input validation and sanitization
- [ ] **Performance**: Database queries optimized
- [ ] **Maintainability**: Code is readable and well-structured
- [ ] **Testing**: Adequate test coverage
- [ ] **Documentation**: Swagger docs updated

---

## 🎯 Summary

This Code Style Guide ensures:

✅ **Consistency** across all team members  
✅ **Maintainability** for long-term project health  
✅ **Security** through established patterns  
✅ **Performance** via optimization guidelines  
✅ **Quality** through comprehensive testing  

**Remember**: These guidelines evolve with the project. Update this document as new patterns emerge and best practices are established.

---

**Last Updated**: January 2025  
**Version**: 1.0  
**Next Review**: March 2025