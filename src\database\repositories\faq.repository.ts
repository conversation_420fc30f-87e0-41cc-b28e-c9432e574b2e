import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { FaqEntity, FaqStatus } from '../entities/faq.entity';

@Injectable()
export class FaqRepository extends Repository<FaqEntity> {
  constructor(private dataSource: DataSource) {
    super(FaqEntity, dataSource.createEntityManager());
  }

  // Admin queries
  async findAdminFaqList(
    page: number = 1,
    limit: number = 20,
    filters?: {
      startDate?: string;
      endDate?: string;
      categoryId?: number;
      status?: FaqStatus;
      search?: string;
    },
  ): Promise<[FaqEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('faq')
      .leftJoinAndSelect('faq.admin', 'admin')
      .leftJoinAndSelect('faq.category', 'category')
      .where('faq.deletedAt IS NULL');

    // Apply filters
    if (filters?.startDate && filters?.endDate) {
      queryBuilder.andWhere(
        'DATE(faq.createdAt) BETWEEN :startDate AND :endDate',
        {
          startDate: filters.startDate,
          endDate: filters.endDate,
        },
      );
    }

    if (filters?.categoryId) {
      queryBuilder.andWhere('faq.categoryId = :categoryId', {
        categoryId: filters.categoryId,
      });
    }

    if (filters?.status) {
      queryBuilder.andWhere('faq.status = :status', {
        status: filters.status,
      });
    }

    if (filters?.search) {
      queryBuilder.andWhere('faq.title LIKE :search', {
        search: `%${filters.search}%`,
      });
    }

    queryBuilder
      .orderBy('faq.order', 'ASC')
      .addOrderBy('faq.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    return queryBuilder.getManyAndCount();
  }

  // User queries
  async findUserFaqList(
    page: number = 1,
    limit: number = 20,
    categoryId?: number,
  ): Promise<[FaqEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('faq')
      .leftJoinAndSelect('faq.category', 'category')
      .where('faq.status = :status', { status: FaqStatus.ACTIVE })
      .andWhere('faq.deletedAt IS NULL');

    if (categoryId) {
      queryBuilder.andWhere('faq.categoryId = :categoryId', { categoryId });
    }

    queryBuilder
      .orderBy('faq.order', 'ASC')
      .addOrderBy('faq.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    return queryBuilder.getManyAndCount();
  }

  async findUserFaqDetail(id: number): Promise<FaqEntity | null> {
    return this.createQueryBuilder('faq')
      .leftJoinAndSelect('faq.category', 'category')
      .where('faq.id = :id', { id })
      .andWhere('faq.status = :status', { status: FaqStatus.ACTIVE })
      .andWhere('faq.deletedAt IS NULL')
      .getOne();
  }

  async incrementViewCount(id: number): Promise<void> {
    await this.createQueryBuilder()
      .update(FaqEntity)
      .set({ viewCount: () => 'view_count + 1' })
      .where('id = :id', { id })
      .execute();
  }

  async softDeleteMany(ids: number[]): Promise<void> {
    await this.createQueryBuilder()
      .softDelete()
      .where('id IN (:...ids)', { ids })
      .execute();
  }
}
