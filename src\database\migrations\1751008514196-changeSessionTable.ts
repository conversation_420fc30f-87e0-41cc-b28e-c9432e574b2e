import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeSessionTable1751008514196 implements MigrationInterface {
  name = 'ChangeSessionTable1751008514196';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sessions\` DROP COLUMN \`refreshTokenExpiresAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD \`refreshTokenExpiresAt\` bigint NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` DROP COLUMN \`lastUpdateTimestamp\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD \`lastUpdateTimestamp\` bigint NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sessions\` DROP COLUMN \`lastUpdateTimestamp\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD \`lastUpdateTimestamp\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` DROP COLUMN \`refreshTokenExpiresAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD \`refreshTokenExpiresAt\` int NULL`,
    );
  }
}
