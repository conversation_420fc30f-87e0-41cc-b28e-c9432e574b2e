import {
  EAiWebtoonBasicStatus,
  ISyntheticDataImagesItem,
} from 'src/common/ai-webtoon.enum';
import { Column, Entity } from 'typeorm';
import { DefaultEntity } from './default.entity';

@Entity({ name: 'ai_webtoon_synthetic_data' })
export class AiWebtoonSyntheticDataEntity extends DefaultEntity {
  @Column({ name: 'story_name' })
  storyName: string;

  @Column({ name: 'flux_kontext_images', type: 'json', nullable: true })
  fluxContextImages: ISyntheticDataImagesItem[] | null;

  @Column({ name: 'b_lora_images', type: 'json', nullable: true })
  bLoraImages: ISyntheticDataImagesItem[] | null;

  @Column({
    type: 'enum',
    enum: EAiWebtoonBasicStatus,
    default: EAiWebtoonBasicStatus.NOT_GENERATED,
  })
  status: EAiWebtoonBasicStatus;

  @Column({ name: 'api_gen_name', type: 'varchar', nullable: true })
  apiGenName?: string | null;
}
