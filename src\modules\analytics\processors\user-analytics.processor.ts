import { Processor, WorkerHost } from '@nestjs/bullmq';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from 'bullmq';
import {
  UserAnalyticsEntity,
  UserPlatform,
} from '../../../database/entities/user-analytics.entity';
import { UserActivityEventDto } from '../dtos/analytics-events.dto';
import { ANALYTICS_QUEUES } from '../constants/analytics-queues.constant';

@Injectable()
@Processor(ANALYTICS_QUEUES.USER_ANALYTICS)
export class UserAnalyticsProcessor extends WorkerHost {
  private readonly logger = new Logger(UserAnalyticsProcessor.name);

  constructor(
    @InjectRepository(UserAnalyticsEntity)
    private userAnalyticsRepository: Repository<UserAnalyticsEntity>,
  ) {
    super();
  }

  async process(job: Job<UserActivityEventDto>): Promise<void> {
    const { data } = job;
    this.logger.debug(`Processing user activity: ${JSON.stringify(data)}`);

    try {
      const activityDate = new Date(data.timestamp);
      const date = new Date(
        activityDate.getFullYear(),
        activityDate.getMonth(),
        activityDate.getDate(),
      );
      const hour = activityDate.getHours();
      const platform = data.platform as UserPlatform;

      let analytics = await this.userAnalyticsRepository.findOne({
        where: { date, hour, platform },
      });

      if (!analytics) {
        analytics = this.userAnalyticsRepository.create({
          date,
          hour,
          platform,
          activeUsers: 0,
          newRegistrations: 0,
          deletedAccounts: 0,
          loginCount: 0,
          uniqueLogins: 0,
          metadata: {},
        });
      }

      // Update metrics based on activity type
      switch (data.activityType) {
        case 'login':
          analytics.loginCount += 1;
          analytics.activeUsers += 1;
          // Update unique logins
          if (data.userId) {
            await this.updateUniqueLogins(analytics, data.userId);
          }
          break;
        case 'register':
          analytics.newRegistrations += 1;
          analytics.activeUsers += 1;
          break;
        case 'delete':
          analytics.deletedAccounts += 1;
          break;
      }

      await this.userAnalyticsRepository.save(analytics);

      this.logger.debug(`Updated user analytics for ${data.activityType}`);
    } catch (error) {
      this.logger.error(
        `Error processing user activity: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  private async updateUniqueLogins(
    analytics: UserAnalyticsEntity,
    userId: number,
  ): Promise<void> {
    // Check if user already logged in this hour
    const existingLogin = await this.userAnalyticsRepository.query(
      `
      SELECT COUNT(*) as count 
      FROM sessions s 
      WHERE s.userId = ? 
        AND DATE(s.createdAt) = ?
        AND HOUR(s.createdAt) = ?
    `,
      [userId, analytics.date, analytics.hour],
    );

    if (existingLogin[0].count === 0) {
      analytics.uniqueLogins += 1;
    }
  }
}
