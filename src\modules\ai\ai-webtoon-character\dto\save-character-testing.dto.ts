import { ApiProperty } from '@nestjs/swagger';
import { Transform, Type } from 'class-transformer';
import {
  <PERSON>Array,
  IsEnum,
  IsInt,
  IsOptional,
  IsString,
  IsUrl,
  Min,
} from 'class-validator';
import {
  EAiWebtoonCharacterTestedType,
  EAiTestingModel,
} from 'src/common/ai-webtoon.enum';

class TestingDataDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  id1: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  id2: number;

  @ApiProperty({ required: false })
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsOptional()
  @IsString()
  prompt: string;

  @ApiProperty({ required: false })
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsOptional()
  @IsString()
  bg: string;

  @ApiProperty({ required: false })
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsOptional()
  @IsString()
  promptCharacter1: string;

  @ApiProperty({ required: false })
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsOptional()
  @IsString()
  promptCharacter2: string;

  @ApiProperty({ enum: EAiWebtoonCharacterTestedType })
  @IsEnum(EAiWebtoonCharacterTestedType)
  type: EAiWebtoonCharacterTestedType;

  @ApiProperty({ enum: EAiTestingModel, required: false })
  @IsEnum(EAiTestingModel)
  @IsOptional()
  model: EAiTestingModel;
}

export class CharacterTestingDto extends TestingDataDto {
  @ApiProperty()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  numberOfImages: number;
}

export class SaveCharacterTestingDto extends TestingDataDto {
  @ApiProperty()
  @IsArray()
  @IsUrl({}, { each: true })
  images: string[];
}
