import { Seeder } from '@jorgebodega/typeorm-seeding';
import { DataSource } from 'typeorm';
import { AdminStatus } from '../../common/status.enum';
import { AdminRole } from '../../common/role.enum';
import * as bcrypt from 'bcrypt';
import { AdminEntity } from '../entities/admin.entity';

export class AdminSeeder extends Seeder {
  async run(datasource: DataSource) {
    const adminData = await datasource.getRepository(AdminEntity).findOne({
      where: {
        email: '<EMAIL>',
      },
    });
    if (adminData) {
      console.log('\nAdmin already exists');
      return;
    }
    const admin = await datasource.getRepository(AdminEntity).save({
      email: '<EMAIL>',
      username: 'admin',
      password: bcrypt.hashSync('password', 10),
      status: AdminStatus.ACTIVE,
      role: AdminRole.SUPER_ADMIN,
    });
    console.log('\nAdmin created successfully', admin.username);
  }
}
