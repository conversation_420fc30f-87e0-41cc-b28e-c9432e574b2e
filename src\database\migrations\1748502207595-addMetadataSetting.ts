import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddMetadataSetting1748502207595 implements MigrationInterface {
  name = 'AddMetadataSetting1748502207595';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`setting_metadata\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`key\` varchar(255) NOT NULL, \`value\` varchar(255) NOT NULL, \`settingId\` int NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`setting_metadata\` ADD CONSTRAINT \`FK_8bf969a07e040bcd1cf2a8d904c\` FOREIGN KEY (\`settingId\`) REFERENCES \`settings\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`setting_metadata\` DROP FOREIGN KEY \`FK_8bf969a07e040bcd1cf2a8d904c\``,
    );
    await queryRunner.query(`DROP TABLE \`setting_metadata\``);
  }
}
