import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateContentTable1752658283094 implements MigrationInterface {
  name = 'UpdateContentTable1752658283094';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`isAdult\` tinyint NOT NULL DEFAULT 1`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`isAdult\``);
  }
}
