import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';
import { IsNumber } from 'class-validator';
import { IsArray } from 'class-validator';

export class RecentlyWatchedContentDto {
  @ApiProperty({ description: 'Content ID', required: true })
  @IsNotEmpty()
  @IsNumber()
  contentId: number;

  @ApiProperty({ description: 'Episode ID', required: false })
  @IsOptional()
  @IsNumber()
  episodeId?: number;
}

export class MultiRecentlyWatchedContentDto {
  @ApiProperty({
    description: 'Content IDs',
    required: true,
    type: [RecentlyWatchedContentDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => RecentlyWatchedContentDto)
  contentIds: RecentlyWatchedContentDto[];
}
