import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUrl,
  Max,
  <PERSON>,
  ValidateNested,
} from 'class-validator';
import {
  EAiBaseModelGenImageType,
  EAiFrameImageSizeType,
} from 'src/common/ai-webtoon.enum';
import { IsValidSeed } from 'src/common/decorators/is-valid-seed.decorator';

export class ItemUpdateNormalCutDto {
  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  @Min(1)
  id: number;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  info: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  description: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  negativeDescription: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  negativePrompt: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  generalPrompt: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  danbooruPrompt: string;

  @ApiProperty({ required: false })
  @IsInt()
  @Type(() => Number)
  @Min(1)
  @IsOptional()
  numberOfImages: number;

  @ApiProperty({ required: false })
  @IsEnum(EAiFrameImageSizeType)
  @IsOptional()
  imageSizeType: EAiFrameImageSizeType;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  imageWidth: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  imageHeight: number;

  @ApiProperty()
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  characterUuids: string[];

  @ApiProperty({ required: false })
  @IsEnum(EAiBaseModelGenImageType)
  @IsOptional()
  baseModel: EAiBaseModelGenImageType;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Max(1)
  controlNetStrengthCanny: number;

  @ApiProperty({ required: false })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Max(1)
  controlNetStrengthDepth: number;

  @ApiProperty({
    required: false,
    description:
      'Seed value as a string, must be a number less than 18446744073709551615',
  })
  @IsString()
  @IsOptional()
  @IsValidSeed()
  seed: string;

  @ApiProperty({ required: false, description: 'Between 1 and 100' })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  cfgScale: number;

  @ApiProperty({ required: false, description: 'Between 1 and 100' })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  steps: number;
}

export class UpdateNormalCutsDto {
  @ApiProperty({
    example: [
      {
        id: 1,
        info: '',
        description: '',
        negativeDescription: '',
        generalPrompt: '',
        negativePrompt: '',
        danbooruPrompt: '',
        numberOfImages: 1,
        imageSizeType: EAiFrameImageSizeType.DEFAULT,
        imageWidth: 688,
        imageHeight: 720,
        characterUuids: ['uuid1'],
        baseModel: EAiBaseModelGenImageType.FLUX_ADULT,
        controlNetStrengthCanny: 0.6,
        controlNetStrengthDepth: 0.6,
        seed: '123456789',
        cfgScale: 5,
        steps: 28,
      },
    ],
  })
  @IsArray()
  @Type(() => ItemUpdateNormalCutDto)
  @ValidateNested({ each: true })
  cuts: ItemUpdateNormalCutDto[];
}

export class UpdateSelectBgImageInpaintCutDto {
  @ApiProperty()
  @IsUrl()
  @IsNotEmpty()
  bgImage: string;
}
