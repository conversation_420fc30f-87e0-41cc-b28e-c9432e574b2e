import { Modu<PERSON> } from '@nestjs/common';
import { AuthController } from './auth-admin.controller';
import { AuthAdminService } from './auth-admin.service';
import { AdminRepository } from '../../../database/repositories/admin.repository';
import { JwtModule } from '@nestjs/jwt';
import { AdminService } from '../admin/admin.service';
import { AdminEntity } from '../../../database/entities/admin.entity';
import { TypeOrmModule } from '@nestjs/typeorm';
import jwtConfig from '../../../config/jwt.config';
import { MenuRepository } from '../../../database/repositories/menu.repository';
import { MenuAdminRepository } from '../../../database/repositories/menu-admin.repository';
import { JwtAdminStrategy } from '../../../modules/auth/strategy/jwt-admin.strategy';
@Module({
  imports: [
    JwtModule.registerAsync(jwtConfig.asProvider()),
    TypeOrmModule.forFeature([AdminEntity]),
  ],
  controllers: [AuthController],
  providers: [
    AuthAdminService,
    AdminRepository,
    AdminService,
    MenuRepository,
    MenuAdminRepository,
    JwtAdminStrategy,
  ],
})
export class AuthAdminModule {}
