# Analytics System với BullMQ Queue

## Tổng quan

Hệ thống analytics này sử dụng BullMQ queue để xử lý real-time analytics events một cách bất đồng bộ. System gồm 3 loại analytics chính:

- **Content Analytics**: Thống kê về views, reading time, content performance
- **User Analytics**: Thống kê về DAU/MAU, login, registration, account deletion
- **Payment Analytics**: Thống kê về first purchase, repurchase, revenue

## Cấu trúc Database

### Analytics Tables

1. **content_analytics**: <PERSON><PERSON><PERSON> thống kê content theo date/hour
2. **user_analytics**: <PERSON><PERSON><PERSON> thống kê user activity theo date/hour
3. **payment_analytics**: <PERSON><PERSON><PERSON> thống kê payment theo date/hour

### Key Features

- **Time-based aggregation**: Dữ liệu đư<PERSON> group theo date + hour
- **Real-time updates**: Events được process ngay lập tức qua BullMQ
- **Unique constraints**: Tr<PERSON>h duplicate records
- **Metadata support**: <PERSON><PERSON><PERSON> thê<PERSON> thông tin chi tiết dạng JSON

## C<PERSON><PERSON> sử dụng

### 1. Inject AnalyticsQueueService

```typescript
import { Injectable } from '@nestjs/common';
import { AnalyticsQueueService } from '../analytics/services/analytics-queue.service';

@Injectable()
export class YourService {
  constructor(
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}
}
```

### 2. Track Content View

```typescript
// Track single content view
await this.analyticsQueueService.trackContentView({
  userId: 123,
  contentId: 456,
  episodeId: 789,
  viewTime: Date.now(),
  readDuration: 30000, // 30 seconds
  platform: 'mobile',
  metadata: {
    source: 'content_detail',
    userAgent: req.headers['user-agent'],
  },
});

// Track batch content views
await this.analyticsQueueService.batchTrackContentViews([
  {
    userId: 123,
    contentId: 456,
    viewTime: Date.now(),
    platform: 'pc',
  },
  // ... more events
]);
```

### 3. Track User Activity

```typescript
// Track user login
await this.analyticsQueueService.trackUserActivity({
  userId: 123,
  activityType: 'login',
  timestamp: Date.now(),
  platform: 'mobile',
  metadata: {
    source: 'login',
    ipAddress: req.ip,
  },
});

// Track user registration
await this.analyticsQueueService.trackUserActivity({
  userId: 123,
  activityType: 'register',
  timestamp: Date.now(),
  platform: 'pc',
});
```

### 4. Track Payment

```typescript
// Track payment event
await this.analyticsQueueService.trackPayment({
  userId: 123,
  paymentId: 456,
  amount: 99.99,
  currency: 'USD',
  status: 'success',
  paymentType: 'first_purchase',
  platform: 'mobile',
  timestamp: Date.now(),
  metadata: {
    source: 'payment_processing',
    provider: 'stripe',
  },
});
```

## Environment Variables

Thêm vào `.env` file:

```env
# Redis configuration for BullMQ
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0
```

## Queue Configuration

- **Retry policy**: 3 attempts với exponential backoff
- **Job cleanup**: Keep 100 completed jobs, 50 failed jobs
- **Concurrency**: Configurable per queue
- **Priority**: Content (1), User (2), Payment (3)

## Monitoring

### Queue Status

```typescript
// Check queue status (implement in monitoring service)
const contentQueue = await this.contentAnalyticsQueue.getWaiting();
const activeJobs = await this.contentAnalyticsQueue.getActive();
const failedJobs = await this.contentAnalyticsQueue.getFailed();
```

### Database Queries

```sql
-- Daily content views
SELECT date, SUM(viewCount) as totalViews
FROM content_analytics 
WHERE date >= '2025-01-01'
GROUP BY date;

-- Hourly user activity
SELECT date, hour, SUM(loginCount) as totalLogins
FROM user_analytics 
WHERE date = '2025-01-15'
GROUP BY date, hour;

-- Payment revenue by day
SELECT date, SUM(totalAmount) as dailyRevenue
FROM payment_analytics 
WHERE paymentType = 'first_purchase'
GROUP BY date;
```

## Best Practices

1. **Error Handling**: Luôn wrap analytics calls trong try-catch
2. **Platform Detection**: Detect platform từ User-Agent
3. **Metadata**: Include useful context information
4. **Batch Processing**: Use batch methods cho high-volume events
5. **Monitoring**: Monitor queue health và job failures

## Troubleshooting

### Common Issues

1. **Redis Connection**: Kiểm tra Redis server có running không
2. **Queue Stuck**: Restart queue workers nếu jobs bị stuck
3. **Database Locks**: Monitor slow queries và optimize indexes
4. **Memory Usage**: Monitor Redis memory usage

### Debug Commands

```bash
# Check queue status
npm run queue:status

# Restart queue workers
npm run queue:restart

# Clear failed jobs
npm run queue:clean
```

## Examples

Xem các file example trong thư mục `examples/`:

- `content-analytics-integration.example.ts`: Content tracking examples
- `auth-analytics-integration.example.ts`: User activity tracking examples  
- `payment-analytics-integration.example.ts`: Payment tracking examples