import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import {
  RecentlySortBy,
  RecentlySortOrder,
} from '../../../common/common.config';

export class FilterRecentlyDto {
  @ApiProperty({
    description: 'Search by title',
    example: 'title',
    required: false,
  })
  @IsOptional()
  @IsString()
  searchTitle: string;

  @ApiProperty({
    description: 'Sort by',
    example: RecentlySortBy.LATEST,
    required: false,
    enum: Object.values(RecentlySortBy),
  })
  @IsOptional()
  @IsString()
  sortBy: string;

  @ApiProperty({
    description: 'Sort order',
    example: RecentlySortOrder.DESC,
    required: false,
    enum: Object.values(RecentlySortOrder),
  })
  @IsOptional()
  @IsString()
  sortOrder: string;

  @ApiProperty({
    description: 'Limit',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit: number = 10;

  @ApiProperty({
    description: 'Page',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page: number = 1;
}
