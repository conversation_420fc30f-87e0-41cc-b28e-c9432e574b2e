import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { CommonStatus } from '../../common/status.enum';
import { UserEntity } from './user.entity';
import { SubscriptionPlanEntity } from './subscription_plan.entity';
import { PaymentEntity } from './payment.entity';

@Entity('subscriptions')
@Index(['expiresAt', 'status'])
export class SubscriptionEntity extends DefaultEntity {
  @Column({ type: 'varchar', length: 255 })
  currency: string;

  @Column({ type: 'enum', enum: CommonStatus, default: CommonStatus.ACTIVE })
  status: CommonStatus;

  @Column({ type: 'bigint' })
  startedAt: number;

  @Column({ type: 'bigint' })
  expiresAt: number;

  @Column({ type: 'boolean', default: false })
  autoRenew: boolean;

  @Column({ type: 'boolean', default: false })
  isTrial: boolean;

  @Column({ type: 'int' })
  trialDays: number;

  @Index()
  @ManyToOne(() => UserEntity, (user) => user.subscriptions)
  user: UserEntity;

  @ManyToOne(
    () => SubscriptionPlanEntity,
    (subscriptionPlan) => subscriptionPlan.subscriptions,
  )
  subscriptionPlan: SubscriptionPlanEntity;

  @OneToMany(() => PaymentEntity, (payment) => payment.subscription)
  payments: PaymentEntity[];
}
