import { Injectable, BadRequestException } from '@nestjs/common';
import { UploadRepository } from '../../../../database/repositories/upload.repository';
import {
  UploaderType,
  UploadContext,
} from '../../../../database/entities/upload.entity';
import { getUploadConfig, ValidationResult } from '../../config/upload.config';
import { MESSAGE_CONFIG } from '../../../../common/message.config';
import * as path from 'path';

@Injectable()
export class FileValidationService {
  constructor(private uploadRepository: UploadRepository) {}

  async validate(
    file: any,
    uploaderId: number,
    uploaderType: UploaderType,
    context: UploadContext,
  ): Promise<ValidationResult> {
    const config = getUploadConfig(uploaderType, context);
    const warnings: string[] = [];

    // 1. Check if file exists
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    // 2. File size validation
    if (file.size > config.maxFileSize) {
      throw new BadRequestException(
        `File too large. Maximum size is ${Math.round(config.maxFileSize / 1024 / 1024)}MB`,
      );
    }

    // 3. MIME type validation
    if (!config.allowedTypes.includes(file.mimetype)) {
      throw new BadRequestException(
        `File type ${file.mimetype} not allowed. Allowed types: ${config.allowedTypes.join(', ')}`,
      );
    }

    // 4. File extension validation (additional security)
    const fileExtension = path.extname(file.originalname).toLowerCase();
    const allowedExtensions = this.getMimeTypeExtensions(config.allowedTypes);

    if (!allowedExtensions.includes(fileExtension)) {
      throw new BadRequestException(
        `File extension ${fileExtension} not allowed`,
      );
    }

    // 5. File count validation (only for users with limits)
    if (uploaderType === UploaderType.USER) {
      const currentFileCount = await this.uploadRepository.getUserFileCount(
        uploaderId,
        context,
      );

      if (currentFileCount >= config.maxFiles) {
        throw new BadRequestException(
          `Maximum ${config.maxFiles} files allowed for this context`,
        );
      }
    }

    // 6. Magic number validation (basic content validation)
    const isValidContent = this.validateFileContent(file);
    if (!isValidContent) {
      throw new BadRequestException('Invalid file content');
    }

    // 7. Filename validation
    if (file.originalname.length > 255) {
      warnings.push('Filename will be truncated');
    }

    // 8. Special validation for profile images (only one allowed)
    if (
      context === UploadContext.PROFILE &&
      uploaderType === UploaderType.USER
    ) {
      const existingProfile = await this.uploadRepository.findUserFiles(
        uploaderId,
        UploadContext.PROFILE,
        1,
        1,
      );

      if (existingProfile[1] > 0) {
        warnings.push('Existing profile image will be replaced');
      }
    }

    return {
      valid: true,
      warnings,
    };
  }

  private getMimeTypeExtensions(allowedTypes: string[]): string[] {
    const mimeToExt: Record<string, string[]> = {
      'image/png': ['.png'],
      'image/jpeg': ['.jpg', '.jpeg'],
      'image/jpg': ['.jpg', '.jpeg'],
      'image/gif': ['.gif'],
      'image/webp': ['.webp'],
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        ['.docx'],
      'text/plain': ['.txt'],
      'application/zip': ['.zip'],
      'application/x-rar-compressed': ['.rar'],
    };

    const extensions: string[] = [];
    for (const mimeType of allowedTypes) {
      if (mimeToExt[mimeType]) {
        extensions.push(...mimeToExt[mimeType]);
      }
    }

    return extensions;
  }

  private validateFileContent(file: any): boolean {
    // Basic magic number validation
    const buffer = file.buffer;

    if (!buffer || buffer.length < 4) {
      return false;
    }

    // Check common file signatures
    const signature = buffer.subarray(0, 4);

    // PNG: 89 50 4E 47
    if (file.mimetype === 'image/png') {
      return (
        signature[0] === 0x89 &&
        signature[1] === 0x50 &&
        signature[2] === 0x4e &&
        signature === 0x47
      );
    }

    // JPEG: FF D8 FF
    if (file.mimetype === 'image/jpeg' || file.mimetype === 'image/jpg') {
      return (
        signature[0] === 0xff && signature[1] === 0xd8 && signature[2] === 0xff
      );
    }

    // PDF: 25 50 44 46
    if (file.mimetype === 'application/pdf') {
      return (
        signature[0] === 0x25 &&
        signature[1] === 0x50 &&
        signature[2] === 0x44 &&
        signature === 0x46
      );
    }

    // ZIP: 50 4B 03 04 or 50 4B 05 06 or 50 4B 07 08
    if (file.mimetype === 'application/zip') {
      return (
        signature[0] === 0x50 &&
        signature[1] === 0x4b &&
        (signature[2] === 0x03 ||
          signature[2] === 0x05 ||
          signature[2] === 0x07)
      );
    }

    // For other types, just return true for now
    // Can be extended with more specific validations
    return true;
  }
}
