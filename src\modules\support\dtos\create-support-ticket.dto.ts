import { ApiProperty } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsOptional,
  IsNumber,
  IsPositive,
  IsArray,
  IsUrl,
  MaxLength,
  IsEnum,
  IsObject,
} from 'class-validator';
import {
  TicketPriority,
  TicketContextData,
} from '../../../database/entities/support-ticket.entity';

export class CreateSupportTicketDto {
  @ApiProperty({
    example: 1,
    description: 'Category ID (optional)',
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @IsPositive()
  categoryId?: number;

  @ApiProperty({
    example: 'Cannot access premium content',
    description: 'Ticket subject/title',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(200)
  subject: string;

  @ApiProperty({
    example:
      'I have an active subscription but cannot read episode 15 of My Hero Academia',
    description: 'Detailed description of the issue',
  })
  @IsNotEmpty()
  @IsString()
  @MaxLength(2000)
  description: string;

  @ApiProperty({
    example: ['https://example.com/screenshot1.png'],
    description: 'Attachment file URLs (max 5 files)',
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsUrl({}, { each: true })
  attachments?: string[];

  @ApiProperty({
    enum: TicketPriority,
    example: TicketPriority.MEDIUM,
    description: 'Ticket priority',
    required: false,
  })
  @IsOptional()
  @IsEnum(TicketPriority)
  priority?: TicketPriority;

  @ApiProperty({
    example: {
      contentId: 123,
      episodeId: 456,
      subscriptionId: 789,
      paymentId: 101,
      deviceInfo: 'iPhone 14 iOS 16.1',
      errorCode: 'CONTENT_LOCKED_ERROR',
      userAgent: 'Mozilla/5.0...',
    },
    description: 'Context data for webtoon platform',
    required: false,
  })
  @IsOptional()
  @IsObject()
  contextData?: TicketContextData;
}
