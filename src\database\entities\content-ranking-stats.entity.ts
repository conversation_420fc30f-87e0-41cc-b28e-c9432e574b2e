import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { ContentEntity } from './content.entity';

export enum PeriodType {
  DAILY = 'daily',
  ALL_TIME = 'all_time',
}

export enum ContentType {
  ADULT = 'adult',
  GENERAL = 'general',
}

export enum SeriesStatus {
  ONGOING = 'ongoing',
  COMPLETED = 'completed',
}

@Entity('content_ranking_stats')
@Unique(['contentId', 'periodType', 'date'])
@Index(['periodType', 'date', 'totalEpisodeRead'])
@Index(['contentId', 'date'])
export class ContentRankingStatsEntity {
  @PrimaryGeneratedColumn('increment')
  id: number;

  @Column({ type: 'int', nullable: false })
  contentId: number;

  @Column({ type: 'varchar', length: 255, nullable: true })
  title: string;

  @Column({ type: 'varchar', length: 100, nullable: true })
  author: string;

  @Column({ type: 'int', nullable: true })
  authorId: number;

  @Column({ type: 'varchar', length: 100, nullable: true })
  genre: string;

  @Column({ type: 'int', nullable: true })
  genreId: number;

  @Column({
    type: 'enum',
    enum: ContentType,
    default: ContentType.GENERAL,
  })
  type: ContentType;

  @Column({
    type: 'enum',
    enum: SeriesStatus,
    default: SeriesStatus.ONGOING,
  })
  series: SeriesStatus;

  @Column({ type: 'int', default: 0 })
  episodes: number;

  @Column({
    type: 'enum',
    enum: PeriodType,
    nullable: false,
  })
  periodType: PeriodType;

  @Column({ type: 'date', nullable: true })
  date: Date;

  @Column({ type: 'int', default: 0 })
  freeEpisodeRead: number;

  @Column({ type: 'int', default: 0 })
  paidEpisodeRead: number;

  @Column({ type: 'int', default: 0 })
  totalEpisodeRead: number;

  @Column({ type: 'int', default: 0 })
  totalViews: number;

  @Column({ type: 'int', default: 0 })
  uniqueViewers: number;

  @Column({ type: 'bigint', default: 0 })
  totalReadTime: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => ContentEntity, (content) => content.id, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'contentId' })
  content: ContentEntity;
}
