import { Injectable } from '@nestjs/common';
import * as path from 'path';
import * as fs from 'fs/promises';
import { UploadRepository } from '../../../../database/repositories/upload.repository';
import { FileValidationService } from './file-validation.service';
import { FileProcessingService } from './file-processing.service';
import {
  UploaderType,
  UploadContext,
  FileType,
} from '../../../../database/entities/upload.entity';
import {
  getUploadConfig,
  UploadResult,
  UPLOAD_CONFIG,
} from '../../config/upload.config';
import { AnalyticsQueueService } from '../../../analytics/services/analytics-queue.service';
import configurationGlobal from '../../../../config/configuration.global';

@Injectable()
export class UploadService {
  constructor(
    private uploadRepository: UploadRepository,
    private fileValidationService: FileValidationService,
    private fileProcessingService: FileProcessingService,
    private analyticsQueueService: AnalyticsQueueService,
  ) {}

  async uploadFile(
    file: any,
    uploaderId: number,
    uploaderType: UploaderType,
    context: UploadContext,
    description?: string,
  ): Promise<UploadResult> {
    // 1. Validate file
    await this.fileValidationService.validate(
      file,
      uploaderId,
      uploaderType,
      context,
    );

    // 2. Handle profile image replacement
    if (
      context === UploadContext.PROFILE &&
      uploaderType === UploaderType.USER
    ) {
      await this.replaceExistingProfileImage(uploaderId);
    }

    // 3. Generate file path and name
    const config = getUploadConfig(uploaderType, context);
    const folder = config.folder.replace('{userId}', uploaderId.toString());
    const fileName = this.fileProcessingService.generateFileName(
      file.originalname,
    );
    const relativeFilePath = `${folder}/${fileName}`;
    const fullPath = path.join(
      process.cwd(),
      UPLOAD_CONFIG.baseUploadPath,
      relativeFilePath,
    );

    // 4. Ensure directory exists
    await fs.mkdir(path.dirname(fullPath), { recursive: true });

    // 5. Save file to disk
    await fs.writeFile(fullPath, file.buffer);

    // 6. Generate thumbnail if needed
    let thumbnailPath: string | null = null;
    if (config.generateThumbnail && file.mimetype.startsWith('image/')) {
      thumbnailPath = await this.fileProcessingService.generateThumbnail(
        fullPath,
        relativeFilePath,
      );
    }

    // 7. Extract metadata
    const metadata = await this.fileProcessingService.extractMetadata(
      file,
      fullPath,
    );

    // 8. Determine file type
    const fileType = file.mimetype.startsWith('image/')
      ? FileType.IMAGE
      : FileType.DOCUMENT;

    // 9. Save to database
    const uploadEntity = this.uploadRepository.create({
      uploaderId,
      uploaderType,
      originalName: file.originalname,
      fileName,
      filePath: `/${UPLOAD_CONFIG.baseUploadPath}/${relativeFilePath}`,
      fileSize: file.size,
      mimeType: file.mimetype,
      fileType,
      uploadContext: context,
      description: description || null,
      metadata,
      thumbnailPath,
    });

    const upload = await this.uploadRepository.save(uploadEntity);

    // 10. Track analytics
    try {
      if (uploaderType === UploaderType.USER) {
        await this.analyticsQueueService.trackUserActivity({
          userId: uploaderId,
          activityType: 'file_upload',
          platform: 'web',
          timestamp: Date.now(),
          metadata: {
            uploaderType,
            context,
            fileType: file.mimetype,
            fileSize: file.size,
            uploadId: upload.id,
          },
        });
      }
    } catch (error) {
      console.error('Analytics tracking failed:', error);
    }

    // 11. Return result
    const baseUrl = configurationGlobal().url.image || 'http://localhost:3000';
    return {
      success: true,
      uploadId: upload.id,
      filePath: upload.filePath,
      thumbnailPath: upload.thumbnailPath?.toString(),
      publicUrl: `${baseUrl}${upload.filePath}`,
    };
  }

  async getUserFiles(
    userId: number,
    query: {
      context?: UploadContext;
      page?: number;
      limit?: number;
    },
  ) {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 20, 100); // Max 100 per page

    const [files, total] = await this.uploadRepository.findUserFiles(
      userId,
      query.context,
      page,
      limit,
    );

    const baseUrl = process.env.APP_URL || 'http://localhost:3000';

    return {
      data: files.map((file) => ({
        id: file.id,
        originalName: file.originalName,
        fileName: file.fileName,
        fileSize: file.fileSize,
        mimeType: file.mimeType,
        fileType: file.fileType,
        context: file.uploadContext,
        description: file.description,
        publicUrl: `${baseUrl}${file.filePath}`,
        thumbnailUrl: file.thumbnailPath
          ? `${baseUrl}${file.thumbnailPath}`
          : null,
        metadata: file.metadata,
        createdAt: file.createdAt,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getAdminFiles(query: {
    context?: UploadContext;
    page?: number;
    limit?: number;
  }) {
    const page = query.page || 1;
    const limit = Math.min(query.limit || 20, 100);

    const [files, total] = await this.uploadRepository.findAdminFiles(
      query.context,
      page,
      limit,
    );

    const baseUrl = process.env.APP_URL || 'http://localhost:3000';

    return {
      data: files.map((file) => ({
        id: file.id,
        uploaderId: file.uploaderId,
        originalName: file.originalName,
        fileName: file.fileName,
        fileSize: file.fileSize,
        mimeType: file.mimeType,
        fileType: file.fileType,
        context: file.uploadContext,
        description: file.description,
        publicUrl: `${baseUrl}${file.filePath}`,
        thumbnailUrl: file.thumbnailPath
          ? `${baseUrl}${file.thumbnailPath}`
          : null,
        metadata: file.metadata,
        createdAt: file.createdAt,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async deleteUserFile(uploadId: number, userId: number): Promise<boolean> {
    const file = await this.uploadRepository.findUserFileById(uploadId, userId);

    if (!file) {
      return false;
    }

    // Delete physical file
    await this.fileProcessingService.deleteFile(file.filePath);

    // Soft delete from database
    await this.uploadRepository.softDelete(uploadId);

    // Track analytics
    try {
      await this.analyticsQueueService.trackUserActivity({
        userId,
        activityType: 'file_delete',
        platform: 'web',
        timestamp: Date.now(),
        metadata: {
          uploadId,
          fileType: file.mimeType,
          context: file.uploadContext,
        },
      });
    } catch (error) {
      console.error('Analytics tracking failed:', error);
    }

    return true;
  }

  async deleteAdminFile(uploadId: number): Promise<boolean> {
    const file = await this.uploadRepository.findAdminFileById(uploadId);

    if (!file) {
      return false;
    }

    // Delete physical file
    await this.fileProcessingService.deleteFile(file.filePath);

    // Soft delete from database
    await this.uploadRepository.softDelete(uploadId);

    return true;
  }

  private async replaceExistingProfileImage(userId: number): Promise<void> {
    const [existingFiles] = await this.uploadRepository.findUserFiles(
      userId,
      UploadContext.PROFILE,
      1,
      1,
    );

    if (existingFiles.length > 0) {
      const existingFile = existingFiles[0];

      // Delete physical file
      await this.fileProcessingService.deleteFile(existingFile.filePath);

      // Soft delete from database
      await this.uploadRepository.softDelete(existingFile.id);
    }
  }
}
