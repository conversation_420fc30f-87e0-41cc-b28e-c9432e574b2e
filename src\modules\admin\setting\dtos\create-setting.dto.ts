import {
  <PERSON><PERSON>num,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON>O<PERSON>al,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { SettingGroup } from '../../../../common/setting.enum';
export class CreateSettingDto {
  @ApiProperty({ description: 'The key of the setting', required: true })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({ description: 'The value of the setting', required: true })
  @IsString()
  @IsNotEmpty()
  value: string;

  @ApiProperty({ description: 'The group of the setting', required: true })
  @IsNotEmpty()
  @IsEnum(SettingGroup)
  group: SettingGroup;

  @ApiProperty({ description: 'The order of the setting', required: false })
  @IsNumber()
  @IsOptional()
  order: number;
}
