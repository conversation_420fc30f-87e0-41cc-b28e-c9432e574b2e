import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyToOne,
  JoinColumn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { UserNotificationEntity } from './user-notification.entity';
import { UserEntity } from './user.entity';

@Entity('user_notification_recipient')
@Index(['userNotificationId', 'userId'], { unique: true })
export class UserNotificationRecipientEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_notification_id' })
  userNotificationId: number;

  @Column({ name: 'user_id' })
  userId: number;

  @ManyToOne(() => UserNotificationEntity)
  @JoinColumn({ name: 'user_notification_id' })
  userNotification: UserNotificationEntity;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @CreateDateColumn()
  createdAt: Date;
}
