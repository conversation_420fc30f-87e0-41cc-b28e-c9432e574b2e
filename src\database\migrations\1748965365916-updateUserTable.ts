import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateUserTable1748965365916 implements MigrationInterface {
  name = 'UpdateUserTable1748965365916';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`lastSignInDate\` datetime NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`lastSignInDate\``,
    );
  }
}
