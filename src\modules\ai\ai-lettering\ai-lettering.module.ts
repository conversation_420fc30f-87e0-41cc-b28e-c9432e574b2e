import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiLetteringController } from './ai-lettering.controller';
import { AiLetteringService } from './ai-lettering.service';
import { AiLetteringLanguageRepository } from 'src/database/repositories/ai-lettering-language.repository';
import { AiLetteringRepository } from 'src/database/repositories/ai-lettering.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiLetteringRepository,
      AiLetteringLanguageRepository,
    ]),
  ],
  controllers: [AiLetteringController],
  providers: [AiLetteringService],
  exports: [AiLetteringService],
})
export class AiLetteringModule {}
