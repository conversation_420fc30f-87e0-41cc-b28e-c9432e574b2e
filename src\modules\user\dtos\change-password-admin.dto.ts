import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsN<PERSON>ber, IsString, Matches } from 'class-validator';
import configuration from '../../../config/configuration.global';
import { MESSAGE_CONFIG } from '../../../common/message.config';
export class ChangePasswordByAdminDto {
  @ApiProperty({
    description: 'The ID of the user to change the password for',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  userId: number;

  @ApiProperty({
    description:
      'The new password for the user, strong password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character',
    example: '1@Aa123456',
  })
  @IsNotEmpty()
  @IsString()
  @Matches(configuration().auth.regex.password, {
    message: MESSAGE_CONFIG.PASSWORD_REGEX.message,
  })
  password: string;
}
