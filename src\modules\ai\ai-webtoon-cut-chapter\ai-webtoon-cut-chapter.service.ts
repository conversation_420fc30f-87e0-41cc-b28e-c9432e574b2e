import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  EAiBaseModelGenImageType,
  EAiBaseModelInpaintType,
  EAiCutChapterType,
  EAiDirectionApi,
  EAiFrameImageGenerateType,
  EAiFrameImageSizeType,
  EAiGenPromptByDescriptionType,
  EAiTypeActionByNormalCut,
  EAiWebtoonBasicStatus,
  EAiWebtoonChapterStatusGenerateImage,
  EAiWebtoonCharacterType,
  EAiWebtoonCutChapterStatusGenerateImage,
} from 'src/common/ai-webtoon.enum';
import {
  IGenerateImageDataItem,
  IGeneratePromptByDescriptionItem,
  ISendGenerateCutImagesRequest,
  ISendInpaintRequest,
} from 'src/common/interfaces/ai-webtoon/ai-send-request.interface';
import { IAiGeneralCharacter } from 'src/common/interfaces/ai-webtoon/ai-webtoon-character.interface';
import { IItemImageInpaint } from 'src/common/interfaces/ai-webtoon/ai-webtoon-cut-chapter.entity';
import { AI_MESSAGE_CONFIG } from 'src/common/message.config';
import { AiWebtoonChapterEntity } from 'src/database/entities/ai-webtoon-chapter.entity';
import { AiWebtoonCutChapterEntity } from 'src/database/entities/ai-webtoon-cut-chapter.entity';
import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { In } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { AiApiGenStateManagementService } from '../ai-api-gen-state-management/ai-api-gen-state-management.service';
import {
  AiItemDialogueBubblesDto,
  BorderConfigDto,
  EditConfigDto,
  ItemEffectsDto,
  // ItemImageSketchDto,
  ItemTextDto,
} from '../ai-webtoon-chapter/dto/common-scene-frame.dto';
import {
  AddImageCutNormalDto,
  AddSketchImagesDto,
} from './dto/add-image-cut-normal.dto';
import { CombineImagesDto } from './dto/combine-image.dto';
import { CreateCutDto } from './dto/create-cut.dto';
import { GenerateImages3Dto } from './dto/generate-images.dto';
import {
  GeneratePromptDto,
  ReceiveGeneratePromptByDescriptionDto,
} from './dto/generate-prompt-by-description.dto';
import { GenerateSketchImageDto } from './dto/generate-sketch-image.dto';
import { InpaintImageDto } from './dto/inpaint-image.dto';
import { ReceiveGenerateImageDto } from './dto/receive-generate-image.dto';
import { ReceiveInpaintImageDto } from './dto/receive-inpaint-image.dto';
import { SelectImageByIndexDto } from './dto/select-image-by-index.dto';
import { SelectSketchImageByIndexDto } from './dto/select-sketch-image-by-index.dto';
import { StopGenerateImagesDto } from './dto/stop-generate-images.dto';
import { UnselectSketchImagesDto } from './dto/unselect-sketch-image-by-index';
import {
  UpdateNormalCutsDto,
  UpdateSelectBgImageInpaintCutDto,
} from './dto/update-cut.dto';
import { UpdateEditImageDto } from './dto/update-edit-image.dto';
import { UpdateOrderByPreviewCutsDto } from './dto/update-order-by-preview-cuts.dto';
// import { NOTFOUND } from 'dns';
import { AiWebtoonOrderGenerateCutChapterEntity } from 'src/database/entities/ai-webtoon-order-generate-cut-chapter.entity';
import { AiWebtoonOrderGenerateCutChapterRepository } from 'src/database/repositories/ai-webtoon-order-generate-cut-chapter.repository';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { AiService } from '../ai-service/ai-service.service';

@Injectable()
export class AiWebtoonCutChapterService {
  private readonly logger = new Logger(AiWebtoonCutChapterService.name);

  constructor(
    private readonly aiWebtoonCutChapterRepository: AiWebtoonCutChapterRepository,
    private readonly aiWebtoonSceneChapterRepository: AiWebtoonSceneChapterRepository,
    private readonly aiApiGenStateManagementService: AiApiGenStateManagementService,
    private readonly aiWebtoonChapterRepository: AiWebtoonChapterRepository,
    private readonly aiWebtoonOrderGenerateCutChapterRepository: AiWebtoonOrderGenerateCutChapterRepository,
    private readonly notificationGoogleService: NotificationGoogleService,
    private readonly aiService: AiService,
    // private readonly socketService: SocketService,
    // private readonly cacheService: CacheService,
  ) {}

  private filterCharactersByBaseModelGenImage(
    cutCharacters: string[],
    generalCharacters: IAiGeneralCharacter[],
    baseModel: EAiBaseModelGenImageType,
  ): IAiGeneralCharacter[] {
    if (baseModel === EAiBaseModelGenImageType.DREAM_BOOTH) {
      return [];
    }

    return cutCharacters
      .map((uuid) => {
        const character = generalCharacters.find(
          (gChar) => gChar.uuid === uuid,
        );
        if (!character) return null;

        if ([EAiBaseModelGenImageType.SDXL_4_OPT].includes(baseModel)) {
          if (character.type !== EAiWebtoonCharacterType.SDXL) {
            return null;
          }
        } else {
          if (character.type !== EAiWebtoonCharacterType.FLUX) {
            return null;
          }
        }
        return character;
      })
      .filter(Boolean) as IAiGeneralCharacter[];
  }

  private filterCharactersByBaseModelInpaint(
    cutCharacters: string[],
    generalCharacters: IAiGeneralCharacter[],
    baseModel: EAiBaseModelInpaintType,
  ): IAiGeneralCharacter[] {
    return cutCharacters
      .map((uuid) => {
        const character = generalCharacters.find(
          (character) => character.uuid === uuid,
        );
        if (!character) {
          return null;
        }

        if (
          [
            EAiBaseModelInpaintType.SDXL_ADULT,
            EAiBaseModelInpaintType.SDXL_4_OPT,
          ].includes(baseModel)
        ) {
          if (character.type !== EAiWebtoonCharacterType.SDXL) {
            return null;
          }
        } else {
          if (character.type !== EAiWebtoonCharacterType.FLUX) {
            return null;
          }
        }

        return character;
      })
      .filter(Boolean) as IAiGeneralCharacter[];
  }

  async findOneById(id: number): Promise<AiWebtoonCutChapterEntity> {
    const result = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id },
    });

    if (!result) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    return result;
  }

  async getSceneById(sceneId: number) {
    const scene = await this.aiWebtoonSceneChapterRepository.findOne({
      where: { id: sceneId },
    });
    if (!scene) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_SCENE_NOT_FOUND);
    }
    return scene;
  }

  async getChapterById(chapterId: number) {
    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id: chapterId },
      relations: ['aiWebtoonStory'],
    });
    if (!chapter) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHAPTER_NOT_FOUND);
    }
    return chapter;
  }

  async updateStatusGenerateImageChapter(chapterId: number) {
    let status: EAiWebtoonChapterStatusGenerateImage =
      EAiWebtoonChapterStatusGenerateImage.NOT_GENERATED;

    const cuts = await this.aiWebtoonCutChapterRepository
      .createQueryBuilder('cut')
      .where('cut.aiWebtoonChapterId = :chapterId', { chapterId })
      .getMany();

    if (cuts.length === 0) {
      return status;
    }

    const allGenerated = cuts.every(
      (cut) =>
        cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.GENERATED,
    );
    const hasGenerating = cuts.some(
      (cut) =>
        cut.statusGenerateImage ===
          EAiWebtoonCutChapterStatusGenerateImage.GENERATING ||
        cut.statusGenerateImage ===
          EAiWebtoonCutChapterStatusGenerateImage.WAITING,
    );
    const hasGenerated = cuts.some(
      (cut) =>
        cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.GENERATED,
    );

    if (allGenerated) {
      status = EAiWebtoonChapterStatusGenerateImage.DONE;
    } else if (hasGenerating) {
      status = EAiWebtoonChapterStatusGenerateImage.GENERATING;
    } else if (hasGenerated) {
      status = EAiWebtoonChapterStatusGenerateImage.GENERATED;
    }

    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id: chapterId },
    });
    if (chapter) {
      chapter.statusGenerateImage = status;
      await this.aiWebtoonChapterRepository.save(chapter);
    }
  }

  async createCutByScene(sceneId: number, body: CreateCutDto) {
    const scene = await this.getSceneById(sceneId);

    await this.aiWebtoonCutChapterRepository
      .createQueryBuilder()
      .update()
      .set({
        order: () => '`order` + 1',
      })
      .where('ai_webtoon_scene_chapter_id = :sceneId', { sceneId })
      .andWhere('order >= :order', { order: body.order })
      .execute();

    const cut = await this.aiWebtoonCutChapterRepository.save({
      aiWebtoonSceneChapterId: sceneId,
      aiWebtoonChapterId: scene.aiWebtoonChapterId,
      type: body.type,
      order: body.order,
      characters: [],
      images: [],
      controlNetCannyImages: [],
      controlNetDepthImages: [],
    } as AiWebtoonCutChapterEntity);
    return {
      cutId: cut.id,
    };
  }

  async deleteCut(id: number) {
    const cut = await this.findOneById(id);

    if (
      cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.GENERATING ||
      cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.WAITING
    ) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CUT_CANNOT_DELETE_WHILE_GENERATING,
      );
    }

    await this.aiWebtoonCutChapterRepository.softDelete(id);
    await this.updateStatusGenerateImageChapter(cut.aiWebtoonChapterId);
    return true;
  }

  async updateNormalCuts(body: UpdateNormalCutsDto) {
    const cuts: AiWebtoonCutChapterEntity[] = [];

    const updatePromises = body.cuts.map(async (cutUpdate) => {
      const cut = await this.aiWebtoonCutChapterRepository.findOne({
        where: {
          id: cutUpdate.id,
        },
        relations: ['chapter', 'chapter.aiWebtoonStory'],
      });

      if (!cut) {
        throw new NotFoundException({
          key: AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND.key,
          message: `Cut with ID ${cutUpdate.id} not found`,
        });
      }

      if (cut.type !== EAiCutChapterType.NORMAL) {
        throw new BadRequestException({
          key: AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL.key,
          message: `Cut with ID ${cutUpdate.id} is not of type NORMAL`,
        });
      }
      const imageSizeType = cutUpdate.imageSizeType ?? cut.imageSizeType;
      let baseModel = cutUpdate.baseModel ?? cut.baseModel;

      if (imageSizeType === EAiFrameImageSizeType.CONTROLNET_DEPTH) {
        baseModel = EAiBaseModelGenImageType.SDXL_4_OPT;
      }

      if (
        cutUpdate.characterUuids &&
        baseModel !== EAiBaseModelGenImageType.DREAM_BOOTH
      ) {
        const generalCharacters = cut.chapter.aiWebtoonStory.characters;

        cutUpdate.characterUuids.forEach((uuid) => {
          const found = generalCharacters.find(
            (character) => character.uuid === uuid,
          );
          if (!found) {
            throw new BadRequestException({
              key: AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND.key,
              message: `Character with UUID ${uuid} not found`,
            });
          }
        });
      }

      let charactersToUpdate: string[];
      if (cutUpdate.characterUuids) {
        charactersToUpdate =
          baseModel === EAiBaseModelGenImageType.DREAM_BOOTH
            ? []
            : cutUpdate.characterUuids;
      } else {
        charactersToUpdate =
          baseModel === EAiBaseModelGenImageType.DREAM_BOOTH
            ? []
            : cut.characters;
      }

      cuts.push(cut);

      return this.aiWebtoonCutChapterRepository.update(cutUpdate.id, {
        info: cutUpdate.info ?? cut.info,
        description: cutUpdate.description ?? cut.description,
        negativeDescription:
          cutUpdate.negativeDescription ?? cut.negativeDescription,
        negativePrompt: cutUpdate.negativePrompt ?? cut.negativePrompt,
        generalPrompt: cutUpdate.generalPrompt ?? cut.generalPrompt,
        danbooruPrompt: cutUpdate.danbooruPrompt ?? cut.danbooruPrompt,
        numberOfImages: cutUpdate.numberOfImages ?? cut.numberOfImages,
        imageSizeType: imageSizeType,
        imageWidth: cutUpdate.imageWidth ?? cut.imageWidth,
        imageHeight: cutUpdate.imageHeight ?? cut.imageHeight,
        characters: charactersToUpdate,
        baseModel: baseModel,
        controlNetStrengthCanny:
          cutUpdate.controlNetStrengthCanny ?? cut.controlNetStrengthCanny,
        controlNetStrengthDepth:
          cutUpdate.controlNetStrengthDepth ?? cut.controlNetStrengthDepth,
        seed: cutUpdate.seed ?? cut.seed,
        cfgScale: cutUpdate.cfgScale ?? cut.cfgScale,
        steps: cutUpdate.steps ?? cut.steps,
      });
    });

    await Promise.all(updatePromises);

    const chapterIds = [...new Set(cuts.map((cut) => cut.aiWebtoonChapterId))];
    // for (const chapterId of chapterIds) {
    // this.socketService.emitUpdateChapter(chapterId);
    // }

    return true;
  }

  async updateSelectBgImageInpaintCut(
    id: number,
    body: UpdateSelectBgImageInpaintCutDto,
  ) {
    const cut = await this.findOneById(id);
    if (cut.type !== EAiCutChapterType.INPAINT) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_INPAINT);
    }

    await this.aiWebtoonCutChapterRepository.update(id, {
      bgImage: body.bgImage,
    });

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async selectImageByIndex(id: number, body: SelectImageByIndexDto) {
    const cut = await this.findOneById(id);

    const check = cut.images[body.imageIndex];
    if (!check) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_IMAGE_INDEX_NOT_FOUND);
    }

    await this.aiWebtoonCutChapterRepository.update(id, {
      imageIndex: body.imageIndex,
    });

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async deleteImageByIndex(id: number, body: SelectImageByIndexDto) {
    const cut = await this.findOneById(id);

    const images = cut.images;
    let characters = cut.characters;

    const check = images[body.imageIndex];
    if (!check) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_IMAGE_INDEX_NOT_FOUND);
    }

    images.splice(body.imageIndex, 1);

    if (images.length === 0) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_NO_IMAGE_LEFT);
    }

    if (cut.type === EAiCutChapterType.INPAINT) {
      characters = Array.from(
        new Set(
          (images as IItemImageInpaint[]).flatMap((image) =>
            image.characters ? image.characters : [],
          ),
        ),
      );
    }

    await this.aiWebtoonCutChapterRepository.update(id, {
      images,
      imageIndex: images[cut.imageIndex] ? cut.imageIndex : 0,
      characters,
    });

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async deleteBgImageInpaintCut(id: number) {
    const cut = await this.findOneById(id);

    if (cut.type !== EAiCutChapterType.INPAINT) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_INPAINT);
    }
    await this.aiWebtoonCutChapterRepository.update(id, {
      bgImage: null,
    });

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  prepareDataToSendGenerateImagesRequest(
    chapter: AiWebtoonChapterEntity,
    cuts: AiWebtoonCutChapterEntity[],
  ): ISendGenerateCutImagesRequest {
    const generalCharacters = chapter.aiWebtoonStory?.characters || [];
    const data = {
      chapterId: chapter.id,
      pathReceived: '/ai-webtoon-cut-chapter/receive-generate-image',
      data: cuts.map((cut) => {
        const cutCharacters = cut.characters;
        const cannyImage =
          cut.controlNetCannyIndex != null
            ? cut.controlNetCannyImages[cut.controlNetCannyIndex]
            : null;
        const depthImage =
          cut.controlNetDepthIndex != null
            ? cut.controlNetDepthImages[cut.controlNetDepthIndex]
            : null;

        if (
          [
            EAiBaseModelGenImageType.FLUX_NORMAL,
            EAiBaseModelGenImageType.FLUX_ADULT,
          ].includes(cut.baseModel) &&
          !cut.generalPrompt
        ) {
          throw new BadRequestException(
            AI_MESSAGE_CONFIG.AI_GENERAL_PROMPT_NOT_FOUND,
          );
        }

        if (
          [
            EAiBaseModelGenImageType.SDXL_4_OPT,
            EAiBaseModelGenImageType.DREAM_BOOTH,
          ].includes(cut.baseModel) &&
          !cut.danbooruPrompt
        ) {
          throw new BadRequestException(
            AI_MESSAGE_CONFIG.AI_DANBOORU_PROMPT_NOT_FOUND,
          );
        }

        return {
          id: cut.id,
          description: cut.description,
          negativePrompt: cut.negativePrompt,
          generalPrompt: [
            EAiBaseModelGenImageType.SDXL_4_OPT,
            EAiBaseModelGenImageType.DREAM_BOOTH,
          ].includes(cut.baseModel)
            ? cut.danbooruPrompt
            : cut.generalPrompt,
          characters: this.filterCharactersByBaseModelGenImage(
            cutCharacters,
            generalCharacters,
            cut.baseModel,
          ),
          dialogues: [],
          numberOfImages: cut.numberOfImages,
          width: cut.imageWidth,
          height: cut.imageHeight,
          rotate: cut.imageSizeType,
          baseModel: cut.baseModel,
          sketchImage:
            cut.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY
              ? cannyImage?.image || null
              : null,
          depthImage:
            cut.imageSizeType === EAiFrameImageSizeType.CONTROLNET_DEPTH
              ? depthImage?.image || null
              : null,
          controlNetStrength:
            cut.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY
              ? cut.controlNetStrengthCanny
              : cut.controlNetStrengthDepth,
          seed: cut.seed ? cut.seed : null,
          cfgScale: Number(cut.cfgScale),
          steps: cut.steps,
          borderType: chapter.aiWebtoonStory.borderType,
          borderColor: chapter.aiWebtoonStory.borderColor,
          borderWeight: chapter.aiWebtoonStory.borderWeight,
        } as IGenerateImageDataItem;
      }),
    } as ISendGenerateCutImagesRequest;

    if (data.data.length === 0)
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_NO_CUT_TO_GENERATE_IMAGE,
      );

    return data;
  }

  async generateImages(chapterId: number, body: GenerateImages3Dto) {
    const chapter = await this.getChapterById(chapterId);

    const cutsValid: AiWebtoonCutChapterEntity[] = [];
    if (body.cutId) {
      const cut = await this.aiWebtoonCutChapterRepository
        .createQueryBuilder('cut')
        .where('cut.type = :type', { type: EAiCutChapterType.NORMAL })
        .andWhere('cut.statusGenerateImage != :status', {
          status: EAiWebtoonBasicStatus.GENERATING,
        })
        .andWhere('cut.aiWebtoonChapterId = :chapterId', { chapterId })
        .andWhere('cut.id = :cutId', { cutId: body.cutId })
        .getOne();

      if (!cut) {
        throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
      }
      cutsValid.push(cut);
    } else {
      const cuts = await this.aiWebtoonCutChapterRepository
        .createQueryBuilder('cut')
        .where('cut.type = :type', { type: EAiCutChapterType.NORMAL })
        .andWhere('cut.statusGenerateImage != :status', {
          status: EAiWebtoonBasicStatus.GENERATING,
        })
        .andWhere('cut.aiWebtoonChapterId = :chapterId', { chapterId })
        .getMany();

      if (cuts.length === 0) {
        throw new BadRequestException(
          AI_MESSAGE_CONFIG.AI_NO_CUT_VALID_TO_GENERATE_IMAGE,
        );
      }
      cutsValid.push(...cuts);
    }

    const data = this.prepareDataToSendGenerateImagesRequest(
      chapter,
      cutsValid,
    );

    const imageGenerateType = body.cutId
      ? EAiFrameImageGenerateType.ONE
      : EAiFrameImageGenerateType.MULTIPLE;

    await this.aiWebtoonOrderGenerateCutChapterRepository.save({
      type: EAiCutChapterType.NORMAL,
      cutType: EAiCutChapterType.NORMAL,
      imageGenerateType,
      data: data || [],
      chapterId: chapter.id,
      cutsId: cutsValid.map((cut) => cut.id) || [],
    } as AiWebtoonOrderGenerateCutChapterEntity);

    // delete chapter.aiWebtoonStory;
    chapter.statusGenerateImage =
      EAiWebtoonChapterStatusGenerateImage.GENERATING;

    await Promise.all([
      this.aiWebtoonChapterRepository.save(chapter),
      ...cutsValid.map((cut) => {
        cut.statusGenerateImage =
          EAiWebtoonCutChapterStatusGenerateImage.WAITING;
        return this.aiWebtoonCutChapterRepository.save(cut);
      }),
    ]);

    // this.socketService.emitUpdateChapter(chapter.id);
    return true;
  }

  async generateImagesByImportFile(
    chapterId: number,
    cuts: AiWebtoonCutChapterEntity[],
  ) {
    const chapter = await this.getChapterById(chapterId);
    const data = this.prepareDataToSendGenerateImagesRequest(chapter, cuts);

    await this.aiWebtoonOrderGenerateCutChapterRepository.save({
      type: EAiCutChapterType.NORMAL,
      cutType: EAiCutChapterType.NORMAL,
      imageGenerateType: EAiFrameImageGenerateType.MULTIPLE,
      data: data || [],
      chapterId: chapter.id,
      cutsId: cuts.map((cut) => cut.id) || [],
    } as AiWebtoonOrderGenerateCutChapterEntity);

    // delete chapter.aiWebtoonStory;
    chapter.statusGenerateImage =
      EAiWebtoonChapterStatusGenerateImage.GENERATING;

    await Promise.all([
      this.aiWebtoonChapterRepository.save(chapter),
      ...cuts.map((cut) => {
        cut.statusGenerateImage =
          EAiWebtoonCutChapterStatusGenerateImage.WAITING;
        return this.aiWebtoonCutChapterRepository.save(cut);
      }),
    ]);

    // this.socketService.emitUpdateChapter(chapter.id);

    return true;
  }

  async receiveGenerateImage(body: ReceiveGenerateImageDto) {
    this.logger.log(
      `🚀 receiveGenerateImage ${body.apiName} ~ body >> `,
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        apiName: body.apiName,
        event: `Receive Generate Image - Chapter ${body.chapterId} - Cut ${body.id}`,
        data: body,
        isSuccess: true,
      },
    );

    const query = this.aiWebtoonCutChapterRepository
      .createQueryBuilder('cut')
      .leftJoinAndSelect('cut.chapter', 'chapter')
      .leftJoinAndSelect('chapter.aiWebtoonStory', 'aiWebtoonStory')
      .withDeleted()
      .where('cut.type = :type', { type: EAiCutChapterType.NORMAL })
      .andWhere('cut.statusGenerateImage = :statusGenerateImage', {
        statusGenerateImage: EAiWebtoonCutChapterStatusGenerateImage.GENERATING,
      })
      .andWhere('cut.id = :cutId', { cutId: body.id });

    const [cut] = await Promise.all([
      query.getOne(),
      this.aiApiGenStateManagementService.checkIsWorkingByName(body.apiName),
    ]);

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;
    const images = cut.images;

    const cannyImage =
      cut.controlNetCannyIndex != null
        ? cut.controlNetCannyImages[cut.controlNetCannyIndex]
        : null;
    const depthImage =
      cut.controlNetDepthIndex != null
        ? cut.controlNetDepthImages[cut.controlNetDepthIndex]
        : null;

    images.unshift({
      type: EAiTypeActionByNormalCut.GENERATE_IMAGE,
      image: body.image,
      rootImage: body.rootImage,
      finishedImage: body.imageWithBorder,
      editImage: body.image,
      seed: body.seed,
      sampler: body.sampler,
      steps: body.steps,
      cfgScale: body.cfgScale,
      imageWidth: body.imageWidth || cut.imageWidth,
      imageHeight: body.imageHeight || cut.imageHeight,
      frameWidthImageContainer: body.imageWidth || cut.imageWidth,
      frameHeightImageContainer: body.imageHeight || cut.imageHeight,
      bubbles: [],
      effects: [],
      texts: [],
      editConfig: {},
      borderConfig: {
        borderType: body.borderType,
        borderColor: body.borderColor,
        borderWeight: body.borderWeight,
      },
      baseModel: body.baseModel,
      originalConfig: {
        info: cut.info,
        description: cut.description,
        negativeDescription: cut.negativeDescription,
        generalPrompt: cut.generalPrompt,
        negativePrompt: cut.negativePrompt,
        danbooruPrompt: cut.danbooruPrompt,
        imageSizeType: cut.imageSizeType,
        imageWidth: body.imageWidth,
        imageHeight: body.imageHeight,
        numberOfImages: cut.numberOfImages,
        characters: this.filterCharactersByBaseModelGenImage(
          cut.characters,
          generalCharacters,
          body.baseModel,
        ),
        baseModel: body.baseModel,
        controlNetStrengthCanny: cut.controlNetStrengthCanny,
        controlNetStrengthDepth: cut.controlNetStrengthDepth,
        sketchImage:
          cut.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY
            ? cannyImage?.image || null
            : null,
        depthImage:
          cut.imageSizeType === EAiFrameImageSizeType.CONTROLNET_DEPTH
            ? depthImage?.image || null
            : null,
        seed: body.seed,
        cfgScale: body.cfgScale,
        steps: body.steps,
      },
      createdAt: new Date(),
    });

    const isGenerated = cut.totalNumberOfImages === images.length;

    await this.aiWebtoonCutChapterRepository.update(cut.id, {
      images,
      imageIndex: 0,
      statusGenerateImage: isGenerated
        ? EAiWebtoonCutChapterStatusGenerateImage.GENERATED
        : cut.statusGenerateImage,
      imageGenerateType: isGenerated ? null : cut.imageGenerateType,
      apiGenName: isGenerated ? null : cut.apiGenName,
      isPreview: isGenerated ? true : cut.isPreview,
    });

    if (isGenerated) {
      await this.updateStatusGenerateImageChapter(body.chapterId);
    }

    if (body.isDone) {
      await this.aiApiGenStateManagementService.updateItemsByNames(
        [body.apiName],
        false,
        null,
      );
    }

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async stopGenerateImages(body: StopGenerateImagesDto) {
    if (!body.cutId && !body.chapterId) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_NO_CUT_OR_CHAPTER);
    }

    const stopCuts: AiWebtoonCutChapterEntity[] = [];

    if (body.cutId) {
      const cut = await this.findOneById(body.cutId);
      if (cut.type !== EAiCutChapterType.NORMAL) {
        throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
      }
      if (
        cut.statusGenerateImage !==
        EAiWebtoonCutChapterStatusGenerateImage.GENERATING
      ) {
        throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_GENERATING);
      }

      if (cut.imageGenerateType === EAiFrameImageGenerateType.MULTIPLE) {
        const otherCutWithSameApiGenName =
          await this.aiWebtoonCutChapterRepository
            .createQueryBuilder('cut')
            .where('cut.id != :id', { id: cut.id })
            .andWhere('cut.apiGenName = :apiGenName', {
              apiGenName: cut.apiGenName,
            })
            .andWhere('cut.statusGenerateImage = :status', {
              status: EAiWebtoonBasicStatus.GENERATING,
            })
            .andWhere('cut.aiWebtoonChapterId = :chapterId', {
              chapterId: cut.aiWebtoonChapterId,
            })
            .getOne();

        if (otherCutWithSameApiGenName) {
          throw new BadRequestException(
            AI_MESSAGE_CONFIG.AI_ANOTHER_CUT_WITH_SAME_API_IS_CURRENTLY_GENERATING_IMAGES,
          );
        }
      }

      cut.apiGenName &&
        (await this.aiService.sendStopServiceRequest(cut.apiGenName));
      stopCuts.push(cut);
    } else {
      const cuts = await this.aiWebtoonCutChapterRepository
        .createQueryBuilder('cut')
        .where('cut.type = :type', { type: EAiCutChapterType.NORMAL })
        .andWhere('cut.statusGenerateImage = :status', {
          status: EAiWebtoonBasicStatus.GENERATING,
        })
        .andWhere('cut.apiGenName IS NOT NULL')
        .andWhere('cut.aiWebtoonChapterId = :chapterId', {
          chapterId: body.chapterId,
        })
        .getMany();

      if (cuts.length > 0) {
        await Promise.all(
          cuts.map(
            (cut) =>
              cut.apiGenName &&
              this.aiService.sendStopServiceRequest(cut.apiGenName),
          ),
        );
        stopCuts.push(...cuts);
      } else {
        await Promise.all([
          this.aiWebtoonOrderGenerateCutChapterRepository.softDelete({
            chapterId: body.chapterId,
          }),
          this.aiWebtoonCutChapterRepository
            .createQueryBuilder()
            .update()
            .set({
              statusGenerateImage: () => `CASE
            WHEN images IS NOT NULL AND images != '[]'
            THEN '${EAiWebtoonCutChapterStatusGenerateImage.GENERATED}'
            ELSE '${EAiWebtoonCutChapterStatusGenerateImage.NOT_GENERATED}'
          END`,
            })
            .where('aiWebtoonChapterId = :chapterId', {
              chapterId: body.chapterId,
            })
            .execute(),
        ]);
      }
    }

    if (stopCuts.length > 0) {
      await Promise.all(
        stopCuts.map((cut) => {
          return this.aiWebtoonCutChapterRepository.update(cut.id, {
            statusGenerateImage:
              cut.images.length > 0
                ? EAiWebtoonCutChapterStatusGenerateImage.GENERATED
                : EAiWebtoonCutChapterStatusGenerateImage.NOT_GENERATED,
            totalNumberOfImages: cut.images.length,
            imageIndex: 0,
            imageGenerateType: null,
            apiGenName: null,
          });
        }),
      );
    }

    await this.updateStatusGenerateImageChapter(
      stopCuts[0]?.aiWebtoonChapterId || body.chapterId,
    );

    // this.socketService.emitUpdateChapter(
    //   stopCuts[0]?.aiWebtoonChapterId || body.chapterId,
    // );
    return true;
  }

  async inpaintImageByCutNormal(id: number, body: InpaintImageDto) {
    const cut = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id },
      relations: ['chapter', 'chapter.aiWebtoonStory'],
    });

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    if (cut.type !== EAiCutChapterType.NORMAL) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
    }

    if (
      cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.GENERATING ||
      cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.WAITING
    ) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CUT_GENERATING_OR_WAITING,
      );
    }

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;

    const data = {
      cutId: cut.id,
      numberOfImages: body.numberOfImages,
      originImage: body.originImage,
      maskImage: body.maskImage,
      prompt: body.prompt,
      negativePrompt: body.negativePrompt,
      characterIds: body.characterUuids
        .map((uuid) => {
          const character = generalCharacters.find(
            (character) => character.uuid === uuid,
          );
          if (!character) {
            return null;
          }

          if (
            [
              EAiBaseModelInpaintType.SDXL_ADULT,
              EAiBaseModelInpaintType.SDXL_4_OPT,
            ].includes(body.baseModel)
          ) {
            if (character.type !== EAiWebtoonCharacterType.SDXL) {
              return null;
            }
          } else {
            if (character.type !== EAiWebtoonCharacterType.FLUX) {
              return null;
            }
          }

          return Number(character.characterId);
        })
        .filter(Boolean),
      baseModel: body.baseModel,
      pathReceived:
        '/ai-webtoon-cut-chapter/receive-inpaint-image-by-normal-cut',
    } as ISendInpaintRequest;

    // await this.aiWebtoonOrderGenerateCutChapterRepository.save({
    //   type: EAiCutChapterType.INPAINT,
    //   cutType: EAiCutChapterType.NORMAL,
    //   imageGenerateType: EAiFrameImageGenerateType.ONE,
    //   data: JSON.stringify(data),
    //   chapterId: cut.aiWebtoonChapterId,
    //   cutsId: JSON.stringify([cut.id]),
    // });

    // delete cut.chapter.aiWebtoonStory;
    // cut.chapter.statusGenerateImage =
    //   EAiWebtoonChapterStatusGenerateImage.GENERATING;

    // await Promise.all([
    //   this.aiWebtoonChapterRepository.save(cut.chapter),
    //   this.aiWebtoonCutChapterRepository.update(cut.id, {
    //     statusGenerateImage: EAiWebtoonCutChapterStatusGenerateImage.WAITING,
    //   }),
    // ]);

    const { apiName } = await this.aiService.sendInpaintRequest(data);

    // delete cut.chapter.aiWebtoonStory;
    cut.chapter.statusGenerateImage =
      EAiWebtoonChapterStatusGenerateImage.GENERATING;

    await Promise.all([
      this.aiWebtoonChapterRepository.save(cut.chapter),
      this.aiWebtoonCutChapterRepository
        .createQueryBuilder()
        .update()
        .set({
          totalNumberOfImages: () =>
            `total_number_of_images + ${body.numberOfImages}`,
          statusGenerateImage:
            EAiWebtoonCutChapterStatusGenerateImage.GENERATING,
          imageGenerateType: EAiFrameImageGenerateType.ONE,
          apiGenName: apiName,
        })
        .where('id = :id', { id: cut.id })
        .execute(),
    ]);

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async receiveInpaintImageByNormalCut(body: ReceiveInpaintImageDto) {
    this.logger.log(
      `🚀 receiveInpaintImageByNormalCut ~ body >> `,
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Inpaint Image By Normal Cut - Cut ${body.cutId}`,
        data: body,
        isSuccess: true,
      },
    );

    const [cut] = await Promise.all([
      this.aiWebtoonCutChapterRepository.findOne({
        where: { id: body.cutId },
        withDeleted: true,
        relations: ['chapter', 'chapter.aiWebtoonStory'],
      }),
      this.aiApiGenStateManagementService.checkIsWorkingByName(body.apiName),
    ]);

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    if (cut.type !== EAiCutChapterType.NORMAL) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
    }

    if (
      cut.statusGenerateImage !==
      EAiWebtoonCutChapterStatusGenerateImage.GENERATING
    ) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_GENERATING);
    }

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;

    const cutCharacters = cut.characters;
    const images = cut.images;
    const cannyImage =
      cut.controlNetCannyIndex != null
        ? cut.controlNetCannyImages[cut.controlNetCannyIndex] || null
        : null;
    const depthImage =
      cut.controlNetDepthIndex != null
        ? cut.controlNetDepthImages[cut.controlNetDepthIndex] || null
        : null;

    images.unshift({
      type: EAiTypeActionByNormalCut.INPAINT_IMAGE,
      prompt: body.prompt,
      image: body.image,
      rootImage: body.image,
      finishedImage: body.image,
      editImage: body.image,
      imageWidth: body.imageWidth,
      imageHeight: body.imageHeight,
      frameWidthImageContainer: body.imageWidth,
      frameHeightImageContainer: body.imageHeight,
      bubbles: [],
      effects: [],
      texts: [],
      editConfig: {},
      borderConfig: {
        borderType: cut.chapter.aiWebtoonStory.borderType,
        borderColor: cut.chapter.aiWebtoonStory.borderColor,
        borderWeight: cut.chapter.aiWebtoonStory.borderWeight,
      },
      baseModel: body.baseModel,
      originalConfig: {
        info: cut.info,
        description: cut.description,
        negativeDescription: cut.negativeDescription,
        generalPrompt: cut.generalPrompt,
        negativePrompt: cut.negativePrompt,
        danbooruPrompt: cut.danbooruPrompt,
        imageSizeType: cut.imageSizeType,
        imageWidth: cut.imageWidth,
        imageHeight: cut.imageHeight,
        numberOfImages: cut.numberOfImages,
        characters: this.filterCharactersByBaseModelGenImage(
          cutCharacters,
          generalCharacters,
          cut.baseModel,
        ),
        baseModel: cut.baseModel,
        controlNetStrengthCanny: cut.controlNetStrengthCanny,
        controlNetStrengthDepth: cut.controlNetStrengthDepth,
        sketchImage:
          cut.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY
            ? cannyImage?.image || null
            : null,
        depthImage:
          cut.imageSizeType === EAiFrameImageSizeType.CONTROLNET_DEPTH
            ? depthImage?.image || null
            : null,
        seed: cut.seed,
        cfgScale: cut.cfgScale,
        steps: cut.steps,
      },
      createdAt: new Date(),
    });

    const isGenerated = cut.totalNumberOfImages === images.length;

    await this.aiWebtoonCutChapterRepository.update(cut.id, {
      images,
      imageIndex: 0,
      statusGenerateImage: isGenerated
        ? EAiWebtoonCutChapterStatusGenerateImage.GENERATED
        : cut.statusGenerateImage,
      imageGenerateType: isGenerated ? null : cut.imageGenerateType,
      apiGenName: isGenerated ? null : cut.apiGenName,
      isPreview: isGenerated ? true : cut.isPreview,
    });

    if (isGenerated) {
      await this.updateStatusGenerateImageChapter(cut.aiWebtoonChapterId);
    }

    if (body.isDone) {
      await this.aiApiGenStateManagementService.updateItemsByNames(
        [body.apiName],
        false,
        null,
      );
    }

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async inpaintImage(id: number, body: InpaintImageDto) {
    let cut = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id },
      relations: ['chapter', 'chapter.aiWebtoonStory'],
    });

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    if (cut.type !== EAiCutChapterType.INPAINT) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_INPAINT);
    }

    if (
      cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.GENERATING ||
      cut.statusGenerateImage ===
        EAiWebtoonCutChapterStatusGenerateImage.WAITING
    ) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_CUT_GENERATING_OR_WAITING,
      );
    }

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;

    const data: ISendInpaintRequest = {
      cutId: cut.id,
      numberOfImages: body.numberOfImages,
      originImage: body.originImage,
      maskImage: body.maskImage,
      prompt: body.prompt,
      negativePrompt: body.negativePrompt,
      characterIds: this.filterCharactersByBaseModelInpaint(
        body.characterUuids,
        generalCharacters,
        body.baseModel,
      ).map((character) => character.characterId),
      baseModel: body.baseModel,
      pathReceived: '/ai-webtoon-cut-chapter/receive-inpaint-image',
    };

    // await this.aiWebtoonOrderGenerateCutChapterRepository.save({
    //   type: EAiCutChapterType.INPAINT,
    //   cutType: EAiCutChapterType.INPAINT,
    //   imageGenerateType: EAiFrameImageGenerateType.ONE,
    //   data: JSON.stringify(data),
    //   chapterId: cut.aiWebtoonChapterId,
    //   cutsId: JSON.stringify([cut.id]),
    // });

    // delete cut.chapter.aiWebtoonStory;
    // cut.chapter.statusGenerateImage =
    //   EAiWebtoonChapterStatusGenerateImage.GENERATING;
    // await Promise.all([
    //   this.aiWebtoonChapterRepository.save(cut.chapter),
    //   this.aiWebtoonCutChapterRepository.update(cut.id, {
    //     statusGenerateImage: EAiWebtoonCutChapterStatusGenerateImage.WAITING,
    //   }),
    // ]);

    const { apiName } = await this.aiService.sendInpaintRequest(data);

    // delete cut.chapter.aiWebtoonStory;
    cut.chapter.statusGenerateImage =
      EAiWebtoonChapterStatusGenerateImage.GENERATING;
    await Promise.all([
      this.aiWebtoonChapterRepository.save(cut.chapter),
      this.aiWebtoonCutChapterRepository
        .createQueryBuilder()
        .update()
        .set({
          totalNumberOfImages: () =>
            `total_number_of_images + ${body.numberOfImages}`,
          statusGenerateImage:
            EAiWebtoonCutChapterStatusGenerateImage.GENERATING,
          imageGenerateType: EAiFrameImageGenerateType.ONE,
          apiGenName: apiName,
        })
        .where('id = :id', { id: cut.id })
        .execute(),
    ]);

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async stopInpaintImage(id: number) {
    const cut = await this.findOneById(id);

    if (cut.type !== EAiCutChapterType.INPAINT) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_INPAINT);
    }
    if (
      cut.statusGenerateImage !==
      EAiWebtoonCutChapterStatusGenerateImage.GENERATING
    ) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_GENERATING);
    }

    if (!cut.apiGenName) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_API_GEN_NAME);
    }

    await this.aiService.sendStopServiceRequest(cut.apiGenName);

    await Promise.all([
      this.aiWebtoonCutChapterRepository.update(cut.id, {
        statusGenerateImage:
          cut.images.length > 0
            ? EAiWebtoonCutChapterStatusGenerateImage.GENERATED
            : EAiWebtoonCutChapterStatusGenerateImage.NOT_GENERATED,
        imageGenerateType: null,
        totalNumberOfImages: cut.images.length,
        apiGenName: null,
        imageIndex: 0,
      }),
      this.updateStatusGenerateImageChapter(cut.aiWebtoonChapterId),
    ]);

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async receiveInpaintImage(body: ReceiveInpaintImageDto) {
    this.logger.log(
      `🚀 receiveInpaintImage ${body.apiName} ~ body >> `,
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        apiName: body.apiName,
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Inpaint Image - Cut ${body.cutId}`,
        data: body,
        isSuccess: true,
      },
    );

    const [cut] = await Promise.all([
      this.aiWebtoonCutChapterRepository.findOne({
        where: { id: body.cutId },
        relations: ['chapter', 'chapter.aiWebtoonStory'],
        withDeleted: true,
      }),
      this.aiApiGenStateManagementService.checkIsWorkingByName(body.apiName),
    ]);

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    if (cut.type !== EAiCutChapterType.INPAINT) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_INPAINT);
    }

    if (
      cut.statusGenerateImage !==
      EAiWebtoonCutChapterStatusGenerateImage.GENERATING
    ) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_GENERATING);
    }

    const images = cut.images;

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;

    const charactersUuids: string[] = body.characterIds
      .map(
        (id) =>
          generalCharacters.find(
            (character) => Number(character.characterId) === id,
          )?.uuid,
      )
      .filter((item) => item !== undefined) as string[];

    // const mergedCharactersUuids = Array.from(
    //   new Set([
    //     ...(cut.characters ? JSON.parse(cut.characters) : []),
    //     ...charactersUuids,
    //   ]),
    // );

    images.unshift({
      image: body.image,
      prompt: body.prompt,
      imageWidth: body.imageWidth,
      imageHeight: body.imageHeight,
      frameWidthImageContainer: body.imageWidth,
      frameHeightImageContainer: body.imageHeight,
      bubbles: [],
      effects: [],
      texts: [],
      editConfig: {},
      characters: charactersUuids,
      baseModel: body.baseModel,
      createdAt: new Date(),
    });

    const isGenerated = cut.totalNumberOfImages === images.length;

    await this.aiWebtoonCutChapterRepository.update(cut.id, {
      images,
      imageIndex: 0,
      statusGenerateImage: isGenerated
        ? EAiWebtoonCutChapterStatusGenerateImage.GENERATED
        : cut.statusGenerateImage,
      imageGenerateType: isGenerated ? null : cut.imageGenerateType,
      apiGenName: isGenerated ? null : cut.apiGenName,
      isPreview: isGenerated ? true : cut.isPreview,
    });

    if (isGenerated) {
      await this.updateStatusGenerateImageChapter(cut.aiWebtoonChapterId);
    }

    if (body.isDone) {
      await this.aiApiGenStateManagementService.updateItemsByNames(
        [body.apiName],
        false,
        null,
      );
    }

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async sketchInpaintImage(id: number, body: InpaintImageDto) {
    let cut = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id },
      relations: ['chapter', 'chapter.aiWebtoonStory'],
    });

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;

    const data: ISendInpaintRequest = {
      uuid: uuidv4(),
      cutId: cut.id,
      numberOfImages: body.numberOfImages,
      originImage: body.originImage,
      maskImage: body.maskImage,
      prompt: body.prompt,
      negativePrompt: body.negativePrompt,
      characterIds: this.filterCharactersByBaseModelInpaint(
        body.characterUuids,
        generalCharacters,
        body.baseModel,
      ).map((item) => item.characterId),
      baseModel: body.baseModel,
      pathReceived: '/ai-webtoon-cut-chapter/receive-sketch-inpaint-image',
    };

    await this.aiService.sendInpaintRequest(data);

    // await this.cacheService.set(data.uuid, {
    //   cutId: cut.id,
    //   apiName,
    //   image: null,
    // });

    return { uuid: data.uuid };
  }

  async receiveSketchInpaintImage(body: ReceiveInpaintImageDto) {
    this.logger.log('🚀 receiveSketchInpaint ~ body >> ', JSON.stringify(body));

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        apiName: body.apiName,
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Sketch Inpaint Image - Cut ${body.cutId}`,
        data: body,
        isSuccess: true,
      },
    );

    // if (!body.uuid) {
    //   return BADREQUEST('ai-webtoon-cut/uuid-not-found', 'UUID not found');
    // }

    // const data = await this.cacheService.get(body.uuid);
    // if (!data) {
    //   return BADREQUEST('ai-webtoon-cut/uuid-not-found', 'UUID not found');
    // }

    // data.image = body.image;
    // data.imageWidth = body.imageWidth;
    // data.imageHeight = body.imageHeight;
    // await Promise.all([
    //   this.cacheService.set(body.uuid, data),
    //   this.aiApiGenStateManagementService.updateItemsByNames(
    //     [body.apiName],
    //     false,
    //     null,
    //   ),
    // ]);
    return true;
  }

  async stopSketchInpaintImage(uuid: string) {
    // const data = await this.cacheService.get(uuid);
    // if (!data) {
    //   return BADREQUEST('ai-webtoon-cut/uuid-not-found', 'UUID not found');
    // }
    // await this.aiService.sendStopServiceRequest(data.apiName);
    // await this.cacheService.del(uuid);
    // return true;
  }

  async generateSketchImage(body: GenerateSketchImageDto) {
    return this.aiService.sendGenerateSketchImageRequest(body);
  }

  async getSketchInpaintImage(uuid: string) {
    // const data = await this.cacheService.get(uuid);
    // if (!data) {
    //   return BADREQUEST('ai-webtoon-cut/uuid-not-found', 'UUID not found');
    // }
    // return data;
  }

  async editImage(id: number, body: UpdateEditImageDto) {
    const cut = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id },
      relations: ['chapter', 'chapter.aiWebtoonStory'],
    });

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    const images = cut.images;

    const imageItem = images[cut.imageIndex];
    if (!imageItem) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_IMAGE_NOT_FOUND);
    }

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;

    body.bubbles.forEach((bubble) => {
      if (bubble.characterUuid) {
        const found = generalCharacters.find(
          (character) => character.uuid === bubble.characterUuid,
        );
        if (!found) {
          throw new BadRequestException(
            AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FOUND,
          );
        }
      }
    });

    images[cut.imageIndex] = {
      ...imageItem,
      frameHeightImageContainer:
        body.frameHeightImageContainer || imageItem.frameHeightImageContainer,
      finishedImage: body.finishedImage,
      editImage: body.editImage,
      bubbles: body.bubbles.map(
        (bubble) =>
          ({
            characterUuid: bubble.characterUuid,
            content: bubble.content,
            contentJson: bubble.contentJson,
            coordinate: bubble.coordinate,
            bubbleType: bubble.bubbleType,
            bubbleBorderColor: bubble.bubbleBorderColor,
            bubbleBackgroundColor: bubble.bubbleBackgroundColor,
            bubbleWidth: bubble.bubbleWidth,
            bubbleHeight: bubble.bubbleHeight,
            fontFamily: bubble.fontFamily,
            fontSize: bubble.fontSize,
            fontStyle: bubble.fontStyle,
            fontWeight: bubble.fontWeight,
            bubbleFontColor: bubble.bubbleFontColor,
          }) as AiItemDialogueBubblesDto,
      ),
      effects: body.effects?.map(
        (effect) =>
          ({
            image: effect.image,
            rotate: effect.rotate,
            width: effect.width,
            height: effect.height,
            x: effect.x,
            y: effect.y,
            scaleX: effect.scaleX,
            scaleY: effect.scaleY,
            zIndex: effect.zIndex,
          }) as ItemEffectsDto,
      ),
      texts: body.texts.map(
        (text) =>
          ({
            text: text.text,
            textJson: text.textJson,
            scale: text.scale,
            rotate: text.rotate,
            fontFamily: text.fontFamily,
            fontSize: text.fontSize,
            fontStyle: text.fontStyle,
            fontWeight: text.fontWeight,
            fontColor: text.fontColor,
            x: text.x,
            y: text.y,
            scaleX: text.scaleX,
            scaleY: text.scaleY,
            zIndex: text.zIndex,
            textAlign: text.textAlign,
          }) as ItemTextDto,
      ),
      editConfig: body.editConfig
        ? ({
            image: body.editConfig?.image,
            left: body.editConfig?.left,
            top: body.editConfig?.top,
            width: body.editConfig?.width,
            height: body.editConfig?.height,
            frameWidth: body.editConfig?.frameWidth,
            frameHeight: body.editConfig?.frameHeight,
            scaleX: body.editConfig?.scaleX,
            scaleY: body.editConfig?.scaleY,
            angle: body.editConfig?.angle,
            cropX: body.editConfig?.cropX,
            cropY: body.editConfig?.cropY,
            cropWidth: body.editConfig?.cropWidth,
            cropHeight: body.editConfig?.cropHeight,
            imageCenterPoint: body.editConfig?.imageCenterPoint
              ? {
                  x: body.editConfig?.imageCenterPoint.x,
                  y: body.editConfig?.imageCenterPoint.y,
                }
              : {
                  x: 0,
                  y: 0,
                },
          } as EditConfigDto)
        : {},
      borderConfig: {
        borderType: body.borderConfig?.borderType,
        borderColor: body.borderConfig?.borderColor,
        borderWeight: body.borderConfig?.borderWeight,
      } as BorderConfigDto,
    };

    await this.aiWebtoonCutChapterRepository.update(id, {
      images,
    });

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async updatePreviewCut(id: number) {
    const cut = await this.findOneById(id);

    const images = cut.images;
    if (images.length === 0 && !cut.isPreview) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NO_IMAGES);
    }

    await this.aiWebtoonCutChapterRepository.update(id, {
      isPreview: !cut.isPreview,
    });

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async addImageCutNormal(id: number, body: AddImageCutNormalDto) {
    const cut = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id },
      relations: ['chapter', 'chapter.aiWebtoonStory'],
    });

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    if (cut.type !== EAiCutChapterType.NORMAL) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
    }

    const images = cut.images;

    images.unshift({
      type: EAiTypeActionByNormalCut.UPLOAD_IMAGE,
      image: body.image,
      rootImage: body.image,
      finishedImage: body.image,
      editImage: body.image,
      imageWidth: body.imageWidth,
      imageHeight: body.imageHeight,
      frameWidthImageContainer: body.imageWidth,
      frameHeightImageContainer: body.imageHeight,
      bubbles: [],
      effects: [],
      texts: [],
      editConfig: {},
      borderConfig: {
        borderType: cut.chapter.aiWebtoonStory.borderType,
        borderColor: cut.chapter.aiWebtoonStory.borderColor,
        borderWeight: cut.chapter.aiWebtoonStory.borderWeight,
      },
      createdAt: new Date(),
    });

    await this.aiWebtoonCutChapterRepository.update(id, {
      images,
      totalNumberOfImages: images.length,
    });

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async addSketchImage(id: number, body: AddSketchImagesDto) {
    const cut = await this.findOneById(id);

    if (cut.type !== EAiCutChapterType.NORMAL) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
    }

    const isCannySketch =
      body.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY;
    const sketchImages = isCannySketch
      ? cut.controlNetCannyImages
        ? cut.controlNetCannyImages
        : []
      : cut.controlNetDepthImages
        ? cut.controlNetDepthImages
        : [];
    let sketchImageIndex = isCannySketch
      ? cut.controlNetCannyIndex
      : cut.controlNetDepthIndex;

    body.images.forEach((image) => {
      if (!sketchImages.some((sketch) => sketch.image === image.image)) {
        sketchImages.unshift({
          image: image.image,
          imageWidth: image.imageWidth,
          imageHeight: image.imageHeight,
        });
        if (sketchImageIndex != null) sketchImageIndex += 1;
      }
    });

    await this.aiWebtoonCutChapterRepository.update(
      id,
      isCannySketch
        ? {
            controlNetCannyImages: sketchImages,
            controlNetCannyIndex: sketchImageIndex,
            imageSizeType: EAiFrameImageSizeType.CONTROLNET_CANNY,
          }
        : {
            controlNetDepthImages: sketchImages,
            controlNetDepthIndex: sketchImageIndex,
            imageSizeType: EAiFrameImageSizeType.CONTROLNET_DEPTH,
          },
    );

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async deleteSketchImage(id: number, body: SelectSketchImageByIndexDto) {
    const cut = await this.findOneById(id);

    if (cut.type !== EAiCutChapterType.NORMAL) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
    }

    const isCannySketch =
      body.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY;
    const sketchImages = isCannySketch
      ? cut.controlNetCannyImages
        ? cut.controlNetCannyImages
        : []
      : cut.controlNetDepthImages
        ? cut.controlNetDepthImages
        : [];
    let sketchImageIndex = isCannySketch
      ? cut.controlNetCannyIndex
      : cut.controlNetDepthIndex;

    const check = sketchImages[body.imageIndex];
    if (!check) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_IMAGE_INDEX_NOT_FOUND);
    }

    sketchImages.splice(body.imageIndex, 1);

    await this.aiWebtoonCutChapterRepository.update(
      id,
      isCannySketch
        ? {
            controlNetCannyImages: sketchImages,
            controlNetCannyIndex:
              sketchImageIndex !== null
                ? sketchImages[sketchImageIndex]
                  ? sketchImageIndex
                  : 0
                : 0,
            imageSizeType:
              sketchImages.length > 0
                ? EAiFrameImageSizeType.CONTROLNET_CANNY
                : EAiFrameImageSizeType.DEFAULT,
          }
        : {
            controlNetDepthImages: sketchImages,
            controlNetDepthIndex:
              sketchImageIndex !== null
                ? sketchImages[sketchImageIndex]
                  ? sketchImageIndex
                  : 0
                : 0,
            imageSizeType:
              sketchImages.length > 0
                ? EAiFrameImageSizeType.CONTROLNET_DEPTH
                : EAiFrameImageSizeType.DEFAULT,
          },
    );

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async selectSketchImageByIndex(
    id: number,
    body: SelectSketchImageByIndexDto,
  ) {
    const cut = await this.findOneById(id);

    if (cut.type !== EAiCutChapterType.NORMAL) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
    }

    const isCannySketch =
      body.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY;
    const sketchImages = isCannySketch
      ? cut.controlNetCannyImages
        ? cut.controlNetCannyImages
        : []
      : cut.controlNetDepthImages
        ? cut.controlNetDepthImages
        : [];

    const itemSelect = sketchImages[body.imageIndex];
    if (!itemSelect) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_IMAGE_INDEX_NOT_FOUND);
    }

    await this.aiWebtoonCutChapterRepository.update(
      id,
      isCannySketch
        ? {
            imageSizeType: body.imageSizeType,
            controlNetCannyIndex: body.imageIndex,
            imageWidth: itemSelect.imageWidth,
            imageHeight: itemSelect.imageHeight,
          }
        : {
            imageSizeType: body.imageSizeType,
            controlNetDepthIndex: body.imageIndex,
            baseModel: EAiBaseModelGenImageType.SDXL_4_OPT,
            imageWidth: itemSelect.imageWidth,
            imageHeight: itemSelect.imageHeight,
          },
    );

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async unselectSketchImageByIndex(id: number, body: UnselectSketchImagesDto) {
    const cut = await this.findOneById(id);

    if (cut.type !== EAiCutChapterType.NORMAL) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_TYPE_NORMAL);
    }

    const isCannySketch =
      body.imageSizeType === EAiFrameImageSizeType.CONTROLNET_CANNY;

    await this.aiWebtoonCutChapterRepository.update(
      id,
      isCannySketch
        ? {
            controlNetCannyIndex: null,
            imageSizeType: EAiFrameImageSizeType.DEFAULT,
            imageWidth: 688,
            imageHeight: 720,
          }
        : {
            controlNetDepthIndex: null,
            imageSizeType: EAiFrameImageSizeType.DEFAULT,
            imageWidth: 688,
            imageHeight: 720,
          },
    );

    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async combineImages(body: CombineImagesDto) {
    return await this.aiService.sendCombineImagesRequest(body);
  }

  async deleteOrderGenerateImage(id: number) {
    const orderGenerate =
      await this.aiWebtoonOrderGenerateCutChapterRepository.findOne({
        where: { id },
      });
    if (!orderGenerate) {
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_ORDER_GENERATE_NOT_FOUND,
      );
    }

    await Promise.all([
      this.aiWebtoonOrderGenerateCutChapterRepository.softDelete(id),
      this.aiWebtoonCutChapterRepository
        .createQueryBuilder()
        .update()
        .set({
          statusGenerateImage: () => `CASE
            WHEN images IS NOT NULL AND images != '[]'
            THEN '${EAiWebtoonCutChapterStatusGenerateImage.GENERATED}'
            ELSE '${EAiWebtoonCutChapterStatusGenerateImage.NOT_GENERATED}'
          END`,
        })
        .whereInIds(orderGenerate.cutsId)
        .execute(),
    ]);

    await this.updateStatusGenerateImageChapter(orderGenerate.chapterId);

    // this.socketService.emitUpdateChapter(orderGenerate.chapterId);

    return true;
  }

  async generatePromptByDescription(
    id: number,
    body: GeneratePromptDto,
    isImportFile = false,
  ) {
    const cut = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id },
      relations: ['chapter', 'chapter.aiWebtoonStory'],
    });

    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    const generalCharacters = cut.chapter.aiWebtoonStory.characters;

    const cutCharacters = cut.characters;

    await this.aiService.sendGeneratePromptByDescriptionRequest({
      isImportFile,
      data: [
        {
          cutId: id,
          description: body.description,
          typePrompt: body.typePrompt,
          characters:
            body.typePrompt !== EAiGenPromptByDescriptionType.DANBOORU_PROMPT
              ? []
              : cutCharacters
                  .map((uuid) =>
                    generalCharacters.find((gChar) => gChar.uuid === uuid),
                  )
                  .filter(
                    (character): character is IAiGeneralCharacter =>
                      character !== undefined &&
                      character.type === EAiWebtoonCharacterType.SDXL,
                  ),
        },
      ],
    });

    const updateStatus = {
      [EAiGenPromptByDescriptionType.GENERAL_PROMPT]: 'isGeneratingPrompt',
      [EAiGenPromptByDescriptionType.NEGATIVE_PROMPT]:
        'isGeneratingNegativePrompt',
      [EAiGenPromptByDescriptionType.DANBOORU_PROMPT]:
        'isGeneratingDanbooruPrompt',
    };

    await this.aiWebtoonCutChapterRepository.update(id, {
      [updateStatus[body.typePrompt]]: true,
    });

    return true;
  }

  async generatePromptByDescriptionByCutsImportFile(
    chapterId: number,
    cuts: AiWebtoonCutChapterEntity[],
  ) {
    this.logger.log(`Generating prompts for ${cuts.length} cuts`);

    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id: chapterId },
      relations: ['aiWebtoonStory'],
    });
    if (!chapter) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHAPTER_NOT_FOUND);
    }
    const generalCharacters = chapter.aiWebtoonStory.characters;

    const data: IGeneratePromptByDescriptionItem[] = [];

    for (const cut of cuts) {
      const needsGeneralPrompt =
        [
          EAiBaseModelGenImageType.FLUX_NORMAL,
          EAiBaseModelGenImageType.FLUX_ADULT,
        ].includes(cut.baseModel) && !cut.generalPrompt;
      const needsDanbooruPrompt =
        [
          EAiBaseModelGenImageType.SDXL_4_OPT,
          EAiBaseModelGenImageType.DREAM_BOOTH,
        ].includes(cut.baseModel) && !cut.danbooruPrompt;
      const needsNegativePrompt =
        cut.negativeDescription && !cut.negativePrompt;

      if (needsGeneralPrompt) {
        data.push({
          cutId: cut.id,
          description: cut.description,
          typePrompt: EAiGenPromptByDescriptionType.GENERAL_PROMPT,
          characters: [],
        } as IGeneratePromptByDescriptionItem);
      }

      if (needsNegativePrompt) {
        data.push({
          cutId: cut.id,
          description: cut.negativeDescription,
          typePrompt: EAiGenPromptByDescriptionType.NEGATIVE_PROMPT,
          characters: [],
        } as IGeneratePromptByDescriptionItem);
      }

      if (needsDanbooruPrompt) {
        const cutCharacters = cut.characters;
        data.push({
          cutId: cut.id,
          description: cut.description,
          typePrompt: EAiGenPromptByDescriptionType.DANBOORU_PROMPT,
          characters: cutCharacters
            .map((uuid) =>
              generalCharacters.find((gChar) => gChar.uuid === uuid),
            )
            .filter(
              (character): character is IAiGeneralCharacter =>
                character !== undefined &&
                character.type === EAiWebtoonCharacterType.SDXL,
            ),
        } as IGeneratePromptByDescriptionItem);
      }
    }

    await this.aiService.sendGeneratePromptByDescriptionRequest({
      isImportFile: true,
      data,
    });

    const updateStatus = {
      [EAiGenPromptByDescriptionType.GENERAL_PROMPT]: 'isGeneratingPrompt',
      [EAiGenPromptByDescriptionType.NEGATIVE_PROMPT]:
        'isGeneratingNegativePrompt',
      [EAiGenPromptByDescriptionType.DANBOORU_PROMPT]:
        'isGeneratingDanbooruPrompt',
    };

    await Promise.all(
      data.map((cut) =>
        this.aiWebtoonCutChapterRepository.update(cut.cutId, {
          [updateStatus[cut.typePrompt]]: true,
        }),
      ),
    );

    // this.socketService.emitUpdateChapter(chapterId);

    return true;
  }

  async receiveGeneratePromptByDescription(
    body: ReceiveGeneratePromptByDescriptionDto,
  ) {
    this.logger.log(
      '🚀 receiveGeneratePromptByDescription ~ body >> ',
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Generate Prompt By Description - Cut ${body.cutId} - Type: ${body.typePrompt}`,
        data: body,
        isSuccess: true,
      },
    );

    const cut = await this.aiWebtoonCutChapterRepository.findOne({
      where: { id: body.cutId },
      withDeleted: true,
    });
    if (!cut) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);
    }

    const updatePrompt = {
      [EAiGenPromptByDescriptionType.GENERAL_PROMPT]: 'generalPrompt',
      [EAiGenPromptByDescriptionType.NEGATIVE_PROMPT]: 'negativePrompt',
      [EAiGenPromptByDescriptionType.DANBOORU_PROMPT]: 'danbooruPrompt',
    };
    const updateStatus = {
      [EAiGenPromptByDescriptionType.GENERAL_PROMPT]: 'isGeneratingPrompt',
      [EAiGenPromptByDescriptionType.NEGATIVE_PROMPT]:
        'isGeneratingNegativePrompt',
      [EAiGenPromptByDescriptionType.DANBOORU_PROMPT]:
        'isGeneratingDanbooruPrompt',
    };

    if (body.isImportFile) {
      cut[updatePrompt[body.typePrompt]] = body.prompt;
      cut[updateStatus[body.typePrompt]] = false;
    } else {
      cut[updateStatus[body.typePrompt]] = false;
    }

    await this.aiWebtoonCutChapterRepository.save(cut);

    if (
      body.isImportFile &&
      !cut.isGeneratingPrompt &&
      !cut.isGeneratingNegativePrompt &&
      !cut.isGeneratingDanbooruPrompt
    ) {
      await this.generateImages(cut.aiWebtoonChapterId, {
        cutId: cut.id,
      });
    }

    // this.socketService.emitPromptByDescription(
    // body.cutId,
    // body.typePrompt,
    // body.prompt,
    // );
    // this.socketService.emitUpdateChapter(cut.aiWebtoonChapterId);

    return true;
  }

  async updateOrderByPreviewCuts(
    id: number,
    body: UpdateOrderByPreviewCutsDto,
  ) {
    const cuts = await this.aiWebtoonCutChapterRepository.find({
      where: {
        aiWebtoonChapterId: id,
        id: In(body.data.map((item) => item.cutId)),
        isPreview: true,
      },
    });
    if (cuts.length !== body.data.length) {
      throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CUT_NOT_SAME_LENGTH);
    }

    cuts.forEach((cut) => {
      const cutData = body.data.find((item) => item.cutId === cut.id);
      if (!cutData) return;
      cut.orderByPreview = cutData.orderByPreview;
    });

    await this.aiWebtoonCutChapterRepository.save(cuts);
    // this.socketService.emitUpdateChapter(id);
    return true;
  }
}
