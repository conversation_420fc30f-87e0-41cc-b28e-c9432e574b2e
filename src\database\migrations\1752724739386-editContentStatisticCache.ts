import { MigrationInterface, QueryRunner } from 'typeorm';

export class EditContentStatisticCache1752724739386
  implements MigrationInterface
{
  name = 'EditContentStatisticCache1752724739386';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_statistics_cache\` ADD UNIQUE INDEX \`IDX_c37a64f44a1fd0ed49854f70c4\` (\`contentId\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content_statistics_cache\` DROP INDEX \`IDX_c37a64f44a1fd0ed49854f70c4\``,
    );
  }
}
