<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

# Project Setup and Running Guide

## Prerequisites

- Node.js (v20 or higher)
- Docker and Docker Compose
- PM2 (for production deployment)
- MySQL 8.0
- Redis

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
DB_PASSWORD=your_database_password
DB_DATABASE=your_database_name
```

## Running with Docker

1. Build and start the containers:
```bash
docker-compose -f docker-compose-dev.yml up --build
```

This will start:
- NestJS application on port 3005
- MySQL database on port 33071
- Redis on port 63799

2. To stop the containers:
```bash
docker-compose -f docker-compose-dev.yml down
```

## Running with PM2

1. Install dependencies:
```bash
npm install
```

2. Build the project:
```bash
npm run build
```

3. Start with PM2:
```bash
pm2 start dist/main.js --name "trunk-be"
```

Other PM2 commands:
```bash
# View logs
pm2 logs trunk-be

# Monitor
pm2 monit

# Stop application
pm2 stop trunk-be

# Restart application
pm2 restart trunk-be
```

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run start:dev

# Start debug mode
npm run start:debug
```

## Database Migrations

```bash
# Generate migration
npm run migration:generate --name=migration_name

# Run migrations
npm run migration:run

# Run migrations in production
npm run migration:run:prod
```

## Testing

```bash
# Unit tests
npm run test

# e2e tests
npm run test:e2e

# Test coverage
npm run test:cov
```

## Project Structure

- `src/` - Source code
- `test/` - Test files
- `dist/` - Compiled code

## API Documentation

The API documentation is available at `http://localhost:3005/api` when the server is running.

## Support

For any issues or questions, please create an issue in the repository.

## License

This project is licensed under the MIT License.

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)
