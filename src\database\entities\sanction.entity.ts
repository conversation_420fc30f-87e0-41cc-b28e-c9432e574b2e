import { Column, Entity, ManyToOne } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { SanctionType } from '../../common/setting.enum';
import { AdminEntity } from './admin.entity';
import { UserEntity } from './user.entity';
@Entity('sanction')
export class SanctionEntity extends DefaultEntity {
  @Column({ type: 'enum', enum: SanctionType })
  type: SanctionType;

  @Column()
  reason: string;

  @Column()
  adminNote: string;

  @Column({ nullable: true })
  startDate: Date;

  @Column({ nullable: true })
  endDate: Date;

  @ManyToOne(() => UserEntity, (user) => user.sanctions)
  user: UserEntity;

  @ManyToOne(() => AdminEntity, (admin) => admin.sanctions)
  admin: AdminEntity;
}
