import { Column, Entity, OneToMany } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { SettingStatus } from '../../common/status.enum';
import { SettingMetadata } from './setting_metadata.entity';
import { ContentSettingEntity } from './content_setting.entity';
@Entity('settings')
export class SettingEntity extends DefaultEntity {
  @Column({ type: 'varchar', length: 255, nullable: false })
  key: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  value: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  group: string;

  @Column({ type: 'int', nullable: false, default: 0 })
  order: number;

  @Column({
    type: 'enum',
    enum: SettingStatus,
    nullable: false,
    default: SettingStatus.ACTIVE,
  })
  status: SettingStatus;

  @OneToMany(() => SettingMetadata, (metadata) => metadata.setting)
  metadata: SettingMetadata[];

  @OneToMany(
    () => ContentSettingEntity,
    (contentSetting) => contentSetting.setting,
  )
  contentSettings: ContentSettingEntity[];
}
