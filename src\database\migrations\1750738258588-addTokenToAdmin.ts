import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTokenToAdmin1750738258588 implements MigrationInterface {
  name = 'AddTokenToAdmin1750738258588';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`admins\` ADD \`forgotPasswordToken\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`admins\` ADD \`forgotPasswordTokenExpiresAt\` datetime NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`admins\` DROP COLUMN \`forgotPasswordTokenExpiresAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`admins\` DROP COLUMN \`forgotPasswordToken\``,
    );
  }
}
