import { <PERSON><PERSON><PERSON>, Column, <PERSON>To<PERSON>ne, Unique } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { SettingEntity } from './setting.entity';

@Entity('setting_metadata')
@Unique(['key', 'setting'])
export class SettingMetadata extends DefaultEntity {
  @Column({ type: 'varchar', length: 255 })
  key: string;

  @Column({ type: 'varchar', length: 255 })
  value: string;

  @ManyToOne(() => SettingEntity, (setting) => setting.metadata)
  setting: SettingEntity;
}
