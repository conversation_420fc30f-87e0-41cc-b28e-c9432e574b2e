import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { UserAnalyticsService } from './user-analytics.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  UserAnalyticsQueryDto,
  HourlyAnalyticsQueryDto,
  MonthlyAnalyticsQueryDto,
} from './dtos/user-analytics-request.dto';
import {
  UserAnalyticsResponseDto,
  HourlyAnalyticsResponseDto,
  MonthlyAnalyticsResponseDto,
} from './dtos/user-analytics-response.dto';
import { Roles } from '../../auth/decorators/role.decorator';
import { AdminRole } from '../../../common/role.enum';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('Admin: User Analytics')
@Controller('admin/user-analytics')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
@Roles(AdminRole.ADMIN, AdminRole.SUPER_ADMIN)
export class UserAnalyticsController {
  constructor(private readonly userAnalyticsService: UserAnalyticsService) {}

  @Get('daily')
  @ApiOperation({
    summary: 'Get daily user analytics statistics',
    description: `Get daily user analytics statistics with the following data:
    - Total Views (login count)
    - User Active (active users)
    - Signup (new registrations)
    - Signup Rate (signup/totalViews * 100)
    - Delete Account (deleted accounts)
    
    Data is sourced from the user_analytics table which tracks real-time user activity events.
    Results are displayed by each day in the date range, with the most recent day shown at the top.
    Supports custom date range filtering (defaults to last 30 days).`,
  })
  @ApiResponse({
    status: 200,
    description: 'Daily user analytics statistics retrieved successfully',
    type: UserAnalyticsResponseDto,
  })
  async getDailyAnalytics(
    @Query() filters: UserAnalyticsQueryDto,
  ): Promise<UserAnalyticsResponseDto> {
    return await this.userAnalyticsService.getDailyAnalytics(filters);
  }

  @Get('hourly')
  @ApiOperation({
    summary: 'Get hourly user analytics statistics for a specific date',
    description: `Get hourly user analytics statistics for a specific date:
    - Total Views (login count) per hour
    - User Active (active users) per hour
    - Signup (new registrations) per hour
    - Signup Rate (signup/totalViews * 100) per hour
    - Delete Account (deleted accounts) per hour
    
    Data is sourced from the user_analytics table which tracks hourly user activity.
    Results show statistics for each hour of the day (00-01, 01-02, ..., 23-00).
    Requires a specific date parameter.`,
  })
  @ApiResponse({
    status: 200,
    description: 'Hourly user analytics statistics retrieved successfully',
    type: HourlyAnalyticsResponseDto,
  })
  async getHourlyAnalytics(
    @Query() filters: HourlyAnalyticsQueryDto,
  ): Promise<HourlyAnalyticsResponseDto> {
    return await this.userAnalyticsService.getHourlyAnalytics(filters);
  }

  @Get('monthly')
  @ApiOperation({
    summary: 'Get monthly user analytics statistics',
    description: `Get monthly user analytics statistics with the following data:
    - Total Views (login count) per month
    - User Active (active users) per month
    - Signup (new registrations) per month
    - Signup Rate (signup/totalViews * 100) per month
    - Delete Account (deleted accounts) per month
    - Average Daily Active Users per month
    
    Data is sourced from the user_analytics table aggregated by month.
    Results are displayed by each month in the range, with the most recent month shown at the top.
    Supports month range filtering (YYYY-MM format).`,
  })
  @ApiResponse({
    status: 200,
    description: 'Monthly user analytics statistics retrieved successfully',
    type: MonthlyAnalyticsResponseDto,
  })
  async getMonthlyAnalytics(
    @Query() filters: MonthlyAnalyticsQueryDto,
  ): Promise<MonthlyAnalyticsResponseDto> {
    return await this.userAnalyticsService.getMonthlyAnalytics(filters);
  }
}
