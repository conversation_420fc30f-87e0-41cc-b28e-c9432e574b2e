import {
  Controller,
  Get,
  Post,
  Put,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { SupportAdminService } from './services/support-admin.service';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
// Remove unused import
import {
  QAAnswerCreateDto,
  QAMultipleAnswersDto,
  QAListDto,
  QAByUserIdDto,
  DeleteListQaDto,
} from './dtos/qa-admin.dto';

@ApiTags('Admin - Q&A Management')
@Controller('admin/qa')
@UseGuards(JwtAdminAuthGuard)
@ApiBearerAuth()
export class QAAdminController {
  constructor(private readonly supportAdminService: SupportAdminService) {}

  @Get()
  @ApiOperation({
    summary: 'Admin Q&A Questions List',
    description:
      'Get paginated list of Q&A questions with advanced filters (maps to GET /qa from require.md admin section)',
  })
  @ApiQuery({
    name: 'dateType',
    required: false,
    enum: ['submit_date', 'response_date'],
  })
  @ApiQuery({
    name: 'fromTime',
    required: false,
    type: String,
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'toTime',
    required: false,
    type: String,
    example: '2025-01-31',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: [
      'all',
      'unanswered',
      'pending',
      'processing',
      'complete',
      'complete_add',
      'automated_reply',
    ],
  })
  @ApiQuery({ name: 'isRead', required: false, enum: ['all', 'true', 'false'] })
  @ApiQuery({
    name: 'searchType',
    required: false,
    enum: ['title', 'description', 'id', 'handler'],
  })
  @ApiQuery({ name: 'keyword', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'take', required: false, type: Number, example: 20 })
  @ApiResponse({
    status: 200,
    description: 'Q&A questions list with pagination',
    example: {
      data: [
        {
          id: 123,
          userId: 456,
          title: 'Payment issue with subscription',
          description: 'I was charged twice',
          files: ['https://example.com/receipt.png'],
          isRead: false,
          responseCheckTime: null,
          createdAt: '2025-01-22T09:00:00.000Z',
          updatedAt: '2025-01-22T10:30:00.000Z',
          user: {
            id: 456,
            email: '<EMAIL>',
            socialId: 'user123',
          },
          res: [
            {
              id: 789,
              status: 'complete',
              response: 'We have processed your request',
              files: [],
              responseCompletedTime: '2025-01-22T10:30:00.000Z',
              createdAt: '2025-01-22T10:30:00.000Z',
              admin: {
                id: 2,
                username: 'support_admin',
              },
            },
          ],
        },
      ],
      total: 1,
      page: 1,
      take: 20,
    },
  })
  async getQAList(@Query() filters: QAListDto) {
    // Map Q&A filters to support filters
    const supportFilters = {
      status:
        filters.status && filters.status !== 'all'
          ? (this.mapQAStatusToSupport(filters.status) as any)
          : undefined,
      dateType:
        filters.dateType === 'submit_date'
          ? ('created' as const)
          : filters.dateType === 'response_date'
            ? ('updated' as const)
            : undefined,
      fromDate: filters.fromTime,
      toDate: filters.toTime,
      searchType:
        filters.searchType === 'id'
          ? ('user_email' as const)
          : filters.searchType === 'handler'
            ? ('admin_username' as const)
            : (filters.searchType as any),
      keyword: filters.keyword,
      isRead:
        filters.isRead && filters.isRead !== 'all'
          ? filters.isRead === 'true'
          : undefined,
    };

    const result = await this.supportAdminService.getAdminTickets(
      filters.page || 1,
      filters.take || 20,
      supportFilters,
    );
    console.log(result);

    // Transform to Q&A format
    return {
      data: result.data.map((ticket: any) => ({
        id: ticket.id,
        userId: ticket.user?.id || ticket.userId,
        title: ticket.subject,
        description: ticket.description || '',
        files: ticket.attachments || [],
        isRead: ticket.isRead || false,
        responseCheckTime: ticket.lastResponseAt || null,
        createdAt: ticket.createdAt,
        updatedAt: ticket.updatedAt || ticket.createdAt,
        user: {
          id: ticket.user?.id,
          email: ticket.user?.email,
          socialId: ticket.user?.socialId || '',
        },
        res:
          ticket.responses?.map((response: any) => ({
            id: response.id,
            status: this.mapSupportStatusToQA(response.status),
            response: response.response,
            files: response.attachments || [],
            responseCompletedTime: response.responseCompletedTime,
            createdAt: response.createdAt,
            admin: {
              id: response.admin?.id,
              username: response.admin?.username,
            },
          })) || [],
      })),
      total: result.pagination?.total || result.data.length,
      page: filters.page || 1,
      take: filters.take || 20,
    };
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Admin Q&A Question Detail',
    description:
      'Get detailed view of Q&A question with all responses (maps to GET /qa/:id from require.md admin section)',
  })
  @ApiResponse({
    status: 200,
    description: 'Q&A question detail with all responses',
    example: {
      id: 123,
      userId: 456,
      title: 'Payment issue',
      description: 'Detailed description here',
      files: ['https://example.com/file.png'],
      isRead: false,
      responseCheckTime: null,
      createdAt: '2025-01-22T09:00:00.000Z',
      updatedAt: '2025-01-22T10:30:00.000Z',
      user: {
        id: 456,
        email: '<EMAIL>',
        socialId: 'user123',
      },
      res: [
        {
          id: 789,
          status: 'complete',
          response: 'Response content',
          files: [],
          responseCompletedTime: '2025-01-22T10:30:00.000Z',
          createdAt: '2025-01-22T10:30:00.000Z',
          admin: {
            id: 2,
            username: 'support_admin',
          },
        },
      ],
    },
  })
  async getQADetail(@Param('id', ParseIntPipe) questionId: number) {
    const ticket =
      await this.supportAdminService.getAdminTicketDetail(questionId);

    // Transform to Q&A format
    return {
      id: ticket.id,
      userId: ticket.user?.id || (ticket as any).userId,
      title: ticket.subject,
      description: ticket.description || '',
      files: ticket.attachments || [],
      isRead: (ticket as any).isRead || false,
      responseCheckTime: (ticket as any).responseCheckTime || null,
      createdAt: ticket.createdAt,
      updatedAt: ticket.updatedAt || ticket.createdAt,
      user: {
        id: ticket.user?.id,
        email: ticket.user?.email,
        socialId: (ticket.user as any)?.socialId || '',
      },
      res:
        ticket.responses?.map((response) => ({
          id: response.id,
          status: this.mapSupportStatusToQA(response.status),
          response: response.response,
          files: response.attachments || [],
          responseCompletedTime: response.responseCompletedTime,
          createdAt: response.createdAt,
          admin: {
            id: response.admin?.id,
            username: response.admin?.username,
          },
        })) || [],
    };
  }

  @Post('answer/:id')
  @ApiOperation({
    summary: 'Admin Answer Q&A Question',
    description:
      'Create answer for Q&A question (maps to POST /qa/answer/:id from require.md)',
  })
  @ApiResponse({
    status: 201,
    description: 'Answer created successfully',
    type: Boolean,
    example: true,
  })
  async answerQuestion(
    @Param('id', ParseIntPipe) questionId: number,
    @Body() answerDto: QAAnswerCreateDto,
    @Request() req: any,
  ): Promise<boolean> {
    // Map Q&A answer to support response
    const supportResponseDto = {
      response: answerDto.response,
      attachments: answerDto.files || [],
      status: this.mapQAStatusToSupport(answerDto.status),
      isInternal: false, // Q&A responses are always public
    };

    const result = await this.supportAdminService.respondToTicket(
      questionId,
      supportResponseDto,
      req.user.id,
    );

    return !!result.success;
  }

  @Post('answer-multiple/:id')
  @ApiOperation({
    summary: 'Admin Answer Q&A Question with Multiple Responses',
    description: 'Create multiple answers for Q&A question in a single request',
  })
  @ApiResponse({
    status: 201,
    description: 'Multiple answers created successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        totalCreated: { type: 'number', example: 2 },
        responses: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              responseId: {
                type: 'number',
                nullable: true,
                description: 'Response ID if successful, null if failed',
              },
              success: { type: 'boolean' },
              error: {
                type: 'string',
                description: 'Error message if creation failed',
              },
            },
          },
        },
      },
    },
  })
  async answerQuestionMultiple(
    @Param('id', ParseIntPipe) questionId: number,
    @Body() multipleAnswersDto: QAMultipleAnswersDto,
    @Request() req: any,
  ): Promise<{
    success: boolean;
    totalCreated: number;
    responses: Array<{
      responseId: number | null;
      success: boolean;
      error?: string;
    }>;
  }> {
    const results = await this.supportAdminService.createMultipleResponses(
      questionId,
      multipleAnswersDto.answers.map((answer: QAAnswerCreateDto) => ({
        response: answer.response,
        attachments: answer.files || [],
        status: this.mapQAStatusToSupport(answer.status),
        isInternal: false, // Q&A responses are always public
      })),
      req.user.id,
    );

    return {
      success: results.success,
      totalCreated: results.responses.length,
      responses: results.responses,
    };
  }

  @Put('update-answer/:id')
  @ApiOperation({
    summary: 'Update Q&A Answer',
    description:
      'Update existing Q&A answer (maps to PUT /qa/update-answer/:id from require.md)',
  })
  @ApiResponse({
    status: 200,
    description: 'Answer updated successfully',
    type: Boolean,
    example: true,
  })
  async updateAnswer(
    @Param('id', ParseIntPipe) responseId: number,
    @Body() updateDto: QAAnswerCreateDto,
    @Request() req: any,
  ): Promise<boolean> {
    // Map Q&A update to support response update
    const supportUpdateDto = {
      response: updateDto.response,
      attachments: updateDto.files || [],
      status: this.mapQAStatusToSupport(updateDto.status),
    };

    const result = await this.supportAdminService.updateResponse(
      responseId,
      supportUpdateDto,
      req.user.id,
    );

    return !!result.success;
  }

  @Get('analyse/statistics')
  @ApiOperation({
    summary: 'Q&A Statistics Dashboard',
    description:
      'Get Q&A statistics for admin dashboard (maps to GET /qa/analyse/statistics from require.md)',
  })
  @ApiResponse({
    status: 200,
    description: 'Q&A statistics',
    example: {
      countStatusPending: 5,
      countIsRead: 12,
    },
  })
  async getQAStatistics() {
    const analytics =
      await this.supportAdminService.getSupportAnalytics('today');

    return {
      countStatusPending: analytics.ticketsByStatus?.assigned || 0,
      countIsRead:
        analytics.overview?.totalTickets - analytics.overview?.closedTickets ||
        0,
    };
  }

  @Get('user/:id')
  @ApiOperation({
    summary: 'Get Q&A Questions by User',
    description:
      'Get paginated list of Q&A questions for specific user (maps to GET /qa/user/:id from require.md)',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'take', required: false, type: Number, example: 20 })
  @ApiResponse({
    status: 200,
    description: 'User Q&A questions with pagination',
    example: {
      data: [],
      total: 0,
      page: 1,
      take: 20,
    },
  })
  async getQAByUserId(
    @Param('id', ParseIntPipe) userId: number,
    @Query() pagination: QAByUserIdDto,
  ) {
    const result = await this.supportAdminService.getTicketsByUser(
      userId,
      pagination.page || 1,
      pagination.take || 20,
    );

    return {
      data: result.data.map((ticket) => ({
        id: ticket.id,
        userId: ticket.userId,
        title: ticket.subject,
        description: ticket.description,
        files: ticket.attachments || [],
        isRead: ticket.isRead || false,
        responseCheckTime: ticket.responseCheckTime,
        createdAt: ticket.createdAt,
        updatedAt: ticket.updatedAt,
        res:
          ticket.responses?.map((response) => ({
            id: response.id,
            status: this.mapSupportStatusToQA(response.status),
            response: response.response,
            files: response.attachments || [],
            responseCompletedTime: response.responseCompletedTime,
            createdAt: response.createdAt,
            admin: {
              id: response.admin?.id,
              username: response.admin?.username,
            },
          })) || [],
      })),
      total: result.total,
      page: result.page,
      take: result.limit,
    };
  }

  @Post('delete')
  @ApiOperation({
    summary: 'Delete Multiple Q&A Questions',
    description:
      'Soft delete multiple Q&A questions (maps to POST /qa/delete from require.md)',
  })
  @ApiResponse({
    status: 200,
    description: 'Questions deleted successfully',
    type: Boolean,
    example: true,
  })
  async deleteQAQuestions(
    @Body() deleteDto: DeleteListQaDto,
  ): Promise<boolean> {
    const result = await this.supportAdminService.bulkDeleteTickets(
      deleteDto.ids,
    );
    return !!result.success;
  }

  /**
   * Map Q&A status to Support status
   */
  private mapQAStatusToSupport(qaStatus: string): string {
    const statusMap = {
      all: undefined,
      unanswered: 'unanswered',
      pending: 'pending',
      processing: 'processing',
      complete: 'complete',
      complete_add: 'complete_add',
      automated_reply: 'automatedReply', // Auto-reply moves to in-progress
    };

    return statusMap[qaStatus] || qaStatus;
  }

  /**
   * Map Support status to Q&A status
   */
  private mapSupportStatusToQA(supportStatus: string): string {
    const statusMap = {
      OPEN: 'unanswered',
      ASSIGNED: 'pending',
      IN_PROGRESS: 'processing',
      PENDING_USER: 'processing',
      CLOSED: 'complete',
      UNANSWERED: 'unanswered',
      PENDING: 'pending',
      PROCESSING: 'processing',
      COMPLETE: 'complete',
      COMPLETE_ADD: 'complete_add',
      AUTOMATED_REPLY: 'automated_reply',
    };

    return statusMap[supportStatus] || 'unanswered';
  }
}
