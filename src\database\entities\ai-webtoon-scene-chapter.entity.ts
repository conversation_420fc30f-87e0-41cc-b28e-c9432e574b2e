import {
  EAiFrameImageSizeType,
  EAiWebtoonBasicStatus,
} from 'src/common/ai-webtoon.enum';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';

import { AiWebtoonChapterEntity } from './ai-webtoon-chapter.entity';
import { AiWebtoonCutChapterEntity } from './ai-webtoon-cut-chapter.entity';
import { DefaultEntity } from './default.entity';
import { IAiImageItemSceneChapterItem } from 'src/common/interfaces/ai-webtoon/ai-webtoon-scene-chapter.interface';

@Entity('ai_webtoon_scene_chapter')
export class AiWebtoonSceneChapterEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_chapter_id' })
  aiWebtoonChapterId: number;

  @Column({ type: 'varchar' })
  title: string;

  @Column({ nullable: true, type: 'text' })
  description?: string | null;

  @Column({ name: 'negative_prompt', nullable: true, type: 'text' })
  negativePrompt?: string | null;

  @Column({ nullable: true, type: 'text' })
  prompt?: string | null;

  @Column({ name: 'number_of_images', default: 1 })
  numberOfImages: number;

  @Column({ default: 688, type: 'int' })
  width: number;

  @Column({ default: 720, type: 'int' })
  height: number;

  @Column({ type: 'json', nullable: true })
  images: IAiImageItemSceneChapterItem[] | null;

  @Column({
    name: 'image_size_type',
    type: 'enum',
    enum: EAiFrameImageSizeType,
    default: EAiFrameImageSizeType.DEFAULT,
  })
  imageSizeType: EAiFrameImageSizeType;

  @Column({ default: 1, type: 'int' })
  order: number;

  @Column({
    name: 'status_generate_image',
    type: 'enum',
    enum: EAiWebtoonBasicStatus,
    default: EAiWebtoonBasicStatus.NOT_GENERATED,
  })
  statusGenerateImage: EAiWebtoonBasicStatus;

  @ManyToOne(() => AiWebtoonChapterEntity)
  @JoinColumn({ name: 'ai_webtoon_chapter_id' })
  chapter: AiWebtoonChapterEntity;

  @OneToMany(
    () => AiWebtoonCutChapterEntity,
    (aiWebtoonCutChapter) => aiWebtoonCutChapter.scene,
  )
  cuts: AiWebtoonCutChapterEntity[];
}
