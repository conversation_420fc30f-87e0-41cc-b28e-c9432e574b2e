import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonCutChapterEntity } from '../entities/ai-webtoon-cut-chapter.entity';

@Injectable()
export class AiWebtoonCutChapterRepository extends Repository<AiWebtoonCutChapterEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonCutChapterEntity, dataSource.createEntityManager());
  }
}