import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { ContentStatsRequestDto } from './dtos/content-stats-request.dto';
import {
  ContentStatsResponseDto,
  ContentAnalyticsStatsDto,
  EpisodeViewStatsDto,
} from './dtos/content-stats-response.dto';

@Injectable()
export class ContentStatsService {
  constructor(private dataSource: DataSource) {}

  async getContentStats(
    filters: ContentStatsRequestDto,
  ): Promise<ContentStatsResponseDto> {
    const { startDate, endDate, language } = filters;

    // Set default date range if not provided (last 30 days)
    const defaultEndDate = new Date();
    const defaultStartDate = new Date();
    defaultStartDate.setDate(defaultStartDate.getDate() - 30);

    const queryStartDate = startDate ? new Date(startDate) : defaultStartDate;
    const queryEndDate = endDate ? new Date(endDate) : defaultEndDate;

    // Format dates for SQL query (YYYY-MM-DD)
    const formattedStartDate = queryStartDate.toISOString().split('T')[0];
    const formattedEndDate = queryEndDate.toISOString().split('T')[0];

    // Build language filter condition
    const languageCondition =
      language && language !== 'all' ? 'AND cs_lang.settingId = ?' : '';

    // Main query from content_analytics table
    const query = `
      SELECT 
        ca.date,
        SUM(CASE WHEN c.isAdult = 1 AND e.paymentType = 'free' THEN ca.viewCount ELSE 0 END) as freeAdultViews,
        SUM(CASE WHEN c.isAdult = 0 AND e.paymentType = 'free' THEN ca.viewCount ELSE 0 END) as freeNonAdultViews,
        SUM(CASE WHEN c.isAdult = 1 AND e.paymentType = 'paid' THEN ca.viewCount ELSE 0 END) as paidAdultViews,
        SUM(CASE WHEN c.isAdult = 0 AND e.paymentType = 'paid' THEN ca.viewCount ELSE 0 END) as paidNonAdultViews
      FROM content_analytics ca
      LEFT JOIN content c ON ca.contentId = c.id
      LEFT JOIN episode e ON ca.episodeId = e.id
      LEFT JOIN content_setting cs_lang ON c.id = cs_lang.contentId AND cs_lang.type = 'language'
      WHERE ca.date BETWEEN ? AND ?
        AND c.deletedAt IS NULL
        ${languageCondition}
      GROUP BY ca.date
      ORDER BY ca.date DESC
    `;

    // Set query parameters in correct order
    const params: any[] = [formattedStartDate, formattedEndDate];

    if (language && language !== 'all') {
      params.push(language);
    }

    const rawResults = await this.dataSource.query(query, params);

    // Process results into daily analytics stats
    const processedData: ContentAnalyticsStatsDto[] = rawResults.map(
      (row: any) => {
        const freeAdultViews = parseInt(row.freeAdultViews) || 0;
        const freeNonAdultViews = parseInt(row.freeNonAdultViews) || 0;
        const paidAdultViews = parseInt(row.paidAdultViews) || 0;
        const paidNonAdultViews = parseInt(row.paidNonAdultViews) || 0;

        const freeEpisodeViews: EpisodeViewStatsDto = {
          adult: freeAdultViews,
          nonAdult: freeNonAdultViews,
        };

        const paidEpisodeReads: EpisodeViewStatsDto = {
          adult: paidAdultViews,
          nonAdult: paidNonAdultViews,
        };

        const combinedBySection: EpisodeViewStatsDto = {
          adult: freeAdultViews + paidAdultViews,
          nonAdult: freeNonAdultViews + paidNonAdultViews,
        };

        const totalCombined =
          freeAdultViews +
          freeNonAdultViews +
          paidAdultViews +
          paidNonAdultViews;

        return {
          date: row.date.toISOString().split('T')[0], // Format as YYYY-MM-DD
          freeEpisodeViews,
          paidEpisodeReads,
          combinedBySection,
          totalCombined,
        };
      },
    );

    return {
      data: processedData,
      total: processedData.length,
      queryInfo: {
        startDate: formattedStartDate,
        endDate: formattedEndDate,
        language: language || 'all',
      },
    };
  }

  /**
   * Legacy method for adult content detection based on classification
   * Note: Current implementation uses content.isAdult field directly in SQL queries
   * This method is kept for backward compatibility and alternative classification logic
   */
  private isAdultContent(classification: string): boolean {
    // This logic may need to be adjusted based on your actual classification values
    // Common adult content classification values might be 'ADULT', 'MATURE', '18+', etc.
    if (!classification) return false;

    const adultKeywords = ['adult', 'mature', '18+', 'restricted'];
    return adultKeywords.some((keyword) =>
      classification.toLowerCase().includes(keyword.toLowerCase()),
    );
  }

  /**
   * Get content analytics stats with additional summary information
   */
  async getContentAnalyticsSummary(filters: ContentStatsRequestDto): Promise<{
    stats: ContentStatsResponseDto;
    summary: {
      totalDays: number;
      totalViews: number;
      averageViewsPerDay: number;
      topViewDay: { date: string; views: number };
      adultContentPercentage: number;
    };
  }> {
    const stats = await this.getContentStats(filters);

    // Calculate summary statistics
    const totalViews = stats.data.reduce(
      (sum, day) => sum + day.totalCombined,
      0,
    );
    const averageViewsPerDay =
      stats.total > 0 ? Math.round(totalViews / stats.total) : 0;

    const topViewDay = stats.data.reduce(
      (max, day) =>
        day.totalCombined > max.views
          ? { date: day.date, views: day.totalCombined }
          : max,
      { date: '', views: 0 },
    );

    const totalAdultViews = stats.data.reduce(
      (sum, day) => sum + day.combinedBySection.adult,
      0,
    );
    const adultContentPercentage =
      totalViews > 0 ? Math.round((totalAdultViews / totalViews) * 100) : 0;

    return {
      stats,
      summary: {
        totalDays: stats.total,
        totalViews,
        averageViewsPerDay,
        topViewDay,
        adultContentPercentage,
      },
    };
  }
}
