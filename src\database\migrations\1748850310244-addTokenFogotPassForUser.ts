import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddTokenFogotPassForUser1748850310244
  implements MigrationInterface
{
  name = 'AddTokenFogotPassForUser1748850310244';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`forgotPasswordToken\` varchar(255) NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`forgotPasswordTokenExpiresAt\` datetime NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`forgotPasswordTokenExpiresAt\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP COLUMN \`forgotPasswordToken\``,
    );
  }
}
