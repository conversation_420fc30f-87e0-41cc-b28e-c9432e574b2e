<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Support Ticket - {{ticketNumber}}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #dc3545; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="margin: 0;">🚨 New Support Ticket Received</h2>
        </div>
        
        <p>A new support ticket has been submitted and requires attention.</p>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #495057;">Ticket Information:</h3>
            <p><strong>Ticket Number:</strong> {{ticketNumber}}</p>
            <p><strong>Subject:</strong> {{subject}}</p>
            <p><strong>Category:</strong> {{category}}</p>
            <p><strong>Priority:</strong> <span style="color: 
                {{#if (eq priority 'urgent')}}#dc3545
                {{else}}{{#if (eq priority 'high')}}#fd7e14
                {{else}}{{#if (eq priority 'medium')}}#ffc107
                {{else}}#28a745{{/if}}{{/if}}{{/if}}
            ;">{{priority}}</span></p>
            <p><strong>User:</strong> {{userEmail}}</p>
            <p><strong>Submitted:</strong> {{createdAt}}</p>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin-top: 0;"><strong>📝 Description:</strong></p>
            <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
                <div style="white-space: pre-line;">{{description}}</div>
            </div>
        </div>
        
        {{#if contextData}}
        <div style="background: #d1ecf1; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin-top: 0;"><strong>🔍 Technical Context:</strong></p>
            <div style="font-family: 'Courier New', monospace; font-size: 12px; background: white; padding: 10px; border-radius: 4px; margin-top: 10px;">
                {{#if contextData.contentId}}Content ID: {{contextData.contentId}}<br>{{/if}}
                {{#if contextData.episodeId}}Episode ID: {{contextData.episodeId}}<br>{{/if}}
                {{#if contextData.subscriptionId}}Subscription: {{contextData.subscriptionId}}<br>{{/if}}
                {{#if contextData.paymentId}}Payment ID: {{contextData.paymentId}}<br>{{/if}}
                {{#if contextData.deviceInfo}}Device: {{contextData.deviceInfo}}<br>{{/if}}
                {{#if contextData.errorCode}}Error: {{contextData.errorCode}}<br>{{/if}}
                {{#if contextData.userAgent}}User Agent: {{contextData.userAgent}}{{/if}}
            </div>
        </div>
        {{/if}}
        
        <div style="background: #e2e3e5; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin-top: 0;"><strong>⚡ Action Required:</strong></p>
            <ul style="margin: 10px 0 0 20px;">
                <li>Review and assign the ticket to appropriate team member</li>
                <li>Respond within expected SLA timeframe</li>
                <li>Update ticket priority if necessary</li>
                <li>Escalate to technical team if needed</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{adminDomain}}/admin/support/tickets/{{ticketId}}" 
               style="background: #007bff; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; display: inline-block; margin-right: 10px;">
                View Ticket in Admin Panel
            </a>
            <a href="{{adminDomain}}/admin/support/tickets/{{ticketId}}/respond" 
               style="background: #28a745; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; display: inline-block;">
                Respond Now
            </a>
        </div>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <div style="font-size: 14px; color: #666;">
            <p><strong>Admin Notification</strong></p>
            <p>This notification was sent to the support team. Please do not forward to users.</p>
            <p>Ticket ID: {{ticketId}} | Generated at: {{timestamp}}</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
            <p>© {{year}} Webtoon Platform - Admin System</p>
        </div>
    </div>
</body>
</html>