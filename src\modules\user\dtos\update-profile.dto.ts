import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, Matches } from 'class-validator';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import configuration from '../../../config/configuration.global';
import { Gender } from '../../../common/common.config';

export class UpdateProfileDto {
  @ApiProperty({
    description: 'The gender of the user',
    example: Gender.MALE,
    enum: Object.values(Gender),
  })
  @IsString()
  @IsOptional()
  gender: string;

  @ApiProperty({
    description: 'The mature content of the user',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  isMatureContent: boolean;

  @ApiProperty({
    description: 'The agree use infomation of the user',
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  agreeUseInfomation: boolean;

  @ApiProperty({
    description: 'The old password of the user',
    example: 'oldpassword',
  })
  @IsString()
  @IsOptional()
  oldPassword: string;

  @ApiProperty({
    description: 'The new password of the user',
    example: 'newpassword',
  })
  @IsString()
  @IsOptional()
  @Matches(configuration().auth.regex.password, {
    message: MESSAGE_CONFIG.PASSWORD_REGEX.message,
  })
  newPassword: string;
}
