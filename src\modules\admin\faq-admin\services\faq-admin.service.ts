import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../../../common/message.config';
import { FaqRepository } from '../../../../database/repositories/faq.repository';
import { SupportCategoryRepository } from '../../../../database/repositories/support-category.repository';
import { CategoryStatus } from '../../../../database/entities/support-category.entity';
import { FaqStatus } from '../../../../database/entities/faq.entity';
import { CreateFaqDto } from '../dtos/create-faq.dto';
import { UpdateFaqDto } from '../dtos/update-faq.dto';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';
import * as DOMPurify from 'isomorphic-dompurify';

@Injectable()
export class FaqAdminService {
  constructor(
    private readonly faqRepository: FaqRepository,
    private readonly supportCategoryRepository: SupportCategoryRepository,
  ) {}

  async createFaq(createFaqDto: CreateFaqDto, adminId: number) {
    // Validate category if provided
    if (createFaqDto.categoryId) {
      const category = await this.supportCategoryRepository.findOne({
        where: { id: createFaqDto.categoryId, status: CategoryStatus.ACTIVE },
      });
      if (!category) {
        throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
      }
    }

    // Sanitize HTML content
    const sanitizedContent = DOMPurify.sanitize(createFaqDto.content);

    const savedFaq = await this.faqRepository.save({
      adminId,
      title: createFaqDto.title,
      categoryId: createFaqDto.categoryId,
      status: createFaqDto.status || FaqStatus.ACTIVE,
      content: sanitizedContent,
      order: createFaqDto.order || 1,
    });

    return {
      success: true,
      faqId: savedFaq.id,
      message: 'FAQ created successfully',
    };
  }

  async getFaqList(query: FaqListQueryDto) {
    const { page = 1, limit = 20, ...filters } = query;

    const [faqs, total] = await this.faqRepository.findAdminFaqList(
      page,
      limit,
      filters,
    );

    return {
      data: faqs.map((faq) => ({
        id: faq.id,
        title: faq.title,
        category: faq.category
          ? {
              id: faq.category.id,
              name: faq.category.name,
              title: faq.category.title,
            }
          : null,
        status: faq.status,
        viewCount: faq.viewCount,
        order: faq.order,
        admin: {
          id: faq.admin.id,
          username: faq.admin.username,
        },
        createdAt: faq.createdAt,
        updatedAt: faq.updatedAt,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getFaqDetail(id: number) {
    const faq = await this.faqRepository.findOne({
      where: { id },
      relations: ['admin', 'category'],
    });

    if (!faq) {
      throw new NotFoundException(MESSAGE_CONFIG.FAQ_NOT_FOUND);
    }

    return {
      id: faq.id,
      title: faq.title,
      content: faq.content,
      category: faq.category
        ? {
            id: faq.category.id,
            name: faq.category.name,
            title: faq.category.title,
          }
        : null,
      status: faq.status,
      viewCount: faq.viewCount,
      order: faq.order,
      admin: {
        id: faq.admin.id,
        username: faq.admin.username,
      },
      createdAt: faq.createdAt,
      updatedAt: faq.updatedAt,
    };
  }

  async updateFaq(id: number, updateFaqDto: UpdateFaqDto) {
    const faq = await this.faqRepository.findOne({ where: { id } });
    if (!faq) {
      throw new NotFoundException(MESSAGE_CONFIG.FAQ_NOT_FOUND);
    }

    // Validate category if provided
    if (updateFaqDto.categoryId) {
      const category = await this.supportCategoryRepository.findOne({
        where: { id: updateFaqDto.categoryId, status: CategoryStatus.ACTIVE },
      });
      if (!category) {
        throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
      }
    }

    // Sanitize HTML content if provided
    let sanitizedContent = faq.content;
    if (updateFaqDto.content) {
      sanitizedContent = DOMPurify.sanitize(updateFaqDto.content);
    }

    await this.faqRepository.update(id, {
      ...updateFaqDto,
      content: sanitizedContent,
    });

    return {
      success: true,
      message: 'FAQ updated successfully',
    };
  }

  async deleteFaqs(ids: number[]) {
    if (!ids || ids.length === 0) {
      throw new BadRequestException(MESSAGE_CONFIG.NO_IDS_PROVIDED);
    }

    await this.faqRepository.softDeleteMany(ids);

    return {
      success: true,
      deletedCount: ids.length,
      message: 'FAQs deleted successfully',
    };
  }
}
