import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { SupportCategoryEntity } from './support-category.entity';
import { AdminEntity } from './admin.entity';
import { SupportResponseEntity } from './support-response.entity';

export enum TicketPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent',
}

export enum TicketStatus {
  OPEN = 'open',
  ASSIGNED = 'assigned',
  IN_PROGRESS = 'in_progress',
  PENDING_USER = 'pending_user',
  CLOSED = 'closed',
}

export interface TicketContextData {
  contentId?: number;
  episodeId?: number;
  subscriptionId?: number;
  paymentId?: number;
  deviceInfo?: string;
  errorCode?: string;
  userAgent?: string;
}

@Entity('support_ticket')
export class SupportTicketEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ name: 'category_id', nullable: true })
  categoryId: number;

  @Column({ name: 'ticket_number', unique: true })
  ticketNumber: string; // Auto-generated (e.g., "SUP-2024-001")

  @Column()
  subject: string; // Tiêu đề ticket

  @Column('text')
  description: string; // Mô tả chi tiết

  @Column('json', { nullable: true })
  attachments: string[]; // File đính kèm URLs

  @Column({
    type: 'enum',
    enum: TicketPriority,
    default: TicketPriority.MEDIUM,
  })
  priority: TicketPriority;

  @Column({
    type: 'enum',
    enum: TicketStatus,
    default: TicketStatus.OPEN,
  })
  status: TicketStatus;

  @Column({ name: 'assigned_admin_id', nullable: true })
  assignedAdminId: number; // Admin được assign

  @Column({ name: 'is_read', default: false })
  isRead: boolean; // User đã đọc response chưa

  @Column({ name: 'response_check_time', nullable: true })
  responseCheckTime: Date; // Thời điểm user check response

  @Column({ name: 'closed_at', nullable: true })
  closedAt: Date; // Thời điểm đóng ticket

  // Context data đặc biệt cho webtoon platform
  @Column('json', { nullable: true })
  contextData: TicketContextData;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;

  // Relations
  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => SupportCategoryEntity)
  @JoinColumn({ name: 'category_id' })
  category: SupportCategoryEntity;

  @ManyToOne(() => AdminEntity, { nullable: true })
  @JoinColumn({ name: 'assigned_admin_id' })
  assignedAdmin: AdminEntity;

  @OneToMany(() => SupportResponseEntity, (response) => response.ticket)
  responses: SupportResponseEntity[];
}
