import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import {
  EAiDirectionApi,
  EAiWebtoonBasicStatus,
} from 'src/common/ai-webtoon.enum';
import { AI_MESSAGE_CONFIG } from 'src/common/message.config';
import { In } from 'typeorm';
import { AiApiGenStateManagementService } from '../ai-api-gen-state-management/ai-api-gen-state-management.service';
// import { SocketService } from '../socket/socket.service';
import { ISendGenerateSceneImagesRequest } from 'src/common/interfaces/ai-webtoon/ai-send-request.interface';
import { IAiImageItemSceneChapterItem } from 'src/common/interfaces/ai-webtoon/ai-webtoon-scene-chapter.interface';
import { AiWebtoonSceneChapterEntity } from 'src/database/entities/ai-webtoon-scene-chapter.entity';
import { AiWebtoonChapterRepository } from 'src/database/repositories/ai-webtoon-chapter.repository';
import { AiWebtoonCutChapterRepository } from 'src/database/repositories/ai-webtoon-cut-chapter.repository';
import { AiWebtoonSceneChapterRepository } from 'src/database/repositories/ai-webtoon-scene-chapter.repository';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { AiService } from '../ai-service/ai-service.service';
import { AddImagesDto, RemoveImageDto } from './dto/add-images.dto';
import { CreateSceneDto } from './dto/create-scene.dto';
import { ReceiveGenerateImagesDto } from './dto/receive-generate-images.dto';
import { UpdateSceneDto } from './dto/update-scene.dto';

@Injectable()
export class AiWebtoonSceneChapterService {
  private readonly logger = new Logger(AiWebtoonSceneChapterService.name);

  constructor(
    private readonly aiWebtoonSceneChapterRepository: AiWebtoonSceneChapterRepository,
    private readonly aiWebtoonChapterRepository: AiWebtoonChapterRepository,
    private readonly aiWebtoonCutChapterRepository: AiWebtoonCutChapterRepository,
    private readonly aiApiGenStateManagementService: AiApiGenStateManagementService,
    // private readonly socketService: SocketService,
    private readonly notificationGoogleService: NotificationGoogleService,
    private readonly aiService: AiService,
  ) {}

  async getChapterById(chapterId: number) {
    const chapter = await this.aiWebtoonChapterRepository.findOne({
      where: { id: chapterId },
    });
    if (!chapter) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CHAPTER_NOT_FOUND);
    }
    return chapter;
  }

  async getSceneById(id: number) {
    const scene = await this.aiWebtoonSceneChapterRepository.findOne({
      where: { id },
    });
    if (!scene) {
      throw new NotFoundException(AI_MESSAGE_CONFIG.AI_SCENE_NOT_FOUND);
    }
    return scene;
  }

  async createSceneByChapter(chapterId: number, body: CreateSceneDto) {
    await this.getChapterById(chapterId);

    let cutIds: number[] = [];
    if (body.cutIds && body.cutIds.length) {
      const cuts = await this.aiWebtoonCutChapterRepository.find({
        where: {
          aiWebtoonSceneChapterId: In(
            await this.aiWebtoonSceneChapterRepository
              .createQueryBuilder('scene')
              .select('scene.id')
              .where('scene.aiWebtoonChapterId = :chapterId', { chapterId })
              .getMany()
              .then((scenes) => scenes.map((scene) => scene.id)),
          ),
          id: In(body.cutIds),
        },
      });

      if (!cuts.length)
        throw new NotFoundException(AI_MESSAGE_CONFIG.AI_CUT_NOT_FOUND);

      cutIds = cuts.map((cut) => cut.id);
    }

    await this.aiWebtoonSceneChapterRepository
      .createQueryBuilder()
      .update()
      .set({
        order: () => '`order` + 1',
      })
      .where('ai_webtoon_chapter_id = :chapterId', { chapterId })
      .andWhere('order >= :order', { order: body.order })
      .execute();

    const scene = await this.aiWebtoonSceneChapterRepository.save({
      aiWebtoonChapterId: chapterId,
      title: body.title,
      order: body.order,
      images: [],
    } as AiWebtoonSceneChapterEntity);

    if (cutIds.length) {
      await Promise.all(
        cutIds.map((cutId, index) =>
          this.aiWebtoonCutChapterRepository.update(
            { id: cutId },
            {
              aiWebtoonSceneChapterId: scene.id,
              order: index + 1,
            },
          ),
        ),
      );
    }

    // this.socketService.emitUpdateChapter(chapterId);

    return {
      sceneId: scene.id,
    };
  }

  async updateScene(id: number, body: UpdateSceneDto) {
    const scene = await this.getSceneById(id);

    await this.aiWebtoonSceneChapterRepository.update(id, {
      title: body.title || scene.title,
      description: body.description,
      negativePrompt: body.negativePrompt,
      prompt: body.prompt,
      numberOfImages: body.numberOfImages || scene.numberOfImages,
      imageSizeType: body.imageSizeType || scene.imageSizeType,
      width: body.width || scene.width,
      height: body.height || scene.height,
    });

    // this.socketService.emitUpdateChapter(scene.aiWebtoonChapterId);

    return true;
  }

  async deleteScene(id: number) {
    const scene = await this.getSceneById(id);
    await Promise.all([
      this.aiWebtoonCutChapterRepository.softDelete({
        aiWebtoonSceneChapterId: id,
      }),
      this.aiWebtoonSceneChapterRepository.softDelete(id),
    ]);

    await this.aiWebtoonSceneChapterRepository
      .createQueryBuilder()
      .update()
      .set({
        order: () => '`order` - 1',
      })
      .where('ai_webtoon_chapter_id = :chapterId', {
        chapterId: scene.aiWebtoonChapterId,
      })
      .andWhere('order > :order', { order: scene.order })
      .execute();

    // this.socketService.emitUpdateChapter(scene.aiWebtoonChapterId);

    return true;
  }

  async generateImages(id: number) {
    const scene = await this.getSceneById(id);

    if (scene.statusGenerateImage === EAiWebtoonBasicStatus.GENERATING) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_SCENE_IMAGE_GENERATION_ALREADY_STARTED,
      );
    }

    if (!scene.prompt) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_SCENE_IMAGE_GENERATION_PROMPT_NOT_FOUND,
      );
    }

    const data: ISendGenerateSceneImagesRequest = {
      sceneId: scene.id,
      description: scene.description || '',
      prompt: scene.prompt,
      numberOfImages: scene.numberOfImages,
      width: scene.width,
      height: scene.height,
      rotate: scene.imageSizeType,
    };

    await this.aiService.sendGenerateSceneImagesRequest(data);
    await Promise.all([
      this.aiWebtoonSceneChapterRepository.update(scene.id, {
        statusGenerateImage: EAiWebtoonBasicStatus.GENERATING,
      }),
    ]);

    // this.socketService.emitUpdateChapter(scene.aiWebtoonChapterId);

    return true;
  }

  async receiveGenerateImages(body: ReceiveGenerateImagesDto) {
    this.logger.log(
      `🚀 receiveGenerateSceneImages ${body.apiName} ~ body >> `,
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        apiName: body.apiName,
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Generate Scene Images - Scene ${body.sceneId}`,
        data: body,
        isSuccess: true,
      },
    );

    const [scene] = await Promise.all([
      this.getSceneById(body.sceneId),
      this.aiApiGenStateManagementService.checkIsWorkingByName(body.apiName),
    ]);
    if (scene.statusGenerateImage !== EAiWebtoonBasicStatus.GENERATING) {
      throw new BadRequestException(
        AI_MESSAGE_CONFIG.AI_SCENE_IMAGE_GENERATION_NOT_STARTED,
      );
    }

    const images = scene.images;

    body.images.forEach((e) => {
      images.unshift({
        image: e,
        width: scene.width,
        height: scene.height,
        imageSizeType: scene.imageSizeType,
        description: scene.description,
        prompt: scene.prompt,
      } as IAiImageItemSceneChapterItem);
    });

    await Promise.all([
      this.aiWebtoonSceneChapterRepository.update(scene.id, {
        images,
        statusGenerateImage: EAiWebtoonBasicStatus.GENERATED,
      }),
      this.aiApiGenStateManagementService.updateItemsByNames(
        [body.apiName],
        false,
        null,
      ),
    ]);

    // this.socketService.emitUpdateChapter(scene.aiWebtoonChapterId);

    return true;
  }

  async addImages(id: number, body: AddImagesDto) {
    const scene = await this.getSceneById(id);
    const images = scene.images;
    await this.aiWebtoonSceneChapterRepository.update(scene.id, {
      images: [...body.images, ...images],
    });

    // this.socketService.emitUpdateChapter(scene.aiWebtoonChapterId);

    return true;
  }

  async removeImage(id: number, body: RemoveImageDto) {
    const scene = await this.getSceneById(id);
    const images = scene.images;

    images.splice(body.index, 1);

    await this.aiWebtoonSceneChapterRepository.update(scene.id, {
      images,
    });

    // this.socketService.emitUpdateChapter(scene.aiWebtoonChapterId);

    return true;
  }
}
