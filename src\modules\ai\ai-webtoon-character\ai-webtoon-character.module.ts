import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { AiApiGenStateManagementModule } from '../ai-api-gen-state-management/ai-api-gen-state-management.module';
import { AiServiceModule } from '../ai-service/ai-service.module';
import { AiWebtoonCharacterRepository } from 'src/database/repositories/ai-webtoon-character.repository';
import { AiWebtoonCharacterController } from './ai-webtoon-character.controller';
import { AiWebtoonCharacterService } from './ai-webtoon-character.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([AiWebtoonCharacterRepository]),
    AiServiceModule,
    AiApiGenStateManagementModule,
  ],
  controllers: [AiWebtoonCharacterController],
  providers: [AiWebtoonCharacterService],
  exports: [AiWebtoonCharacterService],
})
export class AiWebtoonCharacterModule {}
