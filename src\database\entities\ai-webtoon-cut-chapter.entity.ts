import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne } from 'typeorm';

import {
  EAiBaseModelGenImageType,
  EAiCutChapterType,
  EAiFrameImageGenerateType,
  EAiFrameImageSizeType,
  EAiWebtoonCutChapterStatusGenerateImage,
} from 'src/common/ai-webtoon.enum';
import { AiWebtoonChapterEntity } from './ai-webtoon-chapter.entity';
import { AiWebtoonSceneChapterEntity } from './ai-webtoon-scene-chapter.entity';
import { DefaultEntity } from './default.entity';
import {
  IAiItemImageSketchCutChapter,
  IItemImageInpaint,
  IItemImageNormalCutChapter,
} from 'src/common/interfaces/ai-webtoon/ai-webtoon-cut-chapter.entity';

@Entity('ai_webtoon_cut_chapter')
export class AiWebtoonCutChapterEntity extends DefaultEntity {
  @Column({ name: 'ai_webtoon_scene_chapter_id' })
  aiWebtoonSceneChapterId: number;

  @Column({ name: 'ai_webtoon_chapter_id' })
  aiWebtoonChapterId: number;

  @Column({
    type: 'enum',
    enum: EAiCutChapterType,
    default: EAiCutChapterType.NORMAL,
  })
  type: EAiCutChapterType;

  @Column({ type: 'text', nullable: true })
  info?: string;

  @Column({ type: 'text', nullable: true })
  description?: string;

  @Column({ name: 'negative_description', type: 'text', nullable: true })
  negativeDescription?: string;

  @Column({ name: 'general_prompt', type: 'text', nullable: true })
  generalPrompt?: string;

  @Column({ name: 'negative_prompt', type: 'text', nullable: true })
  negativePrompt?: string;

  @Column({ name: 'danbooru_prompt', type: 'text', nullable: true })
  danbooruPrompt?: string;

  @Column({ name: 'is_generating_prompt', default: false })
  isGeneratingPrompt: boolean;

  @Column({ name: 'is_generating_negative_prompt', default: false })
  isGeneratingNegativePrompt: boolean;

  @Column({ name: 'is_generating_danbooru_prompt', default: false })
  isGeneratingDanbooruPrompt: boolean;

  @Column({ name: 'characters', type: 'json', nullable: true })
  characters: string[] | null;

  @Column({ name: 'images', type: 'json', nullable: true })
  images: (IItemImageNormalCutChapter | IItemImageInpaint)[] | null;

  @Column({ name: 'image_index', default: 0 })
  imageIndex: number;

  @Column({ name: 'number_of_images', default: 1 })
  numberOfImages: number;

  @Column({ name: 'total_number_of_images', default: 0 })
  totalNumberOfImages: number;

  @Column({
    name: 'status_generate_image',
    type: 'enum',
    enum: EAiWebtoonCutChapterStatusGenerateImage,
    default: EAiWebtoonCutChapterStatusGenerateImage.NOT_GENERATED,
  })
  statusGenerateImage: EAiWebtoonCutChapterStatusGenerateImage;

  @Column({ name: 'controlnet_canny_images', type: 'json', nullable: true })
  controlNetCannyImages: IAiItemImageSketchCutChapter[] | null;

  @Column({ name: 'controlnet_depth_images', type: 'json', nullable: true })
  controlNetDepthImages: IAiItemImageSketchCutChapter[] | null;

  @Column({ name: 'controlnet_canny_index', type: 'int', default: 0 })
  controlNetCannyIndex: number | null;

  @Column({ name: 'controlnet_depth_index', type: 'int', default: 0 })
  controlNetDepthIndex: number | null;

  @Column({
    name: 'control_net_strength_canny',
    type: 'decimal',
    precision: 5,
    scale: 3,
    default: 0.6,
  })
  controlNetStrengthCanny: number;

  @Column({
    name: 'control_net_strength_depth',
    type: 'decimal',
    precision: 5,
    scale: 3,
    default: 0.6,
  })
  controlNetStrengthDepth: number;

  @Column({ name: 'bg_image', type: 'varchar', nullable: true })
  bgImage?: string | null;

  @Column({ name: 'api_gen_name', type: 'varchar', nullable: true })
  apiGenName?: string | null;

  @Column({
    name: 'image_generate_type',
    type: 'enum',
    enum: EAiFrameImageGenerateType,
    nullable: true,
  })
  imageGenerateType: EAiFrameImageGenerateType | null;

  @Column({ name: 'is_preview', default: false })
  isPreview: boolean;

  @Column({ name: 'order', default: 1 })
  order: number;

  @Column({ name: 'order_by_preview', type: 'int', nullable: true })
  orderByPreview?: number;

  @Column({
    name: 'image_size_type',
    type: 'enum',
    enum: EAiFrameImageSizeType,
    default: EAiFrameImageSizeType.DEFAULT,
  })
  imageSizeType: EAiFrameImageSizeType;

  @Column({ name: 'image_width', default: 688 })
  imageWidth: number;

  @Column({ name: 'image_height', default: 720 })
  imageHeight: number;

  @Column({
    name: 'base_model',
    type: 'enum',
    enum: EAiBaseModelGenImageType,
    default: EAiBaseModelGenImageType.FLUX_NORMAL,
  })
  baseModel: EAiBaseModelGenImageType;

  @Column({ type: 'varchar', nullable: true })
  seed?: string;

  @Column({
    name: 'cfg_scale',
    type: 'decimal',
    precision: 12,
    scale: 6,
    default: 5,
  })
  cfgScale: number;

  @Column({ type: 'int', default: 28 })
  steps: number;

  @ManyToOne(() => AiWebtoonChapterEntity)
  @JoinColumn({ name: 'ai_webtoon_chapter_id' })
  chapter: AiWebtoonChapterEntity;

  @ManyToOne(() => AiWebtoonSceneChapterEntity)
  @JoinColumn({ name: 'ai_webtoon_scene_chapter_id' })
  scene: AiWebtoonSceneChapterEntity;

  orderWaitingNumber?: number | null;
  orderWaitingId?: number | null;
}
