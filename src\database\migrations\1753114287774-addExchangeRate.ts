import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddExchangeRate1753114287774 implements MigrationInterface {
  name = 'AddExchangeRate1753114287774';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`exchange_rates\` (\`id\` int NOT NULL AUTO_INCREMENT, \`date\` date NOT NULL COMMENT 'Exchange rate effective date', \`currency\` enum ('USD', 'EUR', 'JPY', 'GBP', 'CNY', 'KRW') NOT NULL COMMENT 'Source currency code', \`rateToKRW\` decimal(12,4) NOT NULL COMMENT 'Exchange rate to KRW (1 source currency = ? KRW)', \`language\` enum ('en', 'ko', 'ja', 'de', 'zh') NULL COMMENT 'User language for region-specific rates', \`region\` enum ('US', 'KR', 'JP', 'DE', 'CN', 'UK') NULL COMMENT 'User region for region-specific rates', \`source\` varchar(100) NULL COMMENT 'Data source (xe.com, fixer.io, manual, etc.)' DEFAULT 'manual', \`originalRate\` decimal(12,4) NULL COMMENT 'Original rate from API before regional adjustment', \`adjustmentPercentage\` decimal(5,2) NULL COMMENT 'Regional adjustment percentage' DEFAULT '0.00', \`status\` enum ('active', 'inactive', 'expired') NOT NULL COMMENT 'Exchange rate status' DEFAULT 'active', \`metadata\` json NULL COMMENT 'Additional metadata (API response, etc.)', \`createdAt\` datetime(6) NOT NULL COMMENT 'Record creation timestamp' DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL COMMENT 'Record last update timestamp' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), INDEX \`idx_exchange_rates_date\` (\`date\`), UNIQUE INDEX \`idx_exchange_rates_lookup\` (\`date\`, \`currency\`, \`language\`, \`region\`), INDEX \`idx_exchange_rates_date_currency\` (\`date\`, \`currency\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`idx_exchange_rates_date_currency\` ON \`exchange_rates\``,
    );
    await queryRunner.query(
      `DROP INDEX \`idx_exchange_rates_lookup\` ON \`exchange_rates\``,
    );
    await queryRunner.query(
      `DROP INDEX \`idx_exchange_rates_date\` ON \`exchange_rates\``,
    );
    await queryRunner.query(`DROP TABLE \`exchange_rates\``);
  }
}
