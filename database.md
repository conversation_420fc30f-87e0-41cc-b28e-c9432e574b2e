# Database Documentation

This document provides a comprehensive overview of the NestJS Digital Content Platform database schema, covering all entities, relationships, and design patterns.

## Database Overview

The database is designed for a **digital content platform** (webtoon/manga/comic) with subscription-based monetization. It uses **MySQL 8.0** as the primary database with **TypeORM** as the ORM.

### Key Features
- Multi-tenant content management with episodic structure
- Subscription and payment processing system
- Real-time analytics and performance tracking
- Role-based admin panel with permission management
- Comprehensive user activity tracking

## Core Entity Structure

All entities extend the `DefaultEntity` base class which provides:

```typescript
// Default fields for all entities
id: number (Primary Key, Auto-increment)
createdAt: Date (Creation timestamp)
updatedAt: Date (Last update timestamp)  
deletedAt: Date (Soft delete support)
```

## Entity Categories

### 1. User Management

#### Users (`users`)
Main user accounts for the platform.

**Key Fields:**
- `email`: Unique user identifier
- `password`: Encrypted password (excluded from responses)
- `status`: UserStatus (ACTIVE, INACTIVE, etc.)
- `platform`: Platform enum (PC, MOBILE)
- `gender`: Gender enum with privacy controls
- `totalPurchases`: Purchase tracking counter
- `isMatureContent`: Adult content access flag
- `agreeUseInfomation`: GDPR/privacy consent tracking

**Relationships:**
- One-to-Many: `sessions`, `subscriptions`, `payments`, `userContents`, `sanctions`

#### Sessions (`sessions`)
User session management with device tracking.

**Key Fields:**
- `uuid`: Session unique identifier
- `ipAddress`: User's IP address
- `userAgent`, `platform`, `browser`, `os`: Device fingerprinting
- `refreshToken`: JWT refresh token (excluded)
- `refreshTokenExpiresAt`: Token expiration timestamp
- `status`: Session status (ACTIVE/INACTIVE)

**Relationships:**
- Many-to-One: `user`

#### Admins (`admins`)
Administrative user accounts with role-based access.

**Key Fields:**
- `email`, `username`: Admin identifiers (unique constraint)
- `role`: AdminRole enum (ADMIN, SUPER_ADMIN, etc.)
- `status`: AdminStatus for account management
- `forgotPasswordToken`: Password reset functionality

**Relationships:**
- One-to-Many: `menu` (MenuAdminEntity), `sanctions`

### 2. Content Management

#### Content (`content`)
Main content series/comics with metadata.

**Key Fields:**
- `title`: Content series title
- `description`: Series description
- `imagePc`, `imageMobile`: Platform-specific cover images
- `isAdult`: Adult content classification
- `status`: ContentStatus (ACTIVE, DRAFT, PUBLISHED, etc.)
- `viewCount`: Total view counter
- `expectedReleaseDate`: Release scheduling

**Relationships:**
- Many-to-One: `author`, `genre`
- One-to-Many: `episodes`, `contentSettings`, `displays`, `userContents`

#### Episodes (`episode`)
Individual episodes within content series.

**Key Fields:**
- `episodeNumber`: Sequential episode numbering
- `episodeString`: Custom episode labeling
- `title`: Episode title
- `thumbnail`: Episode thumbnail image
- `nonUserViewing`: Guest access permission
- `paymentType`: PaymentType enum (FREE, PAID)
- `paymentUnit`: Currency unit (KRW, USD, etc.)
- `paymentAmount`: Episode pricing
- `bgColor`: Episode background color theme
- `expectedReleaseDate`: Release scheduling

**Relationships:**
- Many-to-One: `content`
- One-to-Many: `images`, `userContents`

#### Episode Images (`episode_image`)
Image management for episodes with ordering.

**Key Fields:**
- `path`: Image file path
- `order`: Display order within episode

**Relationships:**
- Many-to-One: `episode`

#### Authors (`author`)
Content creator management.

**Key Fields:**
- `name`: Author name
- `email`: Contact information
- `bankNumber`: Payment details for revenue sharing
- `memo`: Administrative notes

**Relationships:**
- One-to-Many: `contents`

#### Genres (`genre`)
Content categorization system.

**Key Fields:**
- `name`: Genre name

**Relationships:**
- One-to-Many: `contents`, `displays`

### 3. Settings & Configuration

#### Settings (`settings`)
Platform configuration system with grouping.

**Key Fields:**
- `key`: Setting identifier
- `value`: Setting value
- `group`: Setting category grouping
- `order`: Display ordering
- `status`: Setting status

**Relationships:**
- One-to-Many: `metadata`, `contentSettings`

#### Setting Metadata (`setting_metadata`)
Extended metadata for settings with key-value pairs.

**Key Fields:**
- `key`: Metadata key (unique per setting)
- `value`: Metadata value

**Relationships:**
- Many-to-One: `setting`

#### Content Settings (`content_setting`)
Relationship between content and settings (language, classification, etc.).

**Key Fields:**
- `status`: Relationship status
- `type`: ContentSettingType enum (CLASSIFICATION, LANGUAGE, etc.)

**Relationships:**
- Many-to-One: `content`, `setting`

#### Displays (`displays`)
Content presentation and homepage display management.

**Key Fields:**
- `type`: DisplayType enum (homepage sections, featured, etc.)
- `status`: Display status
- `order`: Display ordering

**Relationships:**
- Many-to-One: `content`, `genre`

### 4. Subscription & Payment System

#### Subscription Plans (`subscription_plans`)
Available subscription tier definitions.

**Key Fields:**
- `name`: Plan name
- `description`: Plan details
- `price`: Regular pricing
- `discountPrice`: Promotional pricing
- `isPopular`: Featured plan flag
- `durationDays`: Subscription duration (default 30 days)
- `currency`: Currency code
- `status`: Plan availability status

**Relationships:**
- One-to-Many: `subscriptions`

#### Subscriptions (`subscriptions`)
User subscription instances with lifecycle management.

**Key Fields:**
- `currency`: Subscription currency
- `status`: Subscription status (ACTIVE, EXPIRED, CANCELLED)
- `startedAt`: Subscription start timestamp (bigint)
- `expiresAt`: Expiration timestamp (bigint)
- `autoRenew`: Automatic renewal flag
- `isTrial`: Trial subscription flag
- `trialDays`: Trial period duration

**Indexes:**
- Composite index on `[expiresAt, status]` for efficient expiration queries
- Index on `user` for user subscription lookups

**Relationships:**
- Many-to-One: `user`, `subscriptionPlan`
- One-to-Many: `payments`

#### Payments (`payments`)
Payment transaction records with provider integration.

**Key Fields:**
- `amount`: Payment amount (decimal 10,2)
- `currency`: Payment currency
- `status`: PaymentStatus (PENDING, COMPLETED, FAILED, etc.)
- `method`: Payment method (card, paypal, etc.)
- `idempotencyKey`: Duplicate prevention
- `providerTransactionId`: External payment provider ID
- `providerTransactionStatus`: Provider-specific status
- `ip`: Payment IP address
- `platform`: Payment platform (PC/MOBILE)

**Indexes:**
- Index on `providerTransactionId` for provider lookups

**Relationships:**
- Many-to-One: `user`, `subscription`

### 5. User Activity & Content Tracking

#### User Content (`user_content`)
User-content interaction tracking (recently read, bookmarks, etc.).

**Key Fields:**
- `type`: UserContentType enum (RECENTLY_READ, BOOKMARK, etc.)
- `status`: UserContentStatus tracking
- `lastUpdateTime`: Last interaction timestamp (bigint)

**Relationships:**
- Many-to-One: `user`, `content`, `episode`

#### Sanctions (`sanction`)
User moderation and penalty system.

**Key Fields:**
- `type`: SanctionType enum (WARNING, SUSPENSION, BAN, etc.)
- `reason`: Sanction reason
- `adminNote`: Administrative notes
- `startDate`, `endDate`: Sanction period

**Relationships:**
- Many-to-One: `user`, `admin`

### 6. Admin Panel & Permissions

#### Menus (`menu`)
Admin panel menu structure with hierarchical organization.

**Key Fields:**
- `key`: Unique menu identifier
- `label`: Display label
- `icon`: Menu icon
- `route`: Navigation route
- `order`: Menu ordering
- `status`: Menu status
- `parentId`: Hierarchical parent menu ID

**Relationships:**
- One-to-Many: `menuAdmin`

#### Menu Admin (`menu_admin`)
Admin permission assignments for menu access.

**Key Fields:**
- `status`: Permission status

**Relationships:**
- Many-to-One: `admin`, `menu`

### 7. Real-time Analytics System

#### Content Analytics (`content_analytics`)
Real-time content consumption analytics with hourly aggregation.

**Key Fields:**
- `date`: Analytics date
- `hour`: Hour of day (0-23)
- `contentId`, `episodeId`: Content references (nullable)
- `viewCount`: Total views in time period
- `uniqueViewers`: Unique user count
- `totalReadTime`: Aggregate reading time (milliseconds)
- `averageReadTime`: Calculated average (decimal 10,2)
- `metadata`: Additional analytics data (JSON)

**Indexes:**
- Unique composite index: `[date, hour, contentId, episodeId]`
- Individual indexes on `date`, `contentId`, `episodeId`

#### User Analytics (`user_analytics`)
Platform user activity analytics with platform segmentation.

**Key Fields:**
- `date`: Analytics date
- `hour`: Hour of day (0-23)
- `platform`: UserPlatform enum (PC, MOBILE)
- `activeUsers`: Active user count
- `newRegistrations`: New user registrations
- `deletedAccounts`: Account deletion count
- `loginCount`: Total login events
- `uniqueLogins`: Unique user logins
- `metadata`: Extended analytics (JSON)

**Indexes:**
- Unique composite index: `[date, hour, platform]`
- Index on `date`

#### Payment Analytics (`payment_analytics`)
Comprehensive payment and revenue analytics with currency conversion.

**Key Fields:**
- `date`: Analytics date
- `hour`: Hour of day (0-23)
- `paymentType`: PaymentType enum (FIRST_PURCHASE, REPURCHASE, SUBSCRIPTION)
- `platform`: UserPlatform enum (PC, MOBILE)
- `currency`: Original payment currency
- `totalRequests`: Payment intent requests
- `successfulPayments`: Completed payments
- `failedPayments`: Failed payment attempts
- `totalAmount`: Revenue in original currency (decimal 15,2)
- `totalAmountKRW`: Revenue in KRW with conversion (decimal 15,2)
- `uniqueUsers`: Unique users involved in payments
- `paymentAttempts`: Total payment page views
- `language`, `region`: Geographic segmentation
- `exchangeRate`: Currency conversion rate (decimal 10,4)
- `exchangeRateDate`: Rate reference date
- `totalRegisteredUsers`: Base user count for rate calculations
- `firstPurchaseRate`, `repurchaseRate`, `totalPurchaseRate`: Conversion rates (decimal 5,2)

**Indexes:**
- Unique composite index: `[date, hour, paymentType, platform, language, region]`
- Index on `date`

### 8. Content Statistics & Caching

#### Content Ranking Stats (`content_ranking_stats`)
Pre-computed content performance statistics for ranking algorithms.

**Key Fields:**
- `contentId`: Content reference
- `title`, `author`, `genre`: Denormalized content data
- `authorId`, `genreId`: Reference IDs
- `type`: ContentType enum (ADULT, GENERAL)
- `series`: SeriesStatus enum (ONGOING, COMPLETED)
- `episodes`: Episode count
- `periodType`: PeriodType enum (DAILY, ALL_TIME)
- `date`: Statistics reference date
- `freeEpisodeRead`, `paidEpisodeRead`, `totalEpisodeRead`: Read counts
- `totalViews`: Aggregate view count
- `uniqueViewers`: Unique viewer count
- `totalReadTime`: Aggregate reading time (bigint)

**Indexes:**
- Unique constraint: `[contentId, periodType, date]`
- Composite index: `[periodType, date, totalEpisodeRead]` for ranking queries
- Index: `[contentId, date]` for content-specific queries

#### Content Statistics Cache (`content_statistics_cache`)
Optimized cache for frequently accessed content statistics.

**Key Fields:**
- `contentId`: Unique content reference
- `title`: Content title
- `releaseDate`: Original release date
- `type`: Content type ('adult' or 'general')
- `episodes`: Total episode count
- `author`, `authorId`: Author information
- `series`: Series status ('ongoing' or 'completed')
- `genre`, `genreId`: Genre information
- `freeEpisodeRead`, `paidEpisodeRead`, `totalEpisodeRead`: Cached read counts (bigint)
- `lastUpdated`: Cache refresh timestamp
- `metadata`: Additional cached data (JSON)

**Indexes:**
- Index on `lastUpdated` for cache management

## Database Relationships Overview

### User-Centric Relationships
- **User** → Sessions (1:N) - Device and login tracking
- **User** → Subscriptions (1:N) - Subscription management
- **User** → Payments (1:N) - Payment history
- **User** → UserContent (1:N) - Content interaction tracking
- **User** → Sanctions (1:N) - Moderation history

### Content Structure Relationships
- **Content** → Episodes (1:N) - Episodic content structure
- **Content** → ContentSettings (1:N) - Multi-language and classification
- **Content** → UserContent (1:N) - User interaction tracking
- **Episode** → EpisodeImages (1:N) - Image management
- **Episode** → UserContent (1:N) - Episode-specific tracking
- **Author** → Content (1:N) - Content authorship
- **Genre** → Content (1:N) - Content categorization

### Business Logic Relationships
- **SubscriptionPlan** → Subscriptions (1:N) - Plan instantiation
- **Subscription** → Payments (1:N) - Payment lifecycle
- **Admin** → MenuAdmin (1:N) - Permission assignments
- **Menu** → MenuAdmin (1:N) - Menu access control

### Settings & Configuration
- **Setting** → SettingMetadata (1:N) - Extended configuration
- **Setting** → ContentSettings (1:N) - Content-specific settings

## Key Design Patterns

### 1. Soft Delete Pattern
All entities extend `DefaultEntity` with `deletedAt` timestamp for soft deletion, preserving data integrity while allowing logical deletion.

### 2. Audit Trail Pattern
Comprehensive tracking with `createdAt` and `updatedAt` timestamps across all entities.

### 3. Status Management Pattern
Consistent status enums across entities for lifecycle management:
- `CommonStatus` (ACTIVE, INACTIVE)
- `UserStatus`, `AdminStatus`, `ContentStatus`, `PaymentStatus`
- `MenuStatus`, `DisplayStatus`, etc.

### 4. Real-time Analytics Pattern
Time-series data with hourly granularity:
- Date + Hour composite indexing
- Unique constraints preventing duplicate aggregations
- JSON metadata for extensible analytics

### 5. Multi-currency Support
- Payment amounts stored with currency codes
- Real-time KRW conversion in analytics
- Exchange rate tracking for historical accuracy

### 6. Platform Segmentation
Consistent platform tracking (PC/MOBILE) across:
- User registration and activity
- Payment analytics
- Content consumption patterns

### 7. Permission-Based Access Control
Menu-driven admin permissions with:
- Hierarchical menu structure
- Admin-menu relationship mapping
- Status-based permission management

## Performance Considerations

### Database Indexes
- **Composite Indexes**: Time-series analytics queries
- **Foreign Key Indexes**: Relationship-based lookups
- **Unique Constraints**: Data integrity enforcement
- **Selective Indexes**: Performance optimization for frequently queried fields

### Query Optimization
- **Date Range Queries**: Efficient time-based filtering
- **Status Filtering**: Active record lookups
- **Platform Segmentation**: Device-specific analytics
- **Currency Conversion**: Real-time rate calculations

### Caching Strategy
- **Content Statistics Cache**: Pre-computed rankings
- **Redis Integration**: Session and queue management
- **BullMQ Analytics**: Asynchronous processing

## Data Flow Examples

### Content Consumption Flow
1. **User** views **Content** → **Episode**
2. **UserContent** record created/updated (RECENTLY_READ)
3. **ContentAnalytics** updated via BullMQ queue
4. **ContentRankingStats** updated for trending calculations

### Payment Flow
1. **User** selects **SubscriptionPlan**
2. **Subscription** created with trial/paid status
3. **Payment** record created with provider integration
4. **PaymentAnalytics** updated with conversion tracking
5. **User.totalPurchases** incremented

### Admin Permission Flow
1. **Admin** logs in with credentials
2. **MenuAdmin** relationships determine accessible **Menus**
3. Permission validation on protected endpoints
4. **Sanction** creation for moderation actions

This database design supports a scalable, multi-tenant digital content platform with comprehensive analytics, flexible subscription management, and robust admin controls.