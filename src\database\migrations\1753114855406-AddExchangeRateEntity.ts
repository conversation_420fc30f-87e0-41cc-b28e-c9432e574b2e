import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddExchangeRateEntity1753114855406 implements MigrationInterface {
  name = 'AddExchangeRateEntity1753114855406';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`exchangeRate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`exchangeRateDate\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`exchangeRateId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD CONSTRAINT \`FK_cc3ccd8f05a918deaf09b0aa951\` FOREIGN KEY (\`exchangeRateId\`) REFERENCES \`exchange_rates\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP FOREIGN KEY \`FK_cc3ccd8f05a918deaf09b0aa951\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` DROP COLUMN \`exchangeRateId\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`exchangeRateDate\` date NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`payment_analytics\` ADD \`exchangeRate\` decimal NULL`,
    );
  }
}
