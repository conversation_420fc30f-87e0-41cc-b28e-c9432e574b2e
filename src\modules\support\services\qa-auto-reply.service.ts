import { Injectable } from '@nestjs/common';
import { SettingRepository } from '../../../database/repositories/setting.repository';
import { SettingMetadataRepository } from '../../../database/repositories/setting-metadat.repository';
import { AdminRepository } from '../../../database/repositories/admin.repository';
import { SupportResponseRepository } from '../../../database/repositories/support-response.repository';
import { SettingGroup } from '../../../common/setting.enum';
import { ResponseStatus } from '../../../database/entities/support-response.entity';
import { UserRepository } from '../../../database/repositories/user.repository';
import { adminNameSystem } from '../../../common/common.config';
import { CommonService } from '../../../common/common.service';
import configurationGlobal from '../../../config/configuration.global';

@Injectable()
export class QAAutoReplyService {
  constructor(
    private settingRepository: SettingRepository,
    private settingMetadataRepository: SettingMetadataRepository,
    private adminRepository: AdminRepository,
    private supportResponseRepository: SupportResponseRepository,
    private userRepository: UserRepository,
    private commonService: CommonService,
  ) {}

  /**
   * Check if auto-reply is enabled and process auto-reply
   */
  async processAutoReply(
    ticketId: number,
    userId: number,
    title: string,
  ): Promise<{ success: boolean; autoReplyId?: number }> {
    try {
      // 1. Get auto-reply configuration
      const config = await this.getAutoReplyConfig();

      if (!config.enabled) {
        return { success: false };
      }

      // 2. Find root admin
      const rootAdmin = await this.adminRepository.findOne({
        where: { username: config.adminUsername },
      });

      if (!rootAdmin) {
        console.error(
          `Auto-reply failed: Root admin '${config.adminUsername}' not found`,
        );
        return { success: false };
      }

      // 3. Create auto-reply response
      const autoReply = await this.supportResponseRepository.save({
        ticketId,
        adminId: rootAdmin.id,
        response: config.content,
        status: ResponseStatus.AUTOMATED_REPLY,
        isInternal: false,
        responseCompletedTime: new Date(),
      });

      // 4. Send email notification to user (if email service is available)
      try {
        const user = await this.userRepository.findOne({
          where: { id: userId },
        });

        if (!user?.email) {
          console.warn(
            `Cannot send auto-reply email: User ${userId} email not found`,
          );
          return { success: true, autoReplyId: autoReply.id };
        }
        await this.commonService.sendEmail(
          user.email,
          'Auto-reply',
          'qa-auto-reply',
          {
            url: `${configurationGlobal().url.user}/customer-service/question-answer/details?id=${ticketId}`,
            urlLogo: '',
            title: 'Re: ' + title,
          },
        );
      } catch (emailError) {
        console.error('Auto-reply email failed:', emailError.message);
        // Continue even if email fails
      }

      return { success: true, autoReplyId: autoReply.id };
    } catch (error) {
      console.error('Auto-reply processing failed:', error.message);
      return { success: false };
    }
  }

  /**
   * Get auto-reply configuration from settings
   */
  async getAutoReplyConfig(): Promise<{
    enabled: boolean;
    content: string;
    delayMinutes: number;
    adminUsername: string;
    emailTemplate: string;
    subjectPrefix: string;
  }> {
    const setting = await this.settingRepository.findOne({
      where: {
        group: SettingGroup.SUPPORT_AUTO_REPLY,
        key: 'qa_auto_reply_global',
      },
      relations: ['metadata'],
    });

    if (!setting || !setting.metadata) {
      return {
        enabled: false,
        content:
          'Thank you for contacting us. We will respond within 24 hours.',
        delayMinutes: 2,
        adminUsername: adminNameSystem,
        emailTemplate: 'qa-auto-reply',
        subjectPrefix: 'Re: ',
      };
    }

    const metadata = setting.metadata.reduce(
      (acc, item) => {
        acc[item.key] = item.value;
        return acc;
      },
      {} as Record<string, string>,
    );

    return {
      enabled: metadata.enabled === 'true',
      content:
        metadata.content ||
        'Thank you for contacting us. We will respond within 24 hours.',
      delayMinutes: parseInt(metadata.delay_minutes || '2', 10),
      adminUsername: metadata.admin_username || 'root',
      emailTemplate: metadata.email_template || 'qa-auto-reply',
      subjectPrefix: metadata.subject_prefix || 'Re: ',
    };
  }

  /**
   * Update auto-reply configuration
   */
  async updateAutoReplyConfig(config: {
    enabled?: boolean;
    content?: string;
    delayMinutes?: number;
    adminUsername?: string;
  }): Promise<void> {
    const setting = await this.settingRepository.findOne({
      where: {
        group: SettingGroup.SUPPORT_AUTO_REPLY,
        key: 'qa_auto_reply_global',
      },
      relations: ['metadata'],
    });

    if (!setting) {
      throw new Error('Auto-reply settings not found');
    }

    // Update metadata
    const updates: Array<{ key: string; value: string }> = [];

    if (config.enabled !== undefined) {
      updates.push({ key: 'enabled', value: config.enabled.toString() });
    }
    if (config.content !== undefined) {
      updates.push({ key: 'content', value: config.content });
    }
    if (config.delayMinutes !== undefined) {
      updates.push({
        key: 'delay_minutes',
        value: config.delayMinutes.toString(),
      });
    }
    if (config.adminUsername !== undefined) {
      updates.push({ key: 'admin_username', value: config.adminUsername });
    }

    for (const update of updates) {
      const metadata = setting.metadata?.find((m) => m.key === update.key);
      if (metadata) {
        metadata.value = update.value;
        await this.settingMetadataRepository.save(metadata);
      }
    }
  }

  /**
   * Send auto-reply email notification
   * This is a placeholder - implement with actual email service
   */
  private async sendAutoReplyEmail(
    userId: number,
    ticketId: number,
    title: string,
  ): Promise<void> {
    // Get user email
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user?.email) {
      console.warn(
        `Cannot send auto-reply email: User ${userId} email not found`,
      );
      return;
    }

    // Mock email sending - replace with actual email service
    console.log('📧 Auto-reply email sent:', {
      to: user.email,
      subject: `Re: ${title}`,
      ticketId,
      template: 'qa-auto-reply',
    });

    // TODO: Integrate with actual email service
    // await this.mailService.sendQAAutoReply({
    //   to: user.email,
    //   subject: `Re: ${title}`,
    //   ticketId,
    //   title,
    // });
  }
}
