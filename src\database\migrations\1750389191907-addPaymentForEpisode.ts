import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentForEpisode1750389191907 implements MigrationInterface {
  name = 'AddPaymentForEpisode1750389191907';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`paymentType\` enum ('free', 'paid') NOT NULL DEFAULT 'free'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`paymentUnit\` enum ('krw') NOT NULL DEFAULT 'krw'`,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` ADD \`paymentAmount\` decimal(10,2) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`episode\` DROP COLUMN \`paymentAmount\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` DROP COLUMN \`paymentUnit\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`episode\` DROP COLUMN \`paymentType\``,
    );
  }
}
