{"name": "new-base-nestjs", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migration:generate": "npm run build && ./node_modules/typeorm/cli.js migration:generate ./src/database/migrations/${npm_config_name} -d ./dist/config/database.config.js", "migration:run": "npm run build &&  ./node_modules/typeorm/cli.js migration:run -d ./dist/config/database.config.js", "migration:run:prod": "./node_modules/typeorm/cli.js migration:run -d ./dist/config/database.config.js", "seed:run": "npm run build && ./node_modules/@jorgebodega/typeorm-seeding/dist/cli.js seed:run -d ./dist/config/database.config.js  ./dist/database/seeders/*.js"}, "dependencies": {"@jorgebodega/typeorm-seeding": "^7.1.0", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/axios": "^4.0.1", "@nestjs/bullmq": "^11.0.2", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "*", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/serve-static": "^5.0.3", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@types/passport-jwt": "^4.0.1", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "bullmq": "^5.56.4", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "ioredis": "^5.6.1", "isomorphic-dompurify": "^2.26.0", "moment": "^2.30.1", "moment-timezone": "^0.6.0", "mysql2": "^3.14.1", "nodemailer": "^7.0.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "sharp": "^0.34.3", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.24"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}