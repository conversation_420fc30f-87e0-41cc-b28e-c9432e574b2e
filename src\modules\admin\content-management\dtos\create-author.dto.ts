import { IsNotEmpty } from 'class-validator';

import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CreateAuthorDto {
  @ApiProperty({
    description: 'Author name',
    example: 'name',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Author email',
    example: 'email',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'Author bank number',
    example: 'bankNumber',
  })
  @IsString()
  @IsNotEmpty()
  bankNumber: string;

  @ApiProperty({
    description: 'Author memo',
    example: 'memo',
  })
  @IsString()
  memo: string;
}
