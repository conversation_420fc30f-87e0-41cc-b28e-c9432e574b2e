import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsString, Matches } from 'class-validator';
import configuration from '../../../config/configuration.global';
import { MESSAGE_CONFIG } from '../../../common/message.config';
export class RegisterDto {
  @ApiProperty({ example: '<EMAIL>', description: 'Email' })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({ example: 'password', description: 'Password' })
  @IsNotEmpty()
  @IsString()
  @Matches(configuration().auth.regex.password, {
    message: MESSAGE_CONFIG.PASSWORD_REGEX.message,
  })
  password: string;
}
