version: '3.8'
services:
  app:
    container_name: trunk_app
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '3005:3005'
    depends_on:
      - db
      - redis
    networks:
      - nest-network
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads
    environment:
      - TZ=Asia/Ho_Chi_Minh
    env_file:
      - .env.dev

  migration:
    container_name: trunk_migration
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      - db
    networks:
      - nest-network
    command: >
      sh -c "
        npm run migration:run:prod"
    restart: 'no'
    environment:
      - TZ=Asia/Ho_Chi_Minh
    env_file:
      - .env.dev

  db:
    container_name: trunk_db
    image: 'mysql:8.0.19'
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_DATABASE}
    ports:
      - '33071:3306'
    volumes:
      - db-store:/var/lib/mysql
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - nest-network

  redis:
    container_name: trunk_redis
    image: redis:latest
    ports:
      - '63799:6379'
    restart: always
    volumes:
      - redis-store:/data
    networks:
      - nest-network
  mailhog:
    image: mailhog/mailhog
    container_name: mailhog
    ports:
      - '1025:1025'
      - '8025:8025'
    networks:
      - nest-network

volumes:
  db-store:
  redis-store:

networks:
  nest-network:
    driver: bridge
