import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsString,
  IsUrl,
  Min,
} from 'class-validator';
import { AiWebtoonSyntheticDataType } from './add-images.dto';

export class AiWebtoonSyntheticReceiveGenerateImageDto {
  @ApiProperty()
  @IsInt()
  @IsNotEmpty()
  @Min(1)
  syntheticDataId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  uuid: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  image: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsBoolean()
  isDone: boolean;

  @ApiProperty({
    enum: AiWebtoonSyntheticDataType,
  })
  @IsEnum(AiWebtoonSyntheticDataType)
  type: AiWebtoonSyntheticDataType;

  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  apiName: string;
}
