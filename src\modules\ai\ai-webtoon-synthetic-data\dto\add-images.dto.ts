import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsEnum, IsUrl } from 'class-validator';

export enum AiWebtoonSyntheticDataType {
  FluxContextImages = 'FluxContextImages',
  BLoraImages = 'BLoraImages',
}

export class AddAiWebtoonSyntheticDataImagesDto {
  @ApiProperty({ type: [String] })
  @IsArray()
  @IsUrl({}, { each: true })
  images: string[];

  @ApiProperty({ enum: AiWebtoonSyntheticDataType })
  @IsEnum(AiWebtoonSyntheticDataType)
  type: AiWebtoonSyntheticDataType;
}
