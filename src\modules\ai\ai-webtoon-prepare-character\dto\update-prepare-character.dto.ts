import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsEnum, IsString } from 'class-validator';
import { EAiWebtoonCharacterGender } from 'src/common/ai-webtoon.enum';

export class UpdateAiWebtoonPrepareCharacterDto {
  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  note: string;

  @ApiProperty()
  @Transform(({ value }: { value: string }) => value?.trim())
  @IsString()
  name: string;

  @ApiProperty()
  @IsEnum(EAiWebtoonCharacterGender)
  gender: EAiWebtoonCharacterGender;
}
