import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsOptional,
  IsUrl,
  ValidateNested,
} from 'class-validator';
import {
  EAiWebtoonStoryBorderType,
  EAiBubbleType,
} from 'src/common/ai-webtoon.enum';
import {
  AiItemDialogueBubblesDto,
  BorderConfigDto,
  EditConfigDto,
  ItemEffectsDto,
  ItemTextDto,
} from '../../ai-webtoon-chapter/dto/common-scene-frame.dto';

export class UpdateEditImageDto {
  @ApiProperty()
  @IsUrl()
  finishedImage: string;

  @ApiProperty()
  @IsUrl()
  editImage: string;

  @ApiProperty({
    required: false,
  })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  frameHeightImageContainer: number;

  @ApiProperty({
    example: [
      {
        characterUuid: 'uuid',
        content: 'content',
        contentJson: 'contentJson',
        coordinate: {
          head: [0.2180232558139535, 0.15384615384615385],
          bubble: [0.024038, 0.0528846],
          mid: [0.14280523255813954, 0.12379807692307693],
        },
        bubbleType: EAiBubbleType.Speech,
        bubbleBorderColor: 'rgba(0, 0, 0, 1)',
        bubbleBackgroundColor: 'rgba(255, 255, 255, 0.9)',
        bubbleWidth: 1,
        bubbleHeight: 2,
        fontFamily: 'Anime Ace',
        fontSize: 12,
        fontStyle: 'normal',
        fontWeight: 'regular',
        bubbleFontColor: 'rgb(71, 34, 3)',
      },
    ],
  })
  @ValidateNested({ each: true })
  @Type(() => AiItemDialogueBubblesDto)
  @IsArray()
  bubbles: AiItemDialogueBubblesDto[];

  @ApiProperty({
    example: [
      {
        image: 'image',
        rotate: 0,
        width: 1,
        height: 2,
        x: 0,
        y: 0,
        scaleX: 1,
        scaleY: 1,
        zIndex: 1,
      },
    ],
  })
  @ValidateNested({ each: true })
  @Type(() => ItemEffectsDto)
  @IsArray()
  effects: ItemEffectsDto[];

  @ApiProperty({
    example: [
      {
        text: 'text',
        textJson: 'textJson',
        scale: 1,
        rotate: 0,
        fontFamily: 'Anime Ace',
        fontSize: 12,
        fontStyle: 'normal',
        fontWeight: 'regular',
        fontColor: 'rgb(0, 0, 0)',
        x: 0,
        y: 0,
        scaleX: 1,
        scaleY: 1,
        zIndex: 1,
      },
    ],
  })
  @ValidateNested({ each: true })
  @Type(() => ItemTextDto)
  @IsArray()
  texts: ItemTextDto[];

  @ApiProperty({
    required: false,
    example: {
      image: 'image',
      left: 0,
      top: 0,
      width: 1,
      height: 1,
      frameWidth: 1,
      frameHeight: 1,
      scaleX: 1,
      scaleY: 1,
      angle: 0,
      cropX: 0,
      cropY: 0,
      cropWidth: 1,
      cropHeight: 1,
      imageCenterPoint: {
        x: 0.5,
        y: 0.5,
      },
    },
  })
  @ValidateNested()
  @Type(() => EditConfigDto)
  @IsOptional()
  editConfig: EditConfigDto;

  @ApiProperty({
    required: false,
    example: {
      borderType: EAiWebtoonStoryBorderType.FULL,
      borderColor: '#000000',
      borderWeight: 2,
    },
  })
  @ValidateNested()
  @Type(() => BorderConfigDto)
  @IsOptional()
  borderConfig: BorderConfigDto;
}
