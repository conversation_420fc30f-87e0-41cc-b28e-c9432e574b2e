import { Column, Entity, ManyToOne } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { DisplayType } from '../../common/common.config';
import { DisplayStatus } from '../../common/status.enum';
import { ContentEntity } from './content.entity';
import { GenreEntity } from './genre.entity';

@Entity('displays')
export class DisplayEntity extends DefaultEntity {
  @Column({ type: 'enum', enum: DisplayType })
  type: DisplayType;

  @Column({ type: 'enum', enum: DisplayStatus })
  status: DisplayStatus;

  @Column({ type: 'int', default: 1 })
  order: number;

  @ManyToOne(() => ContentEntity, (content) => content.displays)
  content: ContentEntity;

  @ManyToOne(() => GenreEntity, (genre) => genre.displays)
  genre: GenreEntity;
}
