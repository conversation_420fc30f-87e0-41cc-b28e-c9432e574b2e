import { IsOptional, IsString, IsEnum, IsNumber } from 'class-validator';
import { SettingStatus } from '../../../../common/status.enum';
import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class FilterContentDto {
  @ApiProperty({
    description: 'Language ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  languageId?: number;

  @ApiProperty({
    description: 'Status',
    example: SettingStatus.ACTIVE,
    enum: SettingStatus,
    required: false,
  })
  @IsOptional()
  @IsEnum(SettingStatus)
  status?: SettingStatus;

  @ApiProperty({
    description: 'Genre ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  genre?: number;

  @ApiProperty({
    description: 'Classification ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  classification?: number;

  @ApiProperty({
    description: 'date ID',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  dateId?: number;

  @ApiProperty({
    description: 'Author payment format',
    example: 'USD',
    required: false,
  })
  @IsOptional()
  @IsString()
  authorPaymentFormat?: string;

  @ApiProperty({
    description: 'Date field filter',
    example: 'createdAt',
    enum: ['createdAt', 'expectedReleaseDate'],
    required: false,
  })
  @IsOptional()
  @IsEnum(['createdAt', 'expectedReleaseDate'])
  dateFeildFilter?: string;

  @ApiProperty({
    description: 'Date field start',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsString()
  dateFeildStart?: string;

  @ApiProperty({
    description: 'Date field end',
    example: '2025-01-01',
    required: false,
  })
  @IsOptional()
  @IsString()
  dateFeildEnd?: string;

  @ApiProperty({
    description: 'Author name search',
    example: 'John Doe',
    required: false,
  })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiProperty({
    description: 'Title search',
    example: 'Title',
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: 'Limit',
    example: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  limit?: number;

  @ApiProperty({
    description: 'Page',
    example: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number;
}
