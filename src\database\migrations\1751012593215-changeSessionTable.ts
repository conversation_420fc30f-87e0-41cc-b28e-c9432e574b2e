import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeSessionTable1751012593215 implements MigrationInterface {
  name = 'ChangeSessionTable1751012593215';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sessions\` ADD \`os\` varchar(255) NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`sessions\` DROP COLUMN \`os\``);
  }
}
