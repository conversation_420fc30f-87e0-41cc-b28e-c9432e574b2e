import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AiLetteringService } from './ai-lettering.service';
import { CreateMultipleAiLetteringDto } from './dto/create-ai-lettering.dto';
import { ListAiLetteringDto } from './dto/list-ai-lettering.dto';
import { UpdateAiLetteringDto } from './dto/update-ai-lettering.dto';
import { DeleteMultipleAiLetteringDto } from './dto/delete-multiple-ai-lettering.dto';
import { JwtAdminAuthGuard } from 'src/modules/admin/auth-admin/guards/jwt-admin-auth.guard';

@ApiTags('ai-lettering')
@Controller('ai-lettering')
@ApiBearerAuth()
@UseGuards(JwtAdminAuthGuard)
export class AiLetteringController {
  constructor(private readonly aiLetteringService: AiLetteringService) {}

  @Post('multiple')
  createMultiple(@Body() body: CreateMultipleAiLetteringDto) {
    return this.aiLetteringService.createMultiple(body);
  }

  @Get()
  list(@Query() dto: ListAiLetteringDto, @Req() req) {
    const timezone = (req.headers.timezone as string) || 'Asia/Bangkok';
    return this.aiLetteringService.list(dto, timezone);
  }

  @Get('/:id')
  detail(@Param('id', ParseIntPipe) id: number) {
    return this.aiLetteringService.detail(id);
  }

  @Put('/:id')
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() body: UpdateAiLetteringDto,
  ) {
    return this.aiLetteringService.update(id, body);
  }

  @Delete()
  deleteMultiple(@Body() body: DeleteMultipleAiLetteringDto) {
    return this.aiLetteringService.deleteMultiple(body.ids);
  }
}
