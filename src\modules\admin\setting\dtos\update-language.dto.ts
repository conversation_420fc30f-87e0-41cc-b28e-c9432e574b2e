import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsOptional, ValidateNested } from 'class-validator';

export class UpdateLanguageDto {
  @ApiProperty({ description: 'Language id', example: '1' })
  @IsOptional()
  id: number;

  @ApiProperty({ description: 'Language name', example: 'English' })
  @IsOptional()
  language: string;

  @ApiProperty({ description: 'Currency', example: 'USD' })
  @IsOptional()
  currency: string;

  @ApiProperty({ description: 'Subscription status', example: 'ON' })
  @IsOptional()
  subscriptionStatus: string;

  @ApiProperty({ description: 'Coins status', example: 'ON' })
  @IsOptional()
  coinsStatus: string;

  @ApiProperty({ description: 'Status of the language', example: 'active' })
  @IsOptional()
  status: string;
}

export class UpdateMultipleLanguageDto {
  @ApiProperty({
    description: 'Languages',
    example: [
      {
        id: 1,
        language: 'English',
        currency: 'USD',
        subscriptionStatus: 'ON',
        coinsStatus: 'ON',
        status: 'active',
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateLanguageDto)
  languages: UpdateLanguageDto[];
}
