import { MigrationInterface, QueryRunner } from 'typeorm';

export class UpdateContentTable1749110462008 implements MigrationInterface {
  name = 'UpdateContentTable1749110462008';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD \`authorId\` int NULL`,
    );
    await queryRunner.query(`ALTER TABLE \`content\` ADD \`genreId\` int NULL`);
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD CONSTRAINT \`FK_93951308013e484eb6ab7888a1b\` FOREIGN KEY (\`authorId\`) REFERENCES \`author\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` ADD CONSTRAINT \`FK_ef067ee3c41ac74d4f4f109d712\` FOREI<PERSON><PERSON> KEY (\`genreId\`) REFERENCES \`genre\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP FOREIGN KEY \`FK_ef067ee3c41ac74d4f4f109d712\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`content\` DROP FOREIGN KEY \`FK_93951308013e484eb6ab7888a1b\``,
    );
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`genreId\``);
    await queryRunner.query(`ALTER TABLE \`content\` DROP COLUMN \`authorId\``);
  }
}
