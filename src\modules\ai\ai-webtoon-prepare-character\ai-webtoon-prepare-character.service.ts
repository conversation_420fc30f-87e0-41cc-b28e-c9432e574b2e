import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { AI_MESSAGE_CONFIG, MESSAGE_CONFIG } from 'src/common/message.config';

// import { HttpService } from '@nestjs/axios';
import * as moment from 'moment-timezone';
// import { firstValueFrom } from 'rxjs';
import {
  EAiDirectionApi,
  EAiWebtoonBasicStatus,
  EAiWebtoonCharacterType,
} from 'src/common/ai-webtoon.enum';
import { isValidTimeZone } from 'src/common/utilities/check.utility';
import { AiWebtoonPrepareCharacterGeneratedRepository } from 'src/database/repositories/ai-webtoon-prepare-character-generated.repository';
import { AiWebtoonPrepareCharacterRepository } from 'src/database/repositories/ai-webtoon-prepare-character.repository';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { Not } from 'typeorm';
import { AiService } from '../ai-service/ai-service.service';
import { AiWebtoonCharacterService } from '../ai-webtoon-character/ai-webtoon-character.service';
import { CreateAiWebtoonPrepareCharacterDto } from './dto/create-prepare-character.dto';
import { GenerateCollectionImagesDto } from './dto/generate-collection-images.dto';
import {
  ListWebtoonAuthorsDto,
  SearchBy,
} from './dto/list-prepare-character.dto';
import { ReceiveCollectionImagesDto } from './dto/receive-collection-images.dto';
import { UpdateAiWebtoonPrepareCharacterDto } from './dto/update-prepare-character.dto';

@Injectable()
export class AiWebtoonPrepareCharacterService {
  constructor(
    private readonly aiWebtoonPrepareCharacterRepository: AiWebtoonPrepareCharacterRepository,
    private readonly aiWebtoonPrepareCharacterGeneratedRepository: AiWebtoonPrepareCharacterGeneratedRepository,
    private readonly aiWebtoonCharacterService: AiWebtoonCharacterService,
    private readonly notificationGoogleService: NotificationGoogleService,
    private readonly aiService: AiService,
  ) {}
  private readonly logger = new Logger(AiWebtoonPrepareCharacterService.name);

  async listAll() {
    const query = this.aiWebtoonPrepareCharacterRepository
      .createQueryBuilder('aiWtPrepareCharacter')
      .select(['aiWtPrepareCharacter.id', 'aiWtPrepareCharacter.name'])
      .orderBy('aiWtPrepareCharacter.id', 'DESC');

    const items = await query.getMany();

    return items;
  }

  async list(dto: ListWebtoonAuthorsDto, timezone: string) {
    if (!isValidTimeZone(timezone)) {
      throw new BadRequestException(MESSAGE_CONFIG.TIMEZONE_INVALID);
    }

    const query = this.aiWebtoonPrepareCharacterRepository
      .createQueryBuilder('aiWtPrepareCharacter')
      .leftJoin('aiWtPrepareCharacter.admin', 'admin')
      .select(['aiWtPrepareCharacter', 'admin.id', 'admin.username']);

    if (dto.search && dto.searchBy) {
      if (dto.searchBy === SearchBy.CHARACTER_NAME) {
        query.andWhere('aiWtPrepareCharacter.name LIKE :name', {
          name: `%${dto.search}%`,
        });
      } else if (dto.searchBy === SearchBy.ADMIN_NAME) {
        query.andWhere('admin.username LIKE :username', {
          username: `%${dto.search}%`,
        });
      }
    }

    if (dto.gender && dto.gender !== 'all') {
      query.andWhere('aiWtPrepareCharacter.gender = :gender', {
        gender: dto.gender,
      });
    }

    if (dto.fromTime) {
      const fromTime = moment(dto.fromTime)
        .tz(timezone)
        .startOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      query.andWhere('aiWtPrepareCharacter.createdAt >= :fromTime', {
        fromTime,
      });
    }
    if (dto.toTime) {
      const toTime = moment(dto.toTime)
        .tz(timezone)
        .endOf('day')
        .utc()
        .format('YYYY-MM-DD HH:mm:ss');

      query.andWhere('aiWtPrepareCharacter.createdAt <= :toTime', {
        toTime,
      });
    }

    const [items, total] = await query
      .orderBy('aiWtPrepareCharacter.id', 'DESC')
      .skip((dto.page - 1) * dto.limit)
      .take(dto.limit)
      .getManyAndCount();

    return {
      data: items,
      page: dto.page,
      limit: dto.limit,
      totalPages: Math.ceil(total / dto.limit),
      total,
    };
  }

  async create(body: CreateAiWebtoonPrepareCharacterDto, adminId: number) {
    const result = await this.aiWebtoonPrepareCharacterRepository.save({
      adminId,
      ...body,
    });
    return { id: result?.id };
  }

  async update(id: number, body: UpdateAiWebtoonPrepareCharacterDto) {
    await this.detail(id);

    await this.aiWebtoonPrepareCharacterRepository.update(id, {
      note: body.note,
      name: body.name,
      gender: body.gender,
    });

    return true;
  }

  async detail(id: number) {
    const item = await this.aiWebtoonPrepareCharacterRepository.findOne({
      where: {
        id,
      },
    });

    if (!item)
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_PREPARE_CHARACTER_NOT_FOUND,
      );

    return item;
  }

  async generateCollectionImages(
    id: number,
    body: GenerateCollectionImagesDto,
    adminId: number,
  ) {
    await this.detail(id);
    if (body.characterId) {
      const character = await this.aiWebtoonCharacterService.detail(
        body.characterId,
      );

      if (character.type !== EAiWebtoonCharacterType.FLUX) {
        throw new BadRequestException(AI_MESSAGE_CONFIG.AI_CHARACTER_NOT_FLUX);
      }
    }

    const aiWebtoonPrepareCharacterGenerated =
      await this.aiWebtoonPrepareCharacterGeneratedRepository.save({
        prompt: body.prompt,
        loraStrength: body.loraStrength,
        numberOfImages: body.numberOfImages,
        aiWebtoonPrepareCharacterId: id,
        aiWebtoonCharacterId: body.characterId || null,
        adminId,
        collectionImages: [],
        cutImages: [],
      });

    try {
      await this.aiService.sendGenerateCollectionImagesRequest({
        aiWebtoonPrepareCharacterGeneratedId:
          aiWebtoonPrepareCharacterGenerated.id,
        characterId: body.characterId || null,
        numberOfImages: body.numberOfImages,
        prompt: body.prompt,
        loraStrength: body.loraStrength,
      });

      await this.aiWebtoonPrepareCharacterRepository.update(id, {
        status: EAiWebtoonBasicStatus.GENERATING,
      });

      return {
        aiWebtoonPrepareCharacterGeneratedId:
          aiWebtoonPrepareCharacterGenerated.id,
      };
    } catch (error) {
      await this.aiWebtoonPrepareCharacterGeneratedRepository.softDelete(
        aiWebtoonPrepareCharacterGenerated.id,
      );

      throw new BadRequestException(error);
    }
  }

  async receiveCollectionImages(body: ReceiveCollectionImagesDto) {
    this.logger.log(
      '🚀 receiveCollectionImages ~ body >> ',
      JSON.stringify(body),
    );

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Collection Images - Generated ID ${body.aiWebtoonPrepareCharacterGeneratedId}`,
        data: body,
        isSuccess: true,
      },
    );

    const aiWebtoonPrepareCharacterGenerated =
      await this.aiWebtoonPrepareCharacterGeneratedRepository.findOne({
        where: {
          id: body.aiWebtoonPrepareCharacterGeneratedId,
        },
      });

    if (!aiWebtoonPrepareCharacterGenerated) {
      throw new NotFoundException(
        AI_MESSAGE_CONFIG.AI_PREPARE_CHARACTER_GENERATED_NOT_FOUND,
      );
    } else {
      const images = [
        body.image,
        ...aiWebtoonPrepareCharacterGenerated.collectionImages,
      ];

      await this.aiWebtoonPrepareCharacterGeneratedRepository.update(
        aiWebtoonPrepareCharacterGenerated.id,
        {
          collectionImages: images,
        },
      );

      if (images.length === aiWebtoonPrepareCharacterGenerated.numberOfImages) {
        const otherIncompleteGenerations =
          await this.aiWebtoonPrepareCharacterGeneratedRepository.find({
            where: {
              aiWebtoonPrepareCharacterId:
                aiWebtoonPrepareCharacterGenerated.aiWebtoonPrepareCharacterId,
              id: Not(aiWebtoonPrepareCharacterGenerated.id),
            },
          });

        const incompleteGenerations = otherIncompleteGenerations.filter(
          (gen) => gen.collectionImages.length < gen.numberOfImages,
        );

        if (incompleteGenerations.length === 0) {
          await this.aiWebtoonPrepareCharacterRepository.update(
            aiWebtoonPrepareCharacterGenerated.aiWebtoonPrepareCharacterId,
            {
              status: EAiWebtoonBasicStatus.GENERATED,
            },
          );
        }
      }
    }

    return true;
  }

  async delete(id: number, adminId: number) {
    const prepareCharacter = await this.detail(id);

    if (prepareCharacter.adminId !== adminId) {
      throw new UnauthorizedException(MESSAGE_CONFIG.ADMIN_NOT_HAVE_PERMISSION);
    }

    await this.aiWebtoonPrepareCharacterGeneratedRepository.softDelete({
      aiWebtoonPrepareCharacterId: id,
    });
    await this.aiWebtoonPrepareCharacterRepository.softDelete(id);

    return true;
  }
}
