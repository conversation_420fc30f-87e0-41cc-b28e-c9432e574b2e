import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { SubscriptionPlanService } from '../admin/payment-management/services/subscription-plan.service';
import { CreatePaymentIntentDto } from './dtos/create-payment-intent.dto';
import { PaymentService } from '../admin/payment-management/services/payment.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AnalyticsQueueService } from '../analytics/services/analytics-queue.service';

@ApiTags('User: Payment')
@Controller('payment')
export class PaymentController {
  constructor(
    private readonly subscriptionPlanService: SubscriptionPlanService,
    private readonly paymentService: PaymentService,
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}
  @Get('payment-plans')
  @ApiOperation({ summary: 'Get payment plan' })
  @ApiResponse({ status: 200, description: 'Get payment plan' })
  getPaymentPlan(@Query('currency') currency: string) {
    return this.subscriptionPlanService.getPaymentPlans(currency);
  }

  @Post('create-payment-intent')
  @ApiOperation({ summary: 'Create payment intent' })
  @ApiResponse({ status: 200, description: 'Create payment intent' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  async createPaymentIntent(
    @Body() body: CreatePaymentIntentDto,
    @Req() req: any,
  ) {
    const result = await this.paymentService.createPaymentIntent(
      body,
      req.user.id,
      req,
    );

    // Track payment analytics
    if (result && req.user?.userId) {
      try {
        // Determine if this is first purchase or repurchase
        const paymentType = await this.determinePaymentType(req.user.id);

        // Get subscription plan to determine amount
        const subscriptionPlan = await this.subscriptionPlanService.findOne(
          body.planId,
        );

        const platform = req.headers['user-agent']
          ?.toLowerCase()
          .includes('mobile')
          ? 'mobile'
          : 'pc';

        const commonPaymentData = {
          userId: req.user.id,
          paymentId: result.id || result.paymentId,
          amount: Number(subscriptionPlan?.price || 0),
          currency: body.currency || 'USD',
          paymentType,
          platform,
          timestamp: Date.now(),
          metadata: {
            userAgent: req.headers['user-agent'],
            subscriptionPlanId: body.planId,
          },
        };

        // 1. Track Payment Attempt (always +1 when user clicks payment)
        await this.analyticsQueueService.trackPaymentAttempt({
          ...commonPaymentData,
          metadata: {
            ...commonPaymentData.metadata,
            source: 'payment_attempt',
          },
        });

        // 2. Track Payment Completion (since we default to success)
        await this.analyticsQueueService.trackPayment({
          ...commonPaymentData,
          status: 'success',
          metadata: {
            ...commonPaymentData.metadata,
            source: 'payment_completion',
          },
        });
      } catch (error) {
        // Don't fail payment if analytics fails
        console.error('Failed to track payment analytics:', error);
      }
    }

    return result;
  }

  private async determinePaymentType(
    userId: number,
  ): Promise<'first_purchase' | 'repurchase'> {
    // Check if user has any successful payments before
    const hasPayments = await this.paymentService.hasSuccessfulPayments(userId);
    return hasPayments ? 'repurchase' : 'first_purchase';
  }
}
