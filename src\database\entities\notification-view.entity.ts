import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  Jo<PERSON><PERSON><PERSON>umn,
  CreateDateColumn,
  Index,
} from 'typeorm';
import { UserEntity } from './user.entity';
import { SystemNotificationEntity } from './system-notification.entity';
import { UserNotificationEntity } from './user-notification.entity';

export enum NotificationType {
  SYSTEM = 'system',
  USER = 'user',
}

@Entity('notification_view')
@Index(['userId', 'notificationType', 'notificationId'], { unique: true })
export class NotificationViewEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ type: 'enum', enum: NotificationType, name: 'notification_type' })
  notificationType: NotificationType;

  @Column({ name: 'notification_id' })
  notificationId: number;

  @ManyToOne(() => UserEntity)
  @JoinColumn({ name: 'user_id' })
  user: UserEntity;

  @ManyToOne(() => SystemNotificationEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'notification_id' })
  systemNotification?: SystemNotificationEntity;

  @ManyToOne(() => UserNotificationEntity, {
    nullable: true,
    createForeignKeyConstraints: false,
  })
  @JoinColumn({ name: 'notification_id' })
  userNotification?: UserNotificationEntity;

  @CreateDateColumn()
  viewedAt: Date;
}
