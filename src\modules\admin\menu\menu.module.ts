import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { MenuController } from './menu.controller';
import { MenuService } from './menu.service';
import { MenuRepository } from '../../../database/repositories/menu.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MenuEntity } from '../../../database/entities/menu.entity';
import { MenuAdminRepository } from '../../../database/repositories/menu-admin.repository';

@Module({
  imports: [TypeOrmModule.forFeature([MenuEntity])],
  controllers: [MenuController],
  providers: [MenuService, MenuRepository, MenuAdminRepository],
  exports: [MenuService],
})
export class MenuModule {}
