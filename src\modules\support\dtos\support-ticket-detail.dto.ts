import { ApiProperty } from '@nestjs/swagger';
import {
  TicketStatus,
  TicketPriority,
  TicketContextData,
} from '../../../database/entities/support-ticket.entity';

export class SupportResponseAdminDto {
  @ApiProperty({ example: 1 })
  id: number;

  @ApiProperty({ example: 'system' })
  username: string;
}

export class SupportResponseDto {
  @ApiProperty({ example: 456 })
  id: number;

  @ApiProperty({
    example:
      'Thank you for contacting Webtoon Support! We have received your support request...',
  })
  response: string;

  @ApiProperty({ example: [], type: [String] })
  attachments: string[];

  @ApiProperty({ enum: ['sent', 'auto_reply', 'draft'] })
  status: string;

  @ApiProperty({ example: '2025-01-22T09:05:00.000Z', nullable: true })
  responseCompletedTime: Date | null;

  @ApiProperty({ example: '2025-01-22T09:05:00.000Z' })
  createdAt: Date;

  @ApiProperty({ type: SupportResponseAdminDto })
  admin: SupportResponseAdminDto;
}

export class SupportTicketCategoryDetailDto {
  @ApiProperty({ example: 1 })
  id: number;

  @ApiProperty({ example: 'content-access' })
  name: string;

  @ApiProperty({ example: 'Content Access Issues' })
  title: string;
}

export class SupportTicketDetailDto {
  @ApiProperty({ example: 123 })
  id: number;

  @ApiProperty({ example: 'SUP-2025-0001' })
  ticketNumber: string;

  @ApiProperty({ example: 'Cannot access premium content' })
  subject: string;

  @ApiProperty({
    example:
      'I have an active subscription but cannot read episode 15 of My Hero Academia',
  })
  description: string;

  @ApiProperty({
    example: ['https://example.com/screenshot1.png'],
    type: [String],
  })
  attachments: string[];

  @ApiProperty({ enum: TicketStatus })
  status: TicketStatus;

  @ApiProperty({ enum: TicketPriority })
  priority: TicketPriority;

  @ApiProperty({ type: SupportTicketCategoryDetailDto, nullable: true })
  category: SupportTicketCategoryDetailDto | null;

  @ApiProperty({
    example: {
      contentId: 123,
      episodeId: 456,
      subscriptionId: 789,
      paymentId: 101,
      deviceInfo: 'iPhone 14 iOS 16.1',
      errorCode: 'CONTENT_LOCKED_ERROR',
      userAgent: 'Mozilla/5.0...',
    },
    nullable: true,
  })
  contextData: TicketContextData | null;

  @ApiProperty({ example: '2025-01-22T09:00:00.000Z' })
  createdAt: Date;

  @ApiProperty({ example: '2025-01-22T10:30:00.000Z' })
  updatedAt: Date;

  @ApiProperty({ type: [SupportResponseDto] })
  responses: SupportResponseDto[];
}
