import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { ACCOUNT_DELETION_CONFIG } from '../../../config/account-deletion.config';

export class DeleteAccountDto {
  @ApiProperty({
    description: 'Current password for verification',
    example: 'currentPassword123',
  })
  @IsString()
  @IsNotEmpty({ message: 'Current password is required' })
  currentPassword: string;

  @ApiProperty({
    description: 'Reason for account deletion (optional)',
    example: 'No longer using the service',
    required: false,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500, { message: 'Reason must not exceed 500 characters' })
  reason?: string;

  @ApiProperty({
    description: 'CAPTCHA token (required when CAPTCHA is enabled)',
    example: 'captcha-token-123',
    required: false,
  })
  @ValidateIf(() => ACCOUNT_DELETION_CONFIG.CAPTCHA_ENABLED)
  @IsString()
  @IsNotEmpty({ message: 'CAPTCHA token is required' })
  captchaToken?: string;

  @ApiProperty({
    description: `Confirmation text (must type "${ACCOUNT_DELETION_CONFIG.REQUIRED_CONFIRM_TEXT}" when enabled)`,
    example: ACCOUNT_DELETION_CONFIG.REQUIRED_CONFIRM_TEXT,
    required: false,
  })
  @ValidateIf(() => ACCOUNT_DELETION_CONFIG.CONFIRM_TEXT_ENABLED)
  @IsString()
  @Equals(ACCOUNT_DELETION_CONFIG.REQUIRED_CONFIRM_TEXT, {
    message: `Please type ${ACCOUNT_DELETION_CONFIG.REQUIRED_CONFIRM_TEXT} to confirm account deletion`,
  })
  confirmText?: string;
}

export class AccountDeletionRequirementsDto {
  @ApiProperty({
    description: 'Whether CAPTCHA is required for account deletion',
    example: false,
  })
  captchaRequired: boolean;

  @ApiProperty({
    description: 'Whether confirmation text is required for account deletion',
    example: false,
  })
  confirmTextRequired: boolean;

  @ApiProperty({
    description:
      'Grace period in days before permanent deletion (0 = immediate)',
    example: 0,
  })
  gracePeriodDays: number;

  @ApiProperty({
    description: 'Rate limiting configuration',
    example: {
      maxAttempts: 3,
      windowMinutes: 60,
    },
  })
  rateLimit: {
    maxAttempts: number;
    windowMinutes: number;
  };
}

export class AccountDeletionResponseDto {
  @ApiProperty({
    description: 'Indicates if deletion was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response message',
    example: 'Account deletion initiated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Deletion data',
    example: {
      deletionId: 'uuid-deletion-id',
      gracePeriodUntil: null,
      dataRetentionPeriod: 30,
    },
  })
  data: {
    deletionId: string;
    gracePeriodUntil?: Date;
    dataRetentionPeriod: number;
  };
}
