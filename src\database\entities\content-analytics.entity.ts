import { Entity, Column, Index } from 'typeorm';
import { DefaultEntity } from './default.entity';

@Entity('content_analytics')
@Index(['date', 'hour', 'contentId', 'episodeId'], { unique: true })
export class ContentAnalyticsEntity extends DefaultEntity {
  @Column({ type: 'date' })
  @Index()
  date: Date;

  @Column({ type: 'tinyint', default: 0 })
  hour: number; // 0-23

  @Column({ type: 'int', nullable: true })
  @Index()
  contentId: number | null;

  @Column({ type: 'int', nullable: true })
  @Index()
  episodeId: number | null;

  @Column({ type: 'int', default: 0 })
  viewCount: number;

  @Column({ type: 'int', default: 0 })
  uniqueViewers: number;

  @Column({ type: 'bigint', default: 0 })
  totalReadTime: number; // milliseconds

  @Column({ type: 'decimal', precision: 10, scale: 2, default: 0 })
  averageReadTime: number;

  @Column({ type: 'json', nullable: true })
  metadata: Record<string, any>;
}
