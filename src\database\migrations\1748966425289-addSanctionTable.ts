import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSanctionTable1748966425289 implements MigrationInterface {
  name = 'AddSanctionTable1748966425289';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`sanction\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`type\` enum ('permanent', 'period') NOT NULL, \`reason\` varchar(255) NOT NULL, \`adminNote\` varchar(255) NOT NULL, \`startDate\` datetime NULL, \`endDate\` datetime NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD \`sanctionId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD UNIQUE INDEX \`IDX_38a664c1a40c384e79aa19fc71\` (\`sanctionId\`)`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_38a664c1a40c384e79aa19fc71\` ON \`users\` (\`sanctionId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` ADD CONSTRAINT \`FK_38a664c1a40c384e79aa19fc71d\` FOREIGN KEY (\`sanctionId\`) REFERENCES \`sanction\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP FOREIGN KEY \`FK_38a664c1a40c384e79aa19fc71d\``,
    );
    await queryRunner.query(
      `DROP INDEX \`REL_38a664c1a40c384e79aa19fc71\` ON \`users\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`users\` DROP INDEX \`IDX_38a664c1a40c384e79aa19fc71\``,
    );
    await queryRunner.query(`ALTER TABLE \`users\` DROP COLUMN \`sanctionId\``);
    await queryRunner.query(`DROP TABLE \`sanction\``);
  }
}
