import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { AiWebtoonSyntheticDataType } from './add-images.dto';

export class DeleteAiWebtoonSyntheticDataImageDto {
  @ApiProperty({ enum: AiWebtoonSyntheticDataType })
  @IsEnum(AiWebtoonSyntheticDataType)
  type: AiWebtoonSyntheticDataType;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  uuid: string;
}