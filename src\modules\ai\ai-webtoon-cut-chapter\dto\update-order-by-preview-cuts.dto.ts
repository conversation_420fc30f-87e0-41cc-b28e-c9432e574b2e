import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsInt,
  IsNotEmpty,
  Min,
  ValidateNested,
} from 'class-validator';

class UpdateOrderByPreviewCutItemDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  cutId: number;

  @ApiProperty()
  @IsNotEmpty()
  @IsInt()
  @Min(1)
  orderByPreview: number;
}

export class UpdateOrderByPreviewCutsDto {
  @ApiProperty({ type: [UpdateOrderByPreviewCutItemDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateOrderByPreviewCutItemDto)
  data: UpdateOrderByPreviewCutItemDto[];
}
