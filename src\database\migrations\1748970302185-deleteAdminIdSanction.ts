import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeleteAdminIdSanction1748970302185 implements MigrationInterface {
  name = 'DeleteAdminIdSanction1748970302185';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sanction\` DROP FOREIGN KEY \`FK_9fd33f14c5f0eb084160ff5757e\``,
    );
    await queryRunner.query(
      `DROP INDEX \`REL_9fd33f14c5f0eb084160ff5757\` ON \`sanction\``,
    );
    await queryRunner.query(`ALTER TABLE \`sanction\` DROP COLUMN \`adminId\``);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD \`adminId\` int NULL`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`REL_9fd33f14c5f0eb084160ff5757\` ON \`sanction\` (\`adminId\`)`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD CONSTRAINT \`FK_9fd33f14c5f0eb084160ff5757e\` FOREIGN KEY (\`adminId\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
