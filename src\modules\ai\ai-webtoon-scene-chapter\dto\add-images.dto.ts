import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ImageDto {
  @ApiProperty({
    description: 'Image URL',
    example: 'image1.jpg',
  })
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'Image width',
    example: 800,
  })
  @IsNotEmpty()
  width: number;

  @ApiProperty({
    description: 'Image height',
    example: 600,
  })
  @IsNotEmpty()
  height: number;
}

export class AddImagesDto {
  @ApiProperty({
    description: 'Images with their dimensions',
    type: [ImageDto],
    example: [
      { url: 'image1.jpg', width: 800, height: 600 },
      { url: 'image2.jpg', width: 1024, height: 768 },
    ],
  })
  @IsNotEmpty()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImageDto)
  images: ImageDto[];
}

export class RemoveImageDto {
  @ApiProperty({
    description: 'Image index',
    example: 0,
  })
  @IsNotEmpty()
  index: number;
}
