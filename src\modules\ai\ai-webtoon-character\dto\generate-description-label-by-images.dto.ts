import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString, IsUrl } from 'class-validator';
import { MaxArraySize } from 'src/common/decorators/max-array-size';

export class GenerateDescriptionLabelByImagesDto {
  @ApiProperty()
  @IsArray()
  @IsUrl({}, { each: true })
  @MaxArraySize(5)
  images: string[];

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  prompt?: string;
}
