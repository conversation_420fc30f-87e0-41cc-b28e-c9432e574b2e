import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangeAdminTable1750836978405 implements MigrationInterface {
  name = 'ChangeAdminTable1750836978405';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE UNIQUE INDEX \`IDX_7afc22b5ac0f832d471dbb12d1\` ON \`admins\` (\`username\`, \`email\`, \`deletedAt\`)`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `DROP INDEX \`IDX_7afc22b5ac0f832d471dbb12d1\` ON \`admins\``,
    );
  }
}
