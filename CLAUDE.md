# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

# NestJS Digital Content Platform

## Project Summary

This is a **digital content platform** (webtoon/manga/comic platform) built with NestJS featuring subscription-based monetization, content management, and payment processing.

## Main Features and Modules

### User-Facing Features:
- **User Authentication & Registration** - JWT-based auth with email verification and password reset
- **Content Consumption** - Browse and view episodic content (comics/webtoons)
- **Subscription Management** - Multiple subscription plans with trial periods and auto-renewal
- **Payment Processing** - Integrated payment system for premium content and subscriptions
- **User Profiles** - Profile management with preferences and viewing history
- **FAQ System** - <PERSON><PERSON><PERSON> frequently asked questions by category with view tracking
- **Legal Documents** - Access Terms of Use and Privacy Policy with view analytics

### Admin Features:
- **Admin Dashboard** - Separate admin authentication and role-based access control
- **Content Management** - Create and manage content, episodes, authors, and genres
- **User Administration** - User management, sanctions, and moderation
- **Payment Management** - Subscription plan management and payment oversight
- **Content Statistics** - Analytics and reporting for content consumption patterns
- **Display Management** - Content display and presentation settings
- **Settings Management** - Platform configuration and metadata management
- **FAQ Management** - Create, update, and manage frequently asked questions with category support
- **Legal Document Management** - Manage Terms of Use and Privacy Policy with HTML content support and analytics

## Technology Stack

### Backend Framework:
- **NestJS** (v11.0.1) - Node.js framework with TypeScript
- **TypeORM** (v0.3.24) - Database ORM with migration support
- **MySQL** (v8.0) - Primary database
- **Redis** - Caching and session management

### Authentication & Security:
- **JWT** (@nestjs/jwt) - Token-based authentication
- **Passport** - Authentication strategies
- **bcrypt** - Password hashing

### Additional Technologies:
- **Swagger/OpenAPI** - API documentation
- **Class Validator/Transformer** - DTO validation and transformation
- **Nodemailer** - Email functionality with Handlebars templates
- **Docker** - Containerization with development and production configs
- **BullMQ** - Queue processing for real-time analytics and background tasks

## Database Structure

### Core Entities:
- **Users** - User accounts with subscription tracking and payment history
- **Content** - Main content items (series/comics) with metadata
- **Episodes** - Individual episodes with payment models and view tracking
- **Authors & Genres** - Content categorization and authorship
- **Subscription Plans** - Tiered pricing with trial periods and discounts
- **Subscriptions** - User subscription instances with auto-renewal
- **Payments** - Payment transactions with provider integration
- **Sessions** - User session management
- **Admin & Menu Management** - Admin interface and permissions
- **Analytics Tables** - Real-time analytics data (content_analytics, user_analytics, payment_analytics)
- **Legal Documents** - Terms of Use and Privacy Policy with HTML content and version management
- **FAQ** - Frequently asked questions with categorization, status management, and view tracking

### Key Relationships:
- Users can have multiple subscriptions and payments
- Content contains multiple episodes with different payment models
- Episodes can be free, paid per episode, or subscription-based
- Comprehensive user content tracking and viewing history