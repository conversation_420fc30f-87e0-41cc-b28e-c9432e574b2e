import { Seeder } from '@jorgebodega/typeorm-seeding';
import { DataSource } from 'typeorm';
import { SettingEntity } from '../entities/setting.entity';
import { SettingGroup } from '../../common/setting.enum';
import { SettingMetadata } from '../entities/setting_metadata.entity';
import { SettingStatus } from '../../common/status.enum';

export class SettingSeeder extends Seeder {
  private readonly settings = [
    {
      key: 'daily',
      value: 'Daily',
      group: SettingGroup.CONTENT_CLASSIFICATION,
      order: 1,
    },
    {
      key: 'new',
      value: 'New',
      group: SettingGroup.CONTENT_CLASSIFICATION,
      order: 2,
    },
    {
      key: 'popular',
      value: 'Popular',
      group: SettingGroup.CONTENT_CLASSIFICATION,
      order: 3,
    },
    {
      key: 'completed',
      value: 'Completed',
      group: SettingGroup.CONTENT_CLASSIFICATION,
      order: 4,
    },
    {
      key: 'date_mon',
      value: 'Mon',
      group: SettingGroup.CONTENT_DATE,
      order: 1,
    },
    {
      key: 'date_tue',
      value: 'Tue',
      group: SettingGroup.CONTENT_DATE,
      order: 2,
    },
    {
      key: 'date_wed',
      value: 'Wed',
      group: SettingGroup.CONTENT_DATE,
      order: 3,
    },
    {
      key: 'date_thu',
      value: 'Thu',
      group: SettingGroup.CONTENT_DATE,
      order: 4,
    },
    {
      key: 'date_fri',
      value: 'Fri',
      group: SettingGroup.CONTENT_DATE,
      order: 5,
    },
    {
      key: 'date_sat',
      value: 'Sat',
      group: SettingGroup.CONTENT_DATE,
      order: 6,
    },
    {
      key: 'date_sun',
      value: 'Sun',
      group: SettingGroup.CONTENT_DATE,
      order: 7,
    },
  ];

  async run(datasource: DataSource): Promise<void> {
    const repository = datasource.getRepository(SettingEntity);

    for (const setting of this.settings) {
      const existingSetting = await repository.findOne({
        where: { key: setting.key },
      });

      if (existingSetting) {
        console.log(`Setting ${setting.key} already exists`);
        continue;
      }

      await repository.save(setting);
    }

    const settingRepository = datasource.getRepository(SettingEntity);
    const metadataRepository = datasource.getRepository(SettingMetadata);

    // Check if support auto-reply settings already exist
    const existingSettings = await settingRepository.findOne({
      where: { group: SettingGroup.SUPPORT_AUTO_REPLY },
    });

    if (existingSettings) {
      console.log('Support auto-reply settings already seeded');
      return;
    }

    try {
      // Create main auto-reply setting
      const autoReplySetting = settingRepository.create({
        key: 'qa_auto_reply_global',
        value: 'Global Q&A Auto Reply Configuration',
        group: SettingGroup.SUPPORT_AUTO_REPLY,
        order: 1,
        status: SettingStatus.ACTIVE,
      });

      const savedSetting = await settingRepository.save(autoReplySetting);

      // Create metadata for the auto-reply setting
      const metadataItems = [
        {
          key: 'enabled',
          value: 'true', // Enable auto-reply by default
          setting: savedSetting,
        },
        {
          key: 'content',
          value:
            'Thank you for contacting us. We have received your inquiry and will respond within 24 hours. For urgent matters, please include "URGENT" in your subject line.',
          setting: savedSetting,
        },
        {
          key: 'delay_minutes',
          value: '2', // Send auto-reply after 2 minutes
          setting: savedSetting,
        },
        {
          key: 'admin_username',
          value: 'admin', // Root admin for auto-replies
          setting: savedSetting,
        },
        {
          key: 'email_template',
          value: 'qa-auto-reply', // Email template name
          setting: savedSetting,
        },
        {
          key: 'subject_prefix',
          value: 'Re: ', // Auto-reply email subject prefix
          setting: savedSetting,
        },
      ];

      const metadata = metadataRepository.create(metadataItems);
      await metadataRepository.save(metadata);

      console.log('✅ Successfully seeded support auto-reply settings');
      console.log(`  - Main setting: ${savedSetting.key}`);
      console.log(`  - Metadata items: ${metadata.length}`);
    } catch (error) {
      console.error(
        '❌ Error seeding support auto-reply settings:',
        error.message,
      );
      throw error;
    }

    console.log('Settings created successfully');
  }
}
