import { Module } from '@nestjs/common';
import { UserAdminController } from './user-admin.controller';
import { UserService } from 'src/modules/user/user.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserEntity } from 'src/database/entities/user.entity';
import { SanctionEntity } from 'src/database/entities/sanction.entity';
import { SanctionRepository } from 'src/database/repositories/sanction.repository';
import { SessionRepository } from '../../../database/repositories/session.repository';
import { SessionEntity } from '../../../database/entities/session.entity';
import { PaymentEntity } from '../../../database/entities/payment.entity';
import { PaymentRepository } from '../../../database/repositories/payment.repository';
import { AccountDeletionAuditEntity } from '../../../database/entities/account-deletion-audit.entity';
import { RateLimitService } from '../../user/services/rate-limit.service';
import { AnalyticsQueueModule } from '../../analytics/analytics-queue.module';
import { AccountDeletionAuditRepository } from '../../../database/repositories/account-deletion-audit.repository';
@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      SanctionEntity,
      SessionEntity,
      PaymentEntity,
      AccountDeletionAuditEntity,
    ]),
    AnalyticsQueueModule,
  ],
  controllers: [UserAdminController],
  providers: [
    UserService,
    SanctionRepository,
    SessionRepository,
    PaymentRepository,
    RateLimitService,
    AccountDeletionAuditRepository,
  ],
})
export class UserAdminModule {}
