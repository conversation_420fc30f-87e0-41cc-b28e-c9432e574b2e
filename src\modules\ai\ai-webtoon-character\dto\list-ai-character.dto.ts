import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import {
  EAiWebtoonCharacterGender,
  EAiWebtoonCharacterStatus,
  EAiWebtoonCharacterType,
  FILTER_ALL,
} from 'src/common/ai-webtoon.enum';
import { PaginationDto } from 'src/modules/notification/dtos/pagination.dto';

export const FILTER_AI_WEBTOON_CHARACTER_STATUS = {
  ...FILTER_ALL,
  ...EAiWebtoonCharacterStatus,
};

export const FILTER_AI_WEBTOON_CHARACTER_GENDER = {
  ...FILTER_ALL,
  ...EAiWebtoonCharacterGender,
};

export class ListAiWebtoonCharacterDto extends PaginationDto {
  @ApiProperty({ enum: EAiWebtoonCharacterType })
  @IsEnum(EAiWebtoonCharacterType)
  type: EAiWebtoonCharacterType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name: string;

  @ApiProperty({
    enum: FILTER_AI_WEBTOON_CHARACTER_STATUS,
    required: false,
  })
  @IsOptional()
  @IsEnum(FILTER_AI_WEBTOON_CHARACTER_STATUS)
  status: string;

  @ApiProperty({
    enum: FILTER_AI_WEBTOON_CHARACTER_GENDER,
    required: false,
  })
  @IsOptional()
  @IsEnum(FILTER_AI_WEBTOON_CHARACTER_GENDER)
  gender: string;
}

export class ListAllAiWebtoonCharacterDto {
  @ApiProperty({ enum: EAiWebtoonCharacterType })
  @IsEnum(EAiWebtoonCharacterType)
  type: EAiWebtoonCharacterType;
}
