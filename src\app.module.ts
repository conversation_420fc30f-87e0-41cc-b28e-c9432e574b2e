import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './modules/auth/auth.module';
import { UserModule } from './modules/user/user.module';
import { AuthAdminModule } from './modules/admin/auth-admin/auth-admin.module';
import { AdminModule } from './modules/admin/admin/admin.module';
import { MenuModule } from './modules/admin/menu/menu.module';
import { SettingModule as AdminSettingModule } from './modules/admin/setting/setting.module';
import { SettingModule as UserSettingModule } from './modules/setting/setting.module';
import { CommonModule } from './common/common.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import { UserAdminModule } from './modules/admin/user-admin/user-admin.module';
import { ContentManagementModule } from './modules/admin/content-management/content-management.module';
import { DisplayManagementModule } from './modules/admin/display-management/display-management.module';
import { ContentModule } from './modules/content/content.module';
import { PaymentModule } from './modules/payment/payment.module';
import { PaymentManagementModule } from './modules/admin/payment-management/payment-management.module';
import { ContentStatsModule } from './modules/admin/content-stats/content-stats.module';
import { AnalyticsQueueModule } from './modules/analytics/analytics-queue.module';
import { ContentRankingModule } from './modules/admin/content-ranking/content-ranking.module';
import { UserAnalyticsModule } from './modules/admin/user-analytics/user-analytics.module';
import { NotificationModule } from './modules/notification/notification.module';
import { AdminNotificationModule } from './modules/admin/notification/notification.module';
import configuration from './config/configuration.global';
import { SupportModule } from './modules/support/support.module';
import { SupportAdminModule } from './modules/admin/support-admin/support-admin.module';
import { FaqModule } from './modules/faq/faq.module';
import { FaqAdminModule } from './modules/admin/faq-admin/faq-admin.module';
import { LegalAdminModule } from './modules/admin/legal-admin/legal-admin.module';
import { LegalDocumentModule } from './modules/legal-document/legal-document.module';
import { UploadModule } from './modules/upload/upload.module';
import { AiApiGenStateManagementModule } from './modules/ai/ai-api-gen-state-management/ai-api-gen-state-management.module';
import { AiLetteringLanguageModule } from './modules/ai/ai-lettering-language/ai-lettering-language.module';
import { AiLetteringTypeModule } from './modules/ai/ai-lettering-type/ai-lettering-type.module';
import { AiLetteringModule } from './modules/ai/ai-lettering/ai-lettering.module';
import { AiServiceModule } from './modules/ai/ai-service/ai-service.module';
import { AiWebtoonChapterModule } from './modules/ai/ai-webtoon-chapter/ai-webtoon-chapter.module';
import { AiWebtoonCharacterModule } from './modules/ai/ai-webtoon-character/ai-webtoon-character.module';
import { AiWebtoonCutChapterModule } from './modules/ai/ai-webtoon-cut-chapter/ai-webtoon-cut-chapter.module';
import { AiWebtoonPrepareCharacterGeneratedModule } from './modules/ai/ai-webtoon-prepare-character-generated/ai-webtoon-prepare-character-generated.module';
import { AiWebtoonPrepareCharacterModule } from './modules/ai/ai-webtoon-prepare-character/ai-webtoon-prepare-character.module';
import { AiWebtoonSceneChapterModule } from './modules/ai/ai-webtoon-scene-chapter/ai-webtoon-scene-chapter.module';
import { AiWebtoonStoryModule } from './modules/ai/ai-webtoon-story/ai-webtoon-story.module';
import { AiWebtoonSyntheticDataModule } from './modules/ai/ai-webtoon-synthetic-data/ai-webtoon-synthetic-data.module';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    DatabaseModule,
    AuthModule,
    UserModule,
    AdminModule,
    AuthAdminModule,
    MenuModule,
    AdminSettingModule,
    UserSettingModule,
    CommonModule,
    ServeStaticModule.forRoot({
      rootPath: join(__dirname, '..', 'uploads/'),
      serveRoot: '/uploads/',
    }),
    UserAdminModule,
    ContentManagementModule,
    DisplayManagementModule,
    ContentModule,
    PaymentModule,
    PaymentManagementModule,
    ContentStatsModule,
    AnalyticsQueueModule,
    ContentRankingModule,
    UserAnalyticsModule,
    NotificationModule,
    AdminNotificationModule,
    SupportModule,
    SupportAdminModule,
    FaqModule,
    FaqAdminModule,
    LegalAdminModule,
    LegalDocumentModule,
    UploadModule,
    AiApiGenStateManagementModule,
    AiLetteringLanguageModule,
    AiLetteringTypeModule,
    AiLetteringModule,
    AiServiceModule,
    AiWebtoonChapterModule,
    AiWebtoonCharacterModule,
    AiWebtoonCutChapterModule,
    AiWebtoonPrepareCharacterGeneratedModule,
    AiWebtoonPrepareCharacterModule,
    AiWebtoonSceneChapterModule,
    AiWebtoonStoryModule,
    AiWebtoonSyntheticDataModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
