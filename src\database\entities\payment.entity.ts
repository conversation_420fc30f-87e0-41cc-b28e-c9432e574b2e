import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { DefaultEntity } from './default.entity';
import { PaymentStatus } from '../../common/status.enum';
import { SubscriptionEntity } from './subscription.entity';
import { UserEntity } from './user.entity';
import { Platform } from '../../common/common.config';

@Entity('payments')
@Index(['providerTransactionId'])
export class PaymentEntity extends DefaultEntity {
  @Column({ type: 'decimal', precision: 10, scale: 2 })
  amount: number;

  @Column({ type: 'varchar', length: 255 })
  currency: string;

  @Column({ type: 'enum', enum: PaymentStatus, default: PaymentStatus.PENDING })
  status: PaymentStatus;

  @Column({ type: 'varchar', length: 255, nullable: true })
  method: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  idempotencyKey: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  providerTransactionId: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  providerTransactionStatus: string;

  @ManyToOne(() => SubscriptionEntity, (subscription) => subscription.payments)
  subscription: SubscriptionEntity;

  @Column({ type: 'varchar', length: 255, nullable: true })
  ip: string;

  @Column({ type: 'enum', enum: Platform, nullable: true })
  platform: Platform;

  @ManyToOne(() => UserEntity, (user) => user.payments)
  user: UserEntity;
}
