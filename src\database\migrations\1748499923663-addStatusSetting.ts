import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddStatusSetting1748499923663 implements MigrationInterface {
  name = 'AddStatusSetting1748499923663';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`settings\` ADD \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE \`settings\` DROP COLUMN \`status\``);
  }
}
