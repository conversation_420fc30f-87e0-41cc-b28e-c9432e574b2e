import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonPrepareCharacterGeneratedEntity } from '../entities/ai-webtoon-prepare-character-generated.entity';

@Injectable()
export class AiWebtoonPrepareCharacterGeneratedRepository extends Repository<AiWebtoonPrepareCharacterGeneratedEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonPrepareCharacterGeneratedEntity, dataSource.createEntityManager());
  }
}