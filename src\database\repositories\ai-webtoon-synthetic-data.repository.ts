import { DataSource, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { AiWebtoonSyntheticDataEntity } from '../entities/ai-webtoon-synthetic-data.entity';

@Injectable()
export class AiWebtoonSyntheticDataRepository extends Repository<AiWebtoonSyntheticDataEntity> {
  constructor(private dataSource: DataSource) {
    super(AiWebtoonSyntheticDataEntity, dataSource.createEntityManager());
  }
}
