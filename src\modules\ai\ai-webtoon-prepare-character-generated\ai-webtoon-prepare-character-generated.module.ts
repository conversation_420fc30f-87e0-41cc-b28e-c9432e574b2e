import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotificationGoogleModule } from 'src/modules/notification-google/notification-google.module';
import { AiWebtoonCharacterRepository } from 'src/database/repositories/ai-webtoon-character.repository';
import { AiWebtoonPrepareCharacterRepository } from 'src/database/repositories/ai-webtoon-prepare-character.repository';
import { AiWebtoonPrepareCharacterGeneratedRepository } from 'src/database/repositories/ai-webtoon-prepare-character-generated.repository';
import { AiWebtoonPrepareCharacterGeneratedController } from './ai-webtoon-prepare-character-generated.controller';
import { AiWebtoonPrepareCharacterGeneratedService } from './ai-webtoon-prepare-character-generated.service';
import { AiServiceModule } from '../ai-service/ai-service.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AiWebtoonPrepareCharacterRepository,
      AiWebtoonCharacterRepository,
      AiWebtoonPrepareCharacterGeneratedRepository,
    ]),
    AiServiceModule,
    NotificationGoogleModule,
  ],
  controllers: [AiWebtoonPrepareCharacterGeneratedController],
  providers: [AiWebtoonPrepareCharacterGeneratedService],
  exports: [AiWebtoonPrepareCharacterGeneratedService],
})
export class AiWebtoonPrepareCharacterGeneratedModule {}
