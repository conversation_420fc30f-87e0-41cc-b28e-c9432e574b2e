import { MigrationInterface, QueryRunner } from 'typeorm';

export class ChangePaymentTable1752565816084 implements MigrationInterface {
  name = 'ChangePaymentTable1752565816084';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payments\` ADD \`platform\` enum ('pc', 'mobile') NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`payments\` DROP COLUMN \`platform\``,
    );
  }
}
