import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { EAiDirectionApi } from 'src/common/ai-webtoon.enum';
import { MESSAGE_CONFIG } from 'src/common/message.config';
import { AiWebtoonPrepareCharacterGeneratedRepository } from 'src/database/repositories/ai-webtoon-prepare-character-generated.repository';
import { NotificationGoogleService } from 'src/modules/notification-google/notification-google.service';
import { AiService } from '../ai-service/ai-service.service';
import { GenerateCutImagesDto } from './dto/generate-cut-images.dto';
import { ReceiveCutImagesDto } from './dto/receive-cut-images.dto';
import { UpdatePrepareCharacterGeneratedDto } from './dto/update.dto';

@Injectable()
export class AiWebtoonPrepareCharacterGeneratedService {
  constructor(
    private readonly aiWebtoonPrepareCharacterGeneratedRepository: AiWebtoonPrepareCharacterGeneratedRepository,
    private readonly aiService: AiService,
    private readonly notificationGoogleService: NotificationGoogleService,
  ) {}

  private readonly logger = new Logger(
    AiWebtoonPrepareCharacterGeneratedService.name,
  );

  async listByPrepareCharacter(prepareCharacterId: number) {
    const items = await this.aiWebtoonPrepareCharacterGeneratedRepository
      .createQueryBuilder('characterGenerated')
      .select([
        'characterGenerated',
        'aiWebtoonCharacter',
        'admin.username',
        'admin.id',
      ])
      .where(
        'characterGenerated.aiWebtoonPrepareCharacterId = :prepareCharacterId',
        { prepareCharacterId },
      )
      .leftJoin('characterGenerated.aiWebtoonCharacter', 'aiWebtoonCharacter')
      .leftJoin('characterGenerated.admin', 'admin')
      .orderBy('characterGenerated.id', 'DESC')
      .getMany();

    return items;
  }

  async detail(id: number) {
    const item =
      await this.aiWebtoonPrepareCharacterGeneratedRepository.findOne({
        where: { id },
      });

    if (!item) {
      throw new NotFoundException(MESSAGE_CONFIG.ITEM_NOT_FOUND);
    }

    return item;
  }

  async update(id: number, body: UpdatePrepareCharacterGeneratedDto) {
    await this.detail(id);

    if (body.collectionImages.length === 0 && body.cutImages.length === 0) {
      await this.aiWebtoonPrepareCharacterGeneratedRepository.softDelete(id);
      return true;
    }

    await this.aiWebtoonPrepareCharacterGeneratedRepository.update(id, {
      collectionImages: body.collectionImages,
      cutImages: body.cutImages,
    });

    return true;
  }

  async generateCutImages(id: number, body: GenerateCutImagesDto) {
    const item = await this.detail(id);

    const data = {
      image: body.collectionImage,
      aiWebtoonPrepareCharacterGeneratedId: item.id,
    };

    this.logger.debug('🚀 generateCutImages ~ data >> ', JSON.stringify(data));

    await this.aiService.sendGenerateCutImagesByPrepareCharacterRequest(data);

    return true;
  }

  async receiveCutImages(body: ReceiveCutImagesDto) {
    this.logger.log('🚀 receiveCutImages ~ body >> ', JSON.stringify(body));

    await this.notificationGoogleService.sendLogNotificationToGoogleChatDataWorkingWithAI(
      {
        directionApi: EAiDirectionApi.RECEIVED,
        event: `Receive Cut Images - Generated ID ${body.aiWebtoonPrepareCharacterGeneratedId}`,
        data: body,
        isSuccess: true,
      },
    );

    const item = await this.detail(body.aiWebtoonPrepareCharacterGeneratedId);

    await this.aiWebtoonPrepareCharacterGeneratedRepository.update(
      body.aiWebtoonPrepareCharacterGeneratedId,
      {
        cutImages: item.cutImages
          ? [...body.images, ...item.cutImages]
          : [...body.images],
      },
    );

    return true;
  }
}
