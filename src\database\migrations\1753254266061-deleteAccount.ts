import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeleteAccount1753254266061 implements MigrationInterface {
  name = 'DeleteAccount1753254266061';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`account_deletion_audit\` (\`id\` int NOT NULL AUTO_INCREMENT, \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, \`user_id\` int NOT NULL, \`deletion_id\` varchar(36) NOT NULL, \`reason\` text NULL, \`ip_address\` varchar(45) NULL, \`user_agent\` text NULL, \`deletion_status\` enum ('INITIATED', 'COMPLETED', 'CANCELLED') NOT NULL DEFAULT 'INITIATED', \`scheduled_deletion_at\` datetime NULL, \`completed_at\` datetime NULL, INDEX \`IDX_1dcd08aef6bf635bc3342ffb4a\` (\`createdAt\`), INDEX \`IDX_3524aa3e2d3b43c240281e79ba\` (\`deletion_id\`), INDEX \`IDX_4d14b6c3aee84e49c7d1a17eba\` (\`user_id\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`account_deletion_audit\` ADD CONSTRAINT \`FK_4d14b6c3aee84e49c7d1a17ebad\` FOREIGN KEY (\`user_id\`) REFERENCES \`users\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`account_deletion_audit\` DROP FOREIGN KEY \`FK_4d14b6c3aee84e49c7d1a17ebad\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_4d14b6c3aee84e49c7d1a17eba\` ON \`account_deletion_audit\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_3524aa3e2d3b43c240281e79ba\` ON \`account_deletion_audit\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_1dcd08aef6bf635bc3342ffb4a\` ON \`account_deletion_audit\``,
    );
    await queryRunner.query(`DROP TABLE \`account_deletion_audit\``);
  }
}
