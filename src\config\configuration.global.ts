import { config } from 'dotenv';
config();

export default () => ({
  auth: {
    forgotPassword: {
      expiresIn: process.env.FORGOT_PASSWORD_EXPIRES_IN_HOURS,
    },
    refreshToken: {
      expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN,
      expiresUnit: process.env.REFRESH_TOKEN_EXPIRES_UNIT,
    },
    regex: {
      password: /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[a-zA-Z]).{8,}$/,
    },
  },
  jwt: {
    secret: process.env.JWT_SECRET,
    expiresIn: process.env.JWT_EXPIRES_IN,
  },
  mail: {
    host: process.env.MAIL_HOST,
    port: process.env.MAIL_PORT,
    user: process.env.MAIL_USER,
    password: process.env.MAIL_PASSWORD,
    from: process.env.MAIL_FROM,
  },
  url: {
    admin: process.env.ADMIN_APP_URL,
    user: process.env.USER_APP_URL,
    image: process.env.IMAGE_URL || 'http://localhost:3005',
  },
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0', 10),
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
  },
});
