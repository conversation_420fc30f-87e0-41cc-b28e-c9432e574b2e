import { Injectable } from '@nestjs/common';
import { readFileSync } from 'fs';
import { join } from 'path';
import * as handlebars from 'handlebars';

export interface SupportTicketEmailData {
  ticketId: number;
  ticketNumber: string;
  subject: string;
  category?: string;
  priority: string;
  userEmail: string;
  description: string;
  contextData?: any;
  createdAt: string;
  clientDomain?: string;
}

export interface SupportAutoReplyEmailData {
  ticketId: number;
  ticketNumber: string;
  subject: string;
  userEmail: string;
  autoReplyContent: string;
  clientDomain?: string;
}

export interface SupportResponseEmailData {
  ticketId: number;
  ticketNumber: string;
  subject: string;
  userEmail: string;
  responseContent: string;
  adminUsername: string;
  responseDate: string;
  responseId: number;
  attachments?: string[];
  clientDomain?: string;
}

export interface SupportTicketClosedEmailData {
  ticketId: number;
  ticketNumber: string;
  subject: string;
  userEmail: string;
  adminUsername: string;
  closedDate: string;
  resolutionNotes?: string;
  totalTime: string;
  responseCount: number;
  closureId: number;
  clientDomain?: string;
}

export interface SupportAdminNotificationEmailData {
  ticketId: number;
  ticketNumber: string;
  subject: string;
  category: string;
  priority: string;
  userEmail: string;
  description: string;
  contextData?: any;
  createdAt: string;
  adminDomain?: string;
}

@Injectable()
export class SupportEmailService {
  private templates: Map<string, handlebars.TemplateDelegate> = new Map();

  constructor() {
    this.loadTemplates();
    this.registerHelpers();
  }

  private loadTemplates() {
    const templateDir = join(__dirname, '../templates');
    const templateFiles = [
      'support-ticket-created.hbs',
      'support-auto-reply.hbs',
      'support-admin-notification.hbs',
      'support-response.hbs',
      'support-ticket-closed.hbs',
    ];

    templateFiles.forEach((filename) => {
      try {
        const templatePath = join(templateDir, filename);
        const templateSource = readFileSync(templatePath, 'utf-8');
        const template = handlebars.compile(templateSource);
        const templateName = filename.replace('.hbs', '');
        this.templates.set(templateName, template);
        console.log(`✅ Loaded email template: ${templateName}`);
      } catch (error) {
        console.error(`❌ Failed to load template ${filename}:`, error.message);
      }
    });
  }

  private registerHelpers() {
    // Handlebars helper for equality comparison
    handlebars.registerHelper('eq', (a, b) => a === b);

    // Helper for current year
    handlebars.registerHelper('year', () => new Date().getFullYear());

    // Helper for formatting dates
    handlebars.registerHelper('formatDate', (date) => {
      if (!date) return '';
      return new Date(date).toLocaleString();
    });

    // Helper for priority color
    handlebars.registerHelper('priorityColor', (priority) => {
      const colors = {
        urgent: '#dc3545',
        high: '#fd7e14',
        medium: '#ffc107',
        low: '#28a745',
      };
      return colors[priority] || '#6c757d';
    });
  }

  generateTicketCreatedEmail(data: SupportTicketEmailData): string {
    const template = this.templates.get('support-ticket-created');
    if (!template) {
      throw new Error('Ticket created template not found');
    }

    return template({
      ...data,
      year: new Date().getFullYear(),
      clientDomain:
        data.clientDomain || process.env.CLIENT_DOMAIN || 'https://webtoon.com',
    });
  }

  generateAutoReplyEmail(data: SupportAutoReplyEmailData): string {
    const template = this.templates.get('support-auto-reply');
    if (!template) {
      throw new Error('Auto-reply template not found');
    }

    return template({
      ...data,
      year: new Date().getFullYear(),
      clientDomain:
        data.clientDomain || process.env.CLIENT_DOMAIN || 'https://webtoon.com',
    });
  }

  generateAdminNotificationEmail(
    data: SupportAdminNotificationEmailData,
  ): string {
    const template = this.templates.get('support-admin-notification');
    if (!template) {
      throw new Error('Admin notification template not found');
    }

    return template({
      ...data,
      year: new Date().getFullYear(),
      timestamp: new Date().toISOString(),
      adminDomain:
        data.adminDomain ||
        process.env.ADMIN_DOMAIN ||
        'https://admin.webtoon.com',
    });
  }

  generateResponseEmail(data: SupportResponseEmailData): string {
    const template = this.templates.get('support-response');
    if (!template) {
      throw new Error('Response template not found');
    }

    return template({
      ...data,
      year: new Date().getFullYear(),
      clientDomain:
        data.clientDomain || process.env.CLIENT_DOMAIN || 'https://webtoon.com',
    });
  }

  generateTicketClosedEmail(data: SupportTicketClosedEmailData): string {
    const template = this.templates.get('support-ticket-closed');
    if (!template) {
      throw new Error('Ticket closed template not found');
    }

    return template({
      ...data,
      year: new Date().getFullYear(),
      clientDomain:
        data.clientDomain || process.env.CLIENT_DOMAIN || 'https://webtoon.com',
    });
  }

  // Email sending methods (to be integrated with actual mail service)
  sendTicketCreatedEmail(data: SupportTicketEmailData): boolean {
    try {
      this.generateTicketCreatedEmail(data);

      // TODO: Integrate with actual mail service (NodeMailer, SendGrid, etc.)
      console.log('📧 Sending ticket created email to:', data.userEmail);
      console.log('Subject:', `Support Ticket Created - ${data.ticketNumber}`);

      return true;
    } catch (error) {
      console.error('Failed to send ticket created email:', error.message);
      return false;
    }
  }

  sendAutoReplyEmail(data: SupportAutoReplyEmailData): boolean {
    try {
      this.generateAutoReplyEmail(data);

      console.log('🤖 Sending auto-reply email to:', data.userEmail);
      console.log('Subject:', `Auto-Reply: ${data.ticketNumber}`);

      // TODO: Replace with actual email sending
      return true;
    } catch (error) {
      console.error('Failed to send auto-reply email:', error.message);
      return false;
    }
  }

  sendAdminNotificationEmail(
    data: SupportAdminNotificationEmailData,
    adminEmails: string[],
  ): boolean {
    try {
      this.generateAdminNotificationEmail(data);

      console.log(
        '🚨 Sending admin notification email to:',
        adminEmails.join(', '),
      );
      console.log('Subject:', `New Support Ticket - ${data.ticketNumber}`);

      // TODO: Send to multiple admin emails
      return true;
    } catch (error) {
      console.error('Failed to send admin notification email:', error.message);
      return false;
    }
  }

  sendResponseEmail(data: SupportResponseEmailData): boolean {
    try {
      this.generateResponseEmail(data);

      console.log('💬 Sending response email to:', data.userEmail);
      console.log('Subject:', `Support Response - ${data.ticketNumber}`);

      // TODO: Replace with actual email sending
      return true;
    } catch (error) {
      console.error('Failed to send response email:', error.message);
      return false;
    }
  }

  sendTicketClosedEmail(data: SupportTicketClosedEmailData): boolean {
    try {
      this.generateTicketClosedEmail(data);

      console.log('✅ Sending ticket closed email to:', data.userEmail);
      console.log('Subject:', `Ticket Resolved - ${data.ticketNumber}`);

      // TODO: Replace with actual email sending
      return true;
    } catch (error) {
      console.error('Failed to send ticket closed email:', error.message);
      return false;
    }
  }
}
