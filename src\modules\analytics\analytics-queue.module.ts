import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bullmq';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { ContentAnalyticsEntity } from '../../database/entities/content-analytics.entity';
import { UserAnalyticsEntity } from '../../database/entities/user-analytics.entity';
import { PaymentAnalyticsEntity } from '../../database/entities/payment-analytics.entity';
import { ExchangeRateEntity } from '../../database/entities/exchange-rate.entity';
import { UserEntity } from '../../database/entities/user.entity';
import { PaymentEntity } from '../../database/entities/payment.entity';
import { AnalyticsQueueService } from './services/analytics-queue.service';
import { ExchangeRateService } from './services/exchange-rate.service';
import { ExchangeRateDbService } from './services/exchange-rate-db.service';
import { PurchaseRateService } from './services/purchase-rate.service';
import { ExchangeRateJob } from './jobs/exchange-rate.job';
import { ContentAnalyticsProcessor } from './processors/content-analytics.processor';
import { UserAnalyticsProcessor } from './processors/user-analytics.processor';
import { PaymentAnalyticsProcessor } from './processors/payment-analytics.processor';
import { ContentStatisticsProcessor } from './processors/content-statistics.processor';
import { ANALYTICS_QUEUES } from './constants/analytics-queues.constant';
import { ContentStatisticsService } from './services/content-statistics.service';
import { ContentStatisticsSchedulerService } from './services/content-statistics-scheduler.service';
import { ContentStatisticsCacheEntity } from '../../database/entities/content-statistics-cache.entity';
import { ContentRankingStatsEntity } from '../../database/entities/content-ranking-stats.entity';
import { ContentSettingEntity } from '../../database/entities/content_setting.entity';
import { AuthorEntity } from '../../database/entities/author.entity';
import { GenreEntity } from '../../database/entities/genre.entity';
import { ContentEntity } from '../../database/entities/content.entity';
import { EpisodeEntity } from '../../database/entities/episode.entity';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    TypeOrmModule.forFeature([
      ContentAnalyticsEntity,
      UserAnalyticsEntity,
      PaymentAnalyticsEntity,
      PaymentAnalyticsEntity,
      ExchangeRateEntity,
      UserEntity,
      PaymentEntity,
      ContentStatisticsCacheEntity,
      ContentRankingStatsEntity,
      ContentSettingEntity,
      AuthorEntity,
      GenreEntity,
      ContentEntity,
      EpisodeEntity,
    ]),
    BullModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        connection: {
          host: configService.get('redis.host'),
          port: configService.get('redis.port'),
          password: configService.get('redis.password'),
          db: configService.get('redis.db'),
          retryDelayOnFailover: configService.get('redis.retryDelayOnFailover'),
          enableReadyCheck: configService.get('redis.enableReadyCheck'),
          maxRetriesPerRequest: configService.get('redis.maxRetriesPerRequest'),
        },
        defaultJobOptions: {
          removeOnComplete: 100,
          removeOnFail: 50,
          attempts: 3,
          backoff: {
            type: 'exponential',
            delay: 2000,
          },
        },
      }),
      inject: [ConfigService],
    }),
    BullModule.registerQueue(
      { name: ANALYTICS_QUEUES.CONTENT_ANALYTICS },
      { name: ANALYTICS_QUEUES.USER_ANALYTICS },
      { name: ANALYTICS_QUEUES.PAYMENT_ANALYTICS },
      { name: 'content-statistics' },
    ),
  ],
  providers: [
    AnalyticsQueueService,
    ExchangeRateService,
    ExchangeRateDbService,
    ExchangeRateJob,
    PurchaseRateService,
    ContentAnalyticsProcessor,
    UserAnalyticsProcessor,
    PaymentAnalyticsProcessor,
    ContentStatisticsProcessor,
    ContentStatisticsService,
    ContentStatisticsSchedulerService,
  ],
  exports: [
    AnalyticsQueueService,
    ExchangeRateDbService,
    ExchangeRateJob,
    ContentStatisticsService,
    ContentStatisticsSchedulerService,
  ],
})
export class AnalyticsQueueModule {}
