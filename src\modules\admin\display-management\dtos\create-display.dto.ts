import {
  IsArray,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  Validate<PERSON>ested,
} from 'class-validator';
import { DisplayType } from '../../../../common/common.config';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class CreateDisplayDto {
  @ApiProperty({
    description: 'The content id',
    required: true,
    example: 1,
  })
  @IsNumber()
  @Type(() => Number)
  @IsNotEmpty()
  contentId: number;

  @ApiProperty({
    description: 'The order of the display',
    default: 1,
    required: false,
  })
  @IsNumber()
  @Type(() => Number)
  @IsNotEmpty()
  order: number;
}

export class CreateDisplayMultipleDto {
  @ApiProperty({
    description: 'The display list',
    type: [CreateDisplayDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateDisplayDto)
  displays: CreateDisplayDto[];

  @ApiProperty({
    enum: DisplayType,
    description: 'The type of the display',
    example: DisplayType.MAIN_BANNER,
  })
  @Type(() => String)
  @IsEnum(DisplayType)
  @IsNotEmpty()
  type: DisplayType;
}
