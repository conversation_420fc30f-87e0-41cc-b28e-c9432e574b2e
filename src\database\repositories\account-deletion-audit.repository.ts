import { Injectable } from '@nestjs/common';
import { Repository, DataSource } from 'typeorm';
import { AccountDeletionAuditEntity } from '../entities/account-deletion-audit.entity';

@Injectable()
export class AccountDeletionAuditRepository extends Repository<AccountDeletionAuditEntity> {
  constructor(dataSource: DataSource) {
    super(AccountDeletionAuditEntity, dataSource.createEntityManager());
  }

  async createAuditRecord(data: {
    userId: number;
    deletionId: string;
    reason?: string;
    ipAddress?: string;
    userAgent?: string;
    scheduledDeletionAt?: Date;
  }): Promise<AccountDeletionAuditEntity> {
    const auditRecord = this.create({
      userId: data.userId,
      deletionId: data.deletionId,
      reason: data.reason,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
      deletionStatus: 'INITIATED',
      scheduledDeletionAt: data.scheduledDeletionAt,
    });

    return await this.save(auditRecord);
  }

  async markAsCompleted(deletionId: string): Promise<void> {
    await this.update(
      { deletionId },
      {
        deletionStatus: 'COMPLETED',
        completedAt: new Date(),
      },
    );
  }

  async markAsCancelled(deletionId: string): Promise<void> {
    await this.update(
      { deletionId },
      {
        deletionStatus: 'CANCELLED',
      },
    );
  }

  async findByDeletionId(
    deletionId: string,
  ): Promise<AccountDeletionAuditEntity | null> {
    return await this.findOne({
      where: { deletionId },
      relations: ['user'],
    });
  }

  async findPendingDeletions(): Promise<AccountDeletionAuditEntity[]> {
    return await this.createQueryBuilder('audit')
      .leftJoinAndSelect('audit.user', 'user')
      .where('audit.deletionStatus = :status', { status: 'INITIATED' })
      .andWhere('audit.scheduledDeletionAt IS NOT NULL')
      .andWhere('audit.scheduledDeletionAt <= :now', { now: new Date() })
      .getMany();
  }
}
