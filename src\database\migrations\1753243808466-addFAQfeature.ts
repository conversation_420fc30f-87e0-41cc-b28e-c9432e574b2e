import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddFAQfeature1753243808466 implements MigrationInterface {
  name = 'AddFAQfeature1753243808466';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE \`faq\` (\`id\` int NOT NULL AUTO_INCREMENT, \`admin_id\` int NOT NULL, \`title\` varchar(255) NOT NULL, \`category_id\` int NULL, \`status\` enum ('active', 'inactive') NOT NULL DEFAULT 'active', \`content\` text NOT NULL, \`view_count\` int NOT NULL DEFAULT '0', \`order\` int NOT NULL DEFAULT '1', \`createdAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deletedAt\` datetime(6) NULL, INDEX \`IDX_75831e6d3b0b8addbce3f525f6\` (\`title\`), INDEX \`IDX_5f5d4596b2c77087c2ae109b3b\` (\`createdAt\`), INDEX \`IDX_5c7bc8b7d86a105cc2bb72337c\` (\`status\`, \`category_id\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB`,
    );
    await queryRunner.query(
      `ALTER TABLE \`faq\` ADD CONSTRAINT \`FK_b8e7788acfac975252dec719ce2\` FOREIGN KEY (\`admin_id\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE \`faq\` ADD CONSTRAINT \`FK_3f3f88890b5286bf53f9e3fb6b8\` FOREIGN KEY (\`category_id\`) REFERENCES \`support_category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`faq\` DROP FOREIGN KEY \`FK_3f3f88890b5286bf53f9e3fb6b8\``,
    );
    await queryRunner.query(
      `ALTER TABLE \`faq\` DROP FOREIGN KEY \`FK_b8e7788acfac975252dec719ce2\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_5c7bc8b7d86a105cc2bb72337c\` ON \`faq\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_5f5d4596b2c77087c2ae109b3b\` ON \`faq\``,
    );
    await queryRunner.query(
      `DROP INDEX \`IDX_75831e6d3b0b8addbce3f525f6\` ON \`faq\``,
    );
    await queryRunner.query(`DROP TABLE \`faq\``);
  }
}
