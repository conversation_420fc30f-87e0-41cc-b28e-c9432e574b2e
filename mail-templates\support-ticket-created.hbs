<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Ticket Created - {{ticketNumber}}</title>
</head>
<body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="margin: 0; color: #2c3e50;">🎫 Support Ticket Created</h2>
        </div>
        
        <p>Hello,</p>
        
        <p>Your support ticket has been successfully created and submitted to our Webtoon Support Team.</p>
        
        <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #27ae60;">Ticket Details:</h3>
            <p><strong>Ticket Number:</strong> {{ticketNumber}}</p>
            <p><strong>Subject:</strong> {{subject}}</p>
            <p><strong>Category:</strong> {{category}}</p>
            <p><strong>Priority:</strong> {{priority}}</p>
            <p><strong>Created:</strong> {{createdAt}}</p>
        </div>
        
        <div style="background: #fff3cd; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin: 0;"><strong>📋 Description:</strong></p>
            <p style="margin: 10px 0 0 0;">{{description}}</p>
        </div>
        
        {{#if contextData}}
        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin-top: 0;"><strong>📱 Context Information:</strong></p>
            {{#if contextData.contentId}}<p>• Content ID: {{contextData.contentId}}</p>{{/if}}
            {{#if contextData.episodeId}}<p>• Episode ID: {{contextData.episodeId}}</p>{{/if}}
            {{#if contextData.subscriptionId}}<p>• Subscription ID: {{contextData.subscriptionId}}</p>{{/if}}
            {{#if contextData.deviceInfo}}<p>• Device: {{contextData.deviceInfo}}</p>{{/if}}
            {{#if contextData.errorCode}}<p>• Error Code: {{contextData.errorCode}}</p>{{/if}}
        </div>
        {{/if}}
        
        <div style="background: #d1ecf1; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin-top: 0;"><strong>⏱️ What happens next?</strong></p>
            <ul style="margin: 10px 0 0 20px;">
                <li>Our support team will review your ticket within 24-48 hours</li>
                <li>You'll receive email notifications for all responses</li>
                <li>You can check your ticket status anytime in your account</li>
                <li>We'll work to resolve your issue as quickly as possible</li>
            </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{clientDomain}}/support/tickets/{{ticketId}}" 
               style="background: #007bff; color: white; text-decoration: none; padding: 12px 24px; border-radius: 6px; display: inline-block;">
                View Ticket Details
            </a>
        </div>
        
        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
        
        <div style="font-size: 14px; color: #666;">
            <p><strong>Need immediate help?</strong></p>
            <p>For urgent issues, please check our <a href="{{clientDomain}}/help">Help Center</a> or contact us directly.</p>
            <p>This email was sent to {{userEmail}}. Please do not reply to this email.</p>
        </div>
        
        <div style="text-align: center; margin-top: 30px; color: #999; font-size: 12px;">
            <p>© {{year}} Webtoon Platform. All rights reserved.</p>
        </div>
    </div>
</body>
</html>