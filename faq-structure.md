# FAQ System Implementation - Complete Structure & Plan

## 📋 Overview

Based on the analysis of `faq-require.md`, `structure.md`, and `CLAUDE.md`, this document provides a comprehensive implementation plan for the FAQ system in the NestJS Digital Content Platform. The FAQ system will integrate seamlessly with the existing support system and follow the established architectural patterns.

## 🎯 Requirements Analysis

### Admin Requirements:
- **Create FAQ**: Save FAQ with admin creator, title, category, status, HTML content, view count
- **Update FAQ**: Modify existing FAQ entries
- **Delete FAQ**: Soft delete functionality
- **List FAQ**: Filter by date range, category, search by title with pagination

### User Requirements:
- **List FAQ**: View active FAQs with category filtering
- **View FAQ Detail**: Get specific FAQ and increment view count

## 🏗️ System Architecture

### 1. Integration with Existing Systems

The FAQ system will integrate with:
- **Support Category System**: Reuse existing `support-category.entity.ts`
- **Admin Authentication**: Use existing JWT admin auth with menu permissions
- **User Authentication**: Optional JWT auth for view tracking
- **Analytics System**: Track FAQ views via BullMQ analytics queue
- **Notification System**: HTML sanitization using DOMPurify

### 2. Module Structure

```
src/modules/
├── admin/
│   └── faq-admin/                    # New admin FAQ module
│       ├── controllers/
│       │   └── faq-admin.controller.ts
│       ├── services/
│       │   └── faq-admin.service.ts
│       ├── dtos/
│       │   ├── create-faq.dto.ts
│       │   ├── update-faq.dto.ts
│       │   ├── faq-list-query.dto.ts
│       │   └── delete-faq.dto.ts
│       └── faq-admin.module.ts
└── faq/                              # New user FAQ module
    ├── controllers/
    │   └── faq.controller.ts
    ├── services/
    │   └── faq.service.ts
    ├── dtos/
    │   ├── faq-list-query.dto.ts
    │   └── faq-detail.dto.ts
    └── faq.module.ts
```

## 🗄️ Database Design

### FAQ Entity

```typescript
// src/database/entities/faq.entity.ts
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  DeleteDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { AdminEntity } from './admin.entity';
import { SupportCategoryEntity } from './support-category.entity';

export enum FaqStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('faq')
@Index(['status', 'categoryId'])
@Index(['createdAt'])
@Index(['title'])
export class FaqEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'admin_id' })
  adminId: number;

  @Column()
  title: string;

  @Column({ name: 'category_id', nullable: true })
  categoryId: number;

  @Column({
    type: 'enum',
    enum: FaqStatus,
    default: FaqStatus.ACTIVE,
  })
  status: FaqStatus;

  @Column({ type: 'text' })
  content: string; // HTML content

  @Column({ name: 'view_count', default: 0 })
  viewCount: number;

  @Column({ default: 1 })
  order: number; // Display order

  // Relations
  @ManyToOne(() => AdminEntity)
  @JoinColumn({ name: 'admin_id' })
  admin: AdminEntity;

  @ManyToOne(() => SupportCategoryEntity)
  @JoinColumn({ name: 'category_id' })
  category: SupportCategoryEntity;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @DeleteDateColumn()
  deletedAt: Date;
}
```

### Migration Script

```typescript
// src/database/migrations/[timestamp]-createFaqTable.ts
import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreateFaqTable[timestamp] implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'faq',
        columns: [
          {
            name: 'id',
            type: 'int',
            isPrimary: true,
            isGenerated: true,
            generationStrategy: 'increment',
          },
          {
            name: 'admin_id',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'title',
            type: 'varchar',
            length: '255',
            isNullable: false,
          },
          {
            name: 'category_id',
            type: 'int',
            isNullable: true,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['active', 'inactive'],
            default: "'active'",
            isNullable: false,
          },
          {
            name: 'content',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'view_count',
            type: 'int',
            default: 0,
            isNullable: false,
          },
          {
            name: 'order',
            type: 'int',
            default: 1,
            isNullable: false,
          },
          {
            name: 'createdAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'updatedAt',
            type: 'datetime',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
            isNullable: false,
          },
          {
            name: 'deletedAt',
            type: 'datetime',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Create indexes
    await queryRunner.createIndex('faq', new Index('IDX_FAQ_STATUS_CATEGORY', ['status', 'category_id']));
    await queryRunner.createIndex('faq', new Index('IDX_FAQ_CREATED_AT', ['createdAt']));
    await queryRunner.createIndex('faq', new Index('IDX_FAQ_TITLE', ['title']));

    // Create foreign keys
    await queryRunner.createForeignKey('faq', new ForeignKey({
      columnNames: ['admin_id'],
      referencedTableName: 'administrator',
      referencedColumnNames: ['id'],
      onDelete: 'RESTRICT',
    }));

    await queryRunner.createForeignKey('faq', new ForeignKey({
      columnNames: ['category_id'],
      referencedTableName: 'support_category',
      referencedColumnNames: ['id'],
      onDelete: 'SET NULL',
    }));
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('faq');
  }
}
```

## 📝 Repository Implementation

```typescript
// src/database/repositories/faq.repository.ts
import { Injectable } from '@nestjs/common';
import { Repository, DataSource, SelectQueryBuilder } from 'typeorm';
import { FaqEntity, FaqStatus } from '../entities/faq.entity';

@Injectable()
export class FaqRepository extends Repository<FaqEntity> {
  constructor(private dataSource: DataSource) {
    super(FaqEntity, dataSource.createEntityManager());
  }

  // Admin queries
  async findAdminFaqList(
    page: number = 1,
    limit: number = 20,
    filters?: {
      startDate?: string;
      endDate?: string;
      categoryId?: number;
      status?: FaqStatus;
      search?: string;
    },
  ): Promise<[FaqEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('faq')
      .leftJoinAndSelect('faq.admin', 'admin')
      .leftJoinAndSelect('faq.category', 'category')
      .where('faq.deletedAt IS NULL');

    // Apply filters
    if (filters?.startDate && filters?.endDate) {
      queryBuilder.andWhere('DATE(faq.createdAt) BETWEEN :startDate AND :endDate', {
        startDate: filters.startDate,
        endDate: filters.endDate,
      });
    }

    if (filters?.categoryId) {
      queryBuilder.andWhere('faq.categoryId = :categoryId', {
        categoryId: filters.categoryId,
      });
    }

    if (filters?.status) {
      queryBuilder.andWhere('faq.status = :status', {
        status: filters.status,
      });
    }

    if (filters?.search) {
      queryBuilder.andWhere('faq.title LIKE :search', {
        search: `%${filters.search}%`,
      });
    }

    queryBuilder
      .orderBy('faq.order', 'ASC')
      .addOrderBy('faq.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    return queryBuilder.getManyAndCount();
  }

  // User queries
  async findUserFaqList(
    page: number = 1,
    limit: number = 20,
    categoryId?: number,
  ): Promise<[FaqEntity[], number]> {
    const queryBuilder = this.createQueryBuilder('faq')
      .leftJoinAndSelect('faq.category', 'category')
      .where('faq.status = :status', { status: FaqStatus.ACTIVE })
      .andWhere('faq.deletedAt IS NULL');

    if (categoryId) {
      queryBuilder.andWhere('faq.categoryId = :categoryId', { categoryId });
    }

    queryBuilder
      .orderBy('faq.order', 'ASC')
      .addOrderBy('faq.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit);

    return queryBuilder.getManyAndCount();
  }

  async findUserFaqDetail(id: number): Promise<FaqEntity | null> {
    return this.createQueryBuilder('faq')
      .leftJoinAndSelect('faq.category', 'category')
      .where('faq.id = :id', { id })
      .andWhere('faq.status = :status', { status: FaqStatus.ACTIVE })
      .andWhere('faq.deletedAt IS NULL')
      .getOne();
  }

  async incrementViewCount(id: number): Promise<void> {
    await this.createQueryBuilder()
      .update(FaqEntity)
      .set({ viewCount: () => 'view_count + 1' })
      .where('id = :id', { id })
      .execute();
  }

  async softDeleteMany(ids: number[]): Promise<void> {
    await this.createQueryBuilder()
      .softDelete()
      .where('id IN (:...ids)', { ids })
      .execute();
  }
}
```

## 🎛️ Admin Implementation

### Admin Controller

```typescript
// src/modules/admin/faq-admin/controllers/faq-admin.controller.ts
import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAdminAuthGuard } from '../../auth-admin/guards/jwt-admin-auth.guard';
import { MenuPermissionGuard } from '../../auth-admin/guards/menu-permission.guard';
import { MenuPermission } from '../../auth-admin/decorators/menu-permission.decorator';
import { FaqAdminService } from '../services/faq-admin.service';
import { CreateFaqDto } from '../dtos/create-faq.dto';
import { UpdateFaqDto } from '../dtos/update-faq.dto';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';
import { DeleteFaqDto } from '../dtos/delete-faq.dto';

@ApiTags('Admin - FAQ Management')
@ApiBearerAuth()
@Controller('admin/faq')
@UseGuards(JwtAdminAuthGuard, MenuPermissionGuard)
@MenuPermission('faq-management')
export class FaqAdminController {
  constructor(private readonly faqAdminService: FaqAdminService) {}

  @Post()
  @ApiOperation({ summary: 'Create new FAQ' })
  @ApiResponse({ status: 201, description: 'FAQ created successfully' })
  async createFaq(@Body() createFaqDto: CreateFaqDto, @Req() req) {
    return this.faqAdminService.createFaq(createFaqDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: 'Get FAQ list with filters and pagination' })
  @ApiResponse({ status: 200, description: 'FAQ list retrieved successfully' })
  async getFaqList(@Query() query: FaqListQueryDto) {
    return this.faqAdminService.getFaqList(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get FAQ detail by ID' })
  @ApiResponse({ status: 200, description: 'FAQ detail retrieved successfully' })
  async getFaqDetail(@Param('id', ParseIntPipe) id: number) {
    return this.faqAdminService.getFaqDetail(id);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update FAQ' })
  @ApiResponse({ status: 200, description: 'FAQ updated successfully' })
  async updateFaq(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateFaqDto: UpdateFaqDto,
  ) {
    return this.faqAdminService.updateFaq(id, updateFaqDto);
  }

  @Delete()
  @ApiOperation({ summary: 'Soft delete multiple FAQs' })
  @ApiResponse({ status: 200, description: 'FAQs deleted successfully' })
  async deleteFaqs(@Body() deleteFaqDto: DeleteFaqDto) {
    return this.faqAdminService.deleteFaqs(deleteFaqDto.ids);
  }
}
```

### Admin Service

```typescript
// src/modules/admin/faq-admin/services/faq-admin.service.ts
import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../../../common/message.config';
import { FaqRepository } from '../../../../database/repositories/faq.repository';
import { SupportCategoryRepository } from '../../../../database/repositories/support-category.repository';
import { CreateFaqDto } from '../dtos/create-faq.dto';
import { UpdateFaqDto } from '../dtos/update-faq.dto';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';
import * as DOMPurify from 'isomorphic-dompurify';

@Injectable()
export class FaqAdminService {
  constructor(
    private readonly faqRepository: FaqRepository,
    private readonly supportCategoryRepository: SupportCategoryRepository,
  ) {}

  async createFaq(createFaqDto: CreateFaqDto, adminId: number) {
    // Validate category if provided
    if (createFaqDto.categoryId) {
      const category = await this.supportCategoryRepository.findOne({
        where: { id: createFaqDto.categoryId, status: 'active' },
      });
      if (!category) {
        throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
      }
    }

    // Sanitize HTML content
    const sanitizedContent = DOMPurify.sanitize(createFaqDto.content);

    const faq = await this.faqRepository.save({
      adminId,
      title: createFaqDto.title,
      categoryId: createFaqDto.categoryId,
      status: createFaqDto.status || 'active',
      content: sanitizedContent,
      order: createFaqDto.order || 1,
    });

    return {
      success: true,
      faqId: faq.id,
      message: 'FAQ created successfully',
    };
  }

  async getFaqList(query: FaqListQueryDto) {
    const { page = 1, limit = 20, ...filters } = query;

    const [faqs, total] = await this.faqRepository.findAdminFaqList(page, limit, filters);

    return {
      data: faqs.map((faq) => ({
        id: faq.id,
        title: faq.title,
        category: faq.category
          ? {
              id: faq.category.id,
              name: faq.category.name,
              title: faq.category.title,
            }
          : null,
        status: faq.status,
        viewCount: faq.viewCount,
        order: faq.order,
        admin: {
          id: faq.admin.id,
          username: faq.admin.username,
        },
        createdAt: faq.createdAt,
        updatedAt: faq.updatedAt,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getFaqDetail(id: number) {
    const faq = await this.faqRepository.findOne({
      where: { id },
      relations: ['admin', 'category'],
    });

    if (!faq) {
      throw new NotFoundException(MESSAGE_CONFIG.FAQ_NOT_FOUND);
    }

    return {
      id: faq.id,
      title: faq.title,
      content: faq.content,
      category: faq.category
        ? {
            id: faq.category.id,
            name: faq.category.name,
            title: faq.category.title,
          }
        : null,
      status: faq.status,
      viewCount: faq.viewCount,
      order: faq.order,
      admin: {
        id: faq.admin.id,
        username: faq.admin.username,
      },
      createdAt: faq.createdAt,
      updatedAt: faq.updatedAt,
    };
  }

  async updateFaq(id: number, updateFaqDto: UpdateFaqDto) {
    const faq = await this.faqRepository.findOne({ where: { id } });
    if (!faq) {
      throw new NotFoundException(MESSAGE_CONFIG.FAQ_NOT_FOUND);
    }

    // Validate category if provided
    if (updateFaqDto.categoryId) {
      const category = await this.supportCategoryRepository.findOne({
        where: { id: updateFaqDto.categoryId, status: 'active' },
      });
      if (!category) {
        throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
      }
    }

    // Sanitize HTML content if provided
    let sanitizedContent = faq.content;
    if (updateFaqDto.content) {
      sanitizedContent = DOMPurify.sanitize(updateFaqDto.content);
    }

    await this.faqRepository.update(id, {
      ...updateFaqDto,
      content: sanitizedContent,
    });

    return {
      success: true,
      message: 'FAQ updated successfully',
    };
  }

  async deleteFaqs(ids: number[]) {
    if (!ids || ids.length === 0) {
      throw new BadRequestException(MESSAGE_CONFIG.NO_IDS_PROVIDED);
    }

    await this.faqRepository.softDeleteMany(ids);

    return {
      success: true,
      deletedCount: ids.length,
      message: 'FAQs deleted successfully',
    };
  }
}
```

### Admin DTOs

```typescript
// src/modules/admin/faq-admin/dtos/create-faq.dto.ts
import { IsString, IsOptional, IsEnum, IsInt, Min, MaxLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { FaqStatus } from '../../../../database/entities/faq.entity';

export class CreateFaqDto {
  @ApiProperty({ description: 'FAQ title' })
  @IsString()
  @MaxLength(255)
  title: string;

  @ApiProperty({ description: 'Support category ID', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  categoryId?: number;

  @ApiProperty({ enum: FaqStatus, default: FaqStatus.ACTIVE })
  @IsOptional()
  @IsEnum(FaqStatus)
  status?: FaqStatus;

  @ApiProperty({ description: 'FAQ content (HTML allowed)' })
  @IsString()
  content: string;

  @ApiProperty({ description: 'Display order', default: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  order?: number;
}

// src/modules/admin/faq-admin/dtos/update-faq.dto.ts
import { PartialType } from '@nestjs/swagger';
import { CreateFaqDto } from './create-faq.dto';

export class UpdateFaqDto extends PartialType(CreateFaqDto) {}

// src/modules/admin/faq-admin/dtos/faq-list-query.dto.ts
import { IsOptional, IsInt, IsEnum, IsString, IsDateString, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { FaqStatus } from '../../../../database/entities/faq.entity';

export class FaqListQueryDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 20;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsDateString()
  endDate?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  categoryId?: number;

  @ApiProperty({ enum: FaqStatus, required: false })
  @IsOptional()
  @IsEnum(FaqStatus)
  status?: FaqStatus;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  search?: string;
}

// src/modules/admin/faq-admin/dtos/delete-faq.dto.ts
import { IsArray, IsInt, ArrayMinSize } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class DeleteFaqDto {
  @ApiProperty({ description: 'Array of FAQ IDs to delete' })
  @IsArray()
  @ArrayMinSize(1)
  @IsInt({ each: true })
  ids: number[];
}
```

## 👤 User Implementation

### User Controller

```typescript
// src/modules/faq/controllers/faq.controller.ts
import {
  Controller,
  Get,
  Param,
  Query,
  UseGuards,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { OptionalJwtAuthGuard } from '../../auth/guards/optional-jwt-auth.guard';
import { FaqService } from '../services/faq.service';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';

@ApiTags('FAQ')
@Controller('faq')
@UseGuards(OptionalJwtAuthGuard)
export class FaqController {
  constructor(private readonly faqService: FaqService) {}

  @Get()
  @ApiOperation({ summary: 'Get FAQ list for users' })
  @ApiResponse({ status: 200, description: 'FAQ list retrieved successfully' })
  async getFaqList(@Query() query: FaqListQueryDto) {
    return this.faqService.getFaqList(query);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get FAQ detail and increment view count' })
  @ApiResponse({ status: 200, description: 'FAQ detail retrieved successfully' })
  async getFaqDetail(@Param('id', ParseIntPipe) id: number, @Req() req) {
    const userId = req.user?.id;
    return this.faqService.getFaqDetail(id, userId);
  }
}
```

### User Service

```typescript
// src/modules/faq/services/faq.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../../common/message.config';
import { FaqRepository } from '../../../database/repositories/faq.repository';
import { AnalyticsQueueService } from '../../analytics/services/analytics-queue.service';
import { FaqListQueryDto } from '../dtos/faq-list-query.dto';

@Injectable()
export class FaqService {
  constructor(
    private readonly faqRepository: FaqRepository,
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}

  async getFaqList(query: FaqListQueryDto) {
    const { page = 1, limit = 20, categoryId } = query;

    const [faqs, total] = await this.faqRepository.findUserFaqList(page, limit, categoryId);

    return {
      data: faqs.map((faq) => ({
        id: faq.id,
        title: faq.title,
        category: faq.category
          ? {
              id: faq.category.id,
              name: faq.category.name,
              title: faq.category.title,
            }
          : null,
        viewCount: faq.viewCount,
        createdAt: faq.createdAt,
      })),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async getFaqDetail(id: number, userId?: number) {
    const faq = await this.faqRepository.findUserFaqDetail(id);

    if (!faq) {
      throw new NotFoundException(MESSAGE_CONFIG.FAQ_NOT_FOUND);
    }

    // Increment view count
    await this.faqRepository.incrementViewCount(id);

    // Track analytics if user is logged in
    if (userId) {
      try {
        await this.analyticsQueueService.trackUserActivity({
          userId,
          activityType: 'faq_view',
          platform: 'web', // Could be determined from request headers
          timestamp: Date.now(),
          metadata: {
            faqId: id,
            categoryId: faq.categoryId,
          },
        });
      } catch (error) {
        // Log error but don't fail the request
        console.error('Failed to track FAQ analytics:', error.message);
      }
    }

    return {
      id: faq.id,
      title: faq.title,
      content: faq.content,
      category: faq.category
        ? {
            id: faq.category.id,
            name: faq.category.name,
            title: faq.category.title,
          }
        : null,
      viewCount: faq.viewCount + 1, // Return incremented count
      createdAt: faq.createdAt,
    };
  }
}
```

### User DTOs

```typescript
// src/modules/faq/dtos/faq-list-query.dto.ts
import { IsOptional, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class FaqListQueryDto {
  @ApiProperty({ required: false, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({ required: false, default: 20 })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 20;

  @ApiProperty({ required: false })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  categoryId?: number;
}
```

## 🔧 Configuration & Integration

### Update MESSAGE_CONFIG

```typescript
// Add to src/common/message.config.ts
export const MESSAGE_CONFIG = {
  // ... existing messages
  FAQ_NOT_FOUND: {
    key: 'faq_not_found',
    message: 'FAQ not found',
  },
  NO_IDS_PROVIDED: {
    key: 'no_ids_provided',
    message: 'No IDs provided',
  },
};
```

### Module Registration

```typescript
// src/modules/admin/faq-admin/faq-admin.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FaqAdminController } from './controllers/faq-admin.controller';
import { FaqAdminService } from './services/faq-admin.service';
import { FaqRepository } from '../../../database/repositories/faq.repository';
import { SupportCategoryRepository } from '../../../database/repositories/support-category.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([FaqRepository, SupportCategoryRepository]),
  ],
  controllers: [FaqAdminController],
  providers: [FaqAdminService],
  exports: [FaqAdminService],
})
export class FaqAdminModule {}

// src/modules/faq/faq.module.ts
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { FaqController } from './controllers/faq.controller';
import { FaqService } from './services/faq.service';
import { FaqRepository } from '../../database/repositories/faq.repository';
import { AnalyticsQueueModule } from '../analytics/analytics-queue.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([FaqRepository]),
    AnalyticsQueueModule,
  ],
  controllers: [FaqController],
  providers: [FaqService],
  exports: [FaqService],
})
export class FaqModule {}
```

### Update App Module

```typescript
// Add to src/app.module.ts
import { FaqAdminModule } from './modules/admin/faq-admin/faq-admin.module';
import { FaqModule } from './modules/faq/faq.module';

@Module({
  imports: [
    // ... existing modules
    FaqAdminModule,
    FaqModule,
  ],
})
export class AppModule {}
```

## 📊 Analytics Integration

The FAQ system integrates with the existing analytics system to track:

- **FAQ View Events**: Track when users view FAQ details
- **Category Analytics**: Track which FAQ categories are most popular
- **Search Analytics**: Track search queries for improving FAQ content

```typescript
// Analytics tracking example
await this.analyticsQueueService.trackUserActivity({
  userId: user.id,
  activityType: 'faq_view',
  platform: 'web',
  timestamp: Date.now(),
  metadata: {
    faqId: id,
    categoryId: faq.categoryId,
    searchQuery: query.search, // if from search
  },
});
```

## 🚀 Development Implementation Steps

### Phase 1: Database Setup
1. Create FAQ entity and migration
2. Create FAQ repository
3. Update MESSAGE_CONFIG
4. Run migration and test database structure

### Phase 2: Admin Implementation
1. Create admin FAQ module structure
2. Implement admin controller and service
3. Create admin DTOs
4. Test admin CRUD operations

### Phase 3: User Implementation
1. Create user FAQ module structure
2. Implement user controller and service
3. Create user DTOs
4. Implement view counting and analytics

### Phase 4: Integration & Testing
1. Update app module registration
2. Add menu permissions for admin
3. Test all endpoints with Swagger
4. Verify analytics integration

### Phase 5: Documentation & Deployment
1. Update API documentation
2. Create admin menu entries
3. Add seeder data for testing
4. Deploy and monitor

## 📋 Testing Checklist

### Admin Functionality
- [ ] Create FAQ with all fields
- [ ] Update FAQ content and metadata
- [ ] Soft delete single and multiple FAQs
- [ ] Filter FAQs by date range, category, status
- [ ] Search FAQs by title
- [ ] Pagination works correctly
- [ ] HTML content is sanitized

### User Functionality
- [ ] List active FAQs with pagination
- [ ] Filter FAQs by category
- [ ] View FAQ detail increments view count
- [ ] Analytics tracking works for logged-in users
- [ ] Anonymous users can view FAQs

### Security & Performance
- [ ] Admin endpoints require authentication
- [ ] Menu permissions work correctly
- [ ] HTML sanitization prevents XSS
- [ ] Database queries are optimized
- [ ] Analytics don't block main requests

## 🎯 Success Metrics

1. **Functionality**: All required APIs work as specified
2. **Performance**: FAQ listing loads in < 200ms
3. **Security**: No XSS vulnerabilities in HTML content
4. **Integration**: Analytics tracking works without errors
5. **Scalability**: System handles 1000+ FAQs efficiently

This comprehensive implementation plan provides a complete FAQ system that integrates seamlessly with the existing NestJS Digital Content Platform architecture while following all established patterns and best practices.