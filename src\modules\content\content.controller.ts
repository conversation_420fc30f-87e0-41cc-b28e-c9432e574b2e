import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { OptionalJwtAuthGuard } from '../auth/guards/optional-jwt-auth.guard';
import { ContentManagementService } from '../admin/content-management/content-management.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { DisplayManagementService } from '../admin/display-management/display-management.service';
import { DisplayType } from '../../common/common.config';
import { SettingGroup } from '../../common/setting.enum';
import { ContentSettingType } from '../../common/setting.enum';
import { MultiRecentlyWatchedContentDto } from './dtos/add-recently.dto';
import { FilterRecentlyDto } from './dtos/filter-recently.dto';
import { GetContentSettingKeyDto } from './dtos/get-content-setting-key.dto';
import { AnalyticsQueueService } from '../analytics/services/analytics-queue.service';

@ApiTags('User: Contents')
@Controller('content')
@ApiBearerAuth()
export class ContentController {
  constructor(
    private readonly contentManagementService: ContentManagementService,
    private readonly displayManagementService: DisplayManagementService,
    private readonly analyticsQueueService: AnalyticsQueueService,
  ) {}

  @Get('detail/:id')
  @ApiParam({ name: 'id', type: Number })
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({ summary: 'Get content detail' })
  async getContentDetail(@Param('id') id: number, @Req() req: any) {
    const result = await this.contentManagementService.getContentDetailForUser(
      id,
      req?.user?.userId,
    );

    // Track content view analytics

    try {
      await this.analyticsQueueService.trackContentView({
        userId: req?.user?.userId || null,
        contentId: id,
        viewTime: Date.now(),
        platform: req.headers['user-agent']?.toLowerCase().includes('mobile')
          ? 'mobile'
          : 'pc',
        metadata: {
          source: 'content_detail',
          userAgent: req.headers['user-agent'],
          referer: req.headers['referer'],
        },
      });
    } catch (error) {
      // Don't fail request if analytics fails
      console.error('Failed to track content view analytics:', error);
    }

    return result;
  }

  @Get('daily-calendar')
  @ApiOperation({ summary: 'Get daily calendar' })
  @ApiQuery({
    name: 'key',
    type: String,
    required: false,
    enum: [
      'date_mon',
      'date_tue',
      'date_wed',
      'date_thu',
      'date_fri',
      'date_sat',
      'date_sun',
    ],
  })
  @ApiQuery({ name: 'limit', type: Number, required: false })
  @ApiQuery({ name: 'page', type: Number, required: false })
  @UseGuards(OptionalJwtAuthGuard)
  async getDailyCalendar(
    @Query('key') key: string,
    @Query('limit') limit: number = 10,
    @Query('page') page: number = 1,
    @Req() req: any,
  ) {
    return await this.contentManagementService.getDailyCalendar(
      limit,
      page,
      SettingGroup.CONTENT_DATE,
      ContentSettingType.DATE,
      req?.user?.userId,
      key,
    );
  }

  @Get('episode-detail/:id')
  @ApiOperation({ summary: 'Get episode detail' })
  @UseGuards(OptionalJwtAuthGuard)
  @ApiParam({ name: 'id', type: Number })
  async getEpisodeDetail(@Param('id') id: number, @Req() req: any) {
    const result = await this.contentManagementService.getEpisodeForUser(
      id,
      req?.user?.userId,
    );

    // Track episode view analytics
    try {
      await this.analyticsQueueService.trackContentView({
        userId: req?.user?.userId || null,
        contentId: result.content?.id,
        episodeId: id,
        viewTime: Date.now(),
        platform: req.headers['user-agent']?.toLowerCase().includes('mobile')
          ? 'mobile'
          : 'pc',
        metadata: {
          source: 'episode_detail',
          userAgent: req.headers['user-agent'],
          referer: req.headers['referer'],
        },
      });
    } catch (error) {
      // Don't fail request if analytics fails
      console.error('Failed to track episode view analytics:', error);
    }

    return result;
  }

  @Get('genre-home-list')
  @ApiOperation({ summary: 'Get genre home list' })
  @UseGuards(OptionalJwtAuthGuard)
  async getGenreHomeList(@Req() req: any) {
    return await this.displayManagementService.getDisplayForUser(
      DisplayType.GENRE_DISPLAY,
      req?.user?.userId,
    );
  }

  @Get('recently-watched')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get recently watched contents' })
  async getRecentlyWatchedContents(
    @Query() filterRecentlyDto: FilterRecentlyDto,
    @Req() req: any,
  ) {
    return await this.contentManagementService.getRecentlyWatchedContents(
      req?.user?.userId,
      filterRecentlyDto,
    );
  }

  @Post('recently-watched')
  @ApiOperation({ summary: 'Add recently watched content' })
  @UseGuards(JwtAuthGuard)
  async addRecentlyWatchedContent(
    @Body() body: MultiRecentlyWatchedContentDto,
    @Req() req: any,
  ) {
    const result =
      await this.contentManagementService.addRecentlyWatchedContents(
        body,
        req?.user?.userId,
      );

    // Track content view analytics for recently watched
    if (req?.user?.userId && body.contentIds) {
      try {
        const events = body.contentIds.map((content) => ({
          userId: req.user.id,
          contentId: content.contentId,
          episodeId: content.episodeId,
          viewTime: Date.now(),
          platform: req.headers['user-agent']?.toLowerCase().includes('mobile')
            ? 'mobile'
            : 'pc',
          metadata: {
            source: 'recently_watched',
            userAgent: req.headers['user-agent'],
          },
        }));

        await this.analyticsQueueService.batchTrackContentViews(events);
      } catch (error) {
        // Don't fail request if analytics fails
        console.error('Failed to track recently watched analytics:', error);
      }
    }

    return result;
  }

  @Delete('recently-watched')
  @ApiOperation({ summary: 'Delete recently watched content' })
  @UseGuards(JwtAuthGuard)
  async deleteRecentlyWatchedContent(
    @Body() body: MultiRecentlyWatchedContentDto,
    @Req() req: any,
  ) {
    return await this.contentManagementService.deleteRecentlyWatchedContents(
      body,
      req?.user?.userId,
    );
  }

  @Get('display-content')
  @ApiOperation({ summary: 'Get main banner' })
  @ApiQuery({
    name: 'type',
    type: String,
    required: true,
    enum: DisplayType,
  })
  @UseGuards(OptionalJwtAuthGuard)
  async getMainBanner(@Query('type') type: DisplayType, @Req() req: any) {
    return await this.displayManagementService.getDisplayForUser(
      type,
      req?.user?.userId,
    );
  }

  @Get('content-classification')
  @ApiOperation({ summary: 'Get content classification' })
  async getContentClassification(
    @Query() getContentSettingKeyDto: GetContentSettingKeyDto,
    @Req() req: any,
  ) {
    return await this.contentManagementService.getContentClassification(
      getContentSettingKeyDto,
      req?.user?.userId,
    );
  }
}
