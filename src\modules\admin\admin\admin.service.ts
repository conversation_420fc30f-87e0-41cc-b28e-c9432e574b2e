import { Injectable, NotFoundException } from '@nestjs/common';
import { AdminRepository } from '../../../database/repositories/admin.repository';
import { plainToInstance } from 'class-transformer';
import { AdminEntity } from '../../../database/entities/admin.entity';
import { UpdateAdminDto } from './dtos/update-admin.dto';
import { MenuAdminStatus } from '../../../common/status.enum';
import { SearchAdminDto } from '../auth-admin/dtos/search.dto';
import { MenuAdminRepository } from '../../../database/repositories/menu-admin.repository';
import { In } from 'typeorm';
import { AdminMenuDto } from './dtos/update-admin-menu.dto';
import { MenuRepository } from '../../../database/repositories/menu.repository';
@Injectable()
export class AdminService {
  constructor(
    private adminRepository: AdminRepository,
    private menuAdminRepository: MenuAdminRepository,
    private menuRepository: MenuRepository,
  ) {}

  async getAdmins(searchDto: SearchAdminDto) {
    const { search, page = 1, limit = 10 } = searchDto;

    const query = this.adminRepository.createQueryBuilder('admin');
    if (search) {
      query.where('admin.username LIKE :search', {
        search: `%${search}%`,
      });
      query.orWhere('admin.email LIKE :search', {
        search: `%${search}%`,
      });
    }
    if (page && limit) {
      query.skip((page - 1) * limit).take(limit);
    }
    query.orderBy('admin.id', 'DESC');

    const [admins, total] = await query.getManyAndCount();
    const totalPages = Math.ceil(total / limit);
    return {
      data: plainToInstance(AdminEntity, admins),
      total,
      totalPages,
      currentPage: page,
    };
  }

  async getAdmin(id: number) {
    return await this.adminRepository.findOne({ where: { id } });
  }

  async updateAdmin(id: number, updateAdminDto: UpdateAdminDto) {
    const admin = await this.adminRepository.findOne({ where: { id } });
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }
    const updatedAdmin = {
      ...admin,
      ...updateAdminDto,
    };
    return await this.adminRepository.update(id, {
      email: updatedAdmin.email || admin.email,
      password: updatedAdmin.password || admin.password,
      role: updatedAdmin.role || admin.role,
      status: updatedAdmin.status || admin.status,
    });
  }

  async deleteAdmin(id: number) {
    const admin = await this.adminRepository.findOne({ where: { id } });
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }
    return await this.adminRepository.softDelete(id);
  }

  async getMenuByAdminId(adminId: number) {
    const admin = await this.adminRepository.findOne({
      where: { id: adminId },
    });
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }
    const menus = await this.menuAdminRepository.find({
      where: { admin: { id: adminId } },
      relations: ['menu'],
    });
    return menus;
  }

  async updateMenuByAdminId(adminId: number, menus: AdminMenuDto[]) {
    const admin = await this.adminRepository.findOne({
      where: { id: adminId },
    });
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }
    // if menu admin not found, create new menu admin
    const menusId = menus.map((menu) => ({
      id: menu.menuId,
      status: menu.status,
    }));

    const menusIdWhere = menusId.map((menu) => menu.id);

    const menusAdmin = await this.menuAdminRepository.find({
      where: { admin: { id: adminId }, menu: { id: In(menusIdWhere) } },
      relations: ['menu'],
    });

    const menusAdminId = menusAdmin.map((menu) => ({
      menuAdminId: menu.id,
      menuId: menu.menu.id,
    }));
    const menusIdToCreate = menusId.filter(
      (menu) => !menusAdminId.some((m) => m.menuId === menu.id),
    );
    const menusIdToUpdate = menusId.filter((menu) =>
      menusAdminId.some((m) => m.menuId === menu.id),
    );
    // create new menu admin
    const menusToCreate = menusIdToCreate.map((menu) => ({
      admin: admin,
      menu: { id: menu.id },
      status: menu.status as MenuAdminStatus,
    }));
    await this.menuAdminRepository.save(menusToCreate);
    // update menu admin
    const menusToUpdate = menusIdToUpdate.map((menu) => ({
      id: menusAdminId.find((m) => m.menuId === menu.id)?.menuAdminId,
      admin: admin,
      menu: { id: menu.id },
      status: menu.status as MenuAdminStatus,
    }));
    await Promise.all(
      menusToUpdate.map((menu) =>
        this.menuAdminRepository.update(Number(menu.id), {
          status: menu.status,
        }),
      ),
    );
  }
}
