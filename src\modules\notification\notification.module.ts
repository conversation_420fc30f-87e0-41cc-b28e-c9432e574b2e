import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SystemNotificationEntity } from '../../database/entities/system-notification.entity';
import { UserNotificationEntity } from '../../database/entities/user-notification.entity';
import { UserNotificationRecipientEntity } from '../../database/entities/user-notification-recipient.entity';
import { NotificationViewEntity } from '../../database/entities/notification-view.entity';
import { UserEntity } from '../../database/entities/user.entity';

import { SystemNotificationRepository } from '../../database/repositories/system-notification.repository';
import { UserNotificationRepository } from '../../database/repositories/user-notification.repository';
import { UserNotificationRecipientRepository } from '../../database/repositories/user-notification-recipient.repository';
import { NotificationViewRepository } from '../../database/repositories/notification-view.repository';
import { UserRepository } from '../../database/repositories/user.repository';

import { NotificationService } from './services/notification.service';
import { NotificationController } from './controllers/notification.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SystemNotificationEntity,
      UserNotificationEntity,
      UserNotificationRecipientEntity,
      NotificationViewEntity,
      UserEntity,
    ]),
  ],
  providers: [
    SystemNotificationRepository,
    UserNotificationRepository,
    UserNotificationRecipientRepository,
    NotificationViewRepository,
    UserRepository,
    NotificationService,
  ],
  controllers: [NotificationController],
  exports: [NotificationService],
})
export class NotificationModule {}
