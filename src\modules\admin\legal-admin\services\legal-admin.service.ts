import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { LegalDocumentRepository } from '../../../../database/repositories/legal-document.repository';
import {
  LegalDocumentEntity,
  LegalDocumentStatus,
} from '../../../../database/entities/legal-document.entity';
import { CreateLegalDocumentDto } from '../dtos/create-legal-document.dto';
import { UpdateLegalDocumentDto } from '../dtos/update-legal-document.dto';
import { LegalDocumentFilterDto } from '../dtos/legal-document-filter.dto';
import { In } from 'typeorm';
import * as DOMPurify from 'isomorphic-dompurify';

@Injectable()
export class LegalAdminService {
  constructor(private legalDocumentRepository: LegalDocumentRepository) {}

  async createLegalDocument(
    createDto: CreateLegalDocumentDto,
    adminId: number,
  ): Promise<LegalDocumentEntity> {
    // Check if name already exists
    const existingDocument = await this.legalDocumentRepository.checkNameExists(
      createDto.name,
    );

    if (existingDocument) {
      throw new ConflictException(
        `Legal document with name '${createDto.name}' already exists`,
      );
    }

    // Create new document
    const document = this.legalDocumentRepository.create({
      title: createDto.title,
      name: createDto.name,
      type: createDto.type,
      content: DOMPurify.sanitize(createDto.content),
      status: createDto.status || LegalDocumentStatus.ACTIVE,
      version: createDto.version || '1.0',
      adminId,
      viewCount: 0,
    });

    return await this.legalDocumentRepository.save(document);
  }

  async updateLegalDocument(
    id: number,
    updateDto: UpdateLegalDocumentDto,
  ): Promise<LegalDocumentEntity> {
    const document = await this.legalDocumentRepository.findOne({
      where: { id },
    });

    if (!document) {
      throw new NotFoundException('Legal document not found');
    }

    // Check name uniqueness if name is being updated
    if (updateDto.name && updateDto.name !== document.name) {
      const existingDocument =
        await this.legalDocumentRepository.checkNameExists(updateDto.name, id);

      if (existingDocument) {
        throw new ConflictException(
          `Legal document with name '${updateDto.name}' already exists`,
        );
      }
    }

    // Update fields
    if (updateDto.title !== undefined) {
      document.title = updateDto.title;
    }
    if (updateDto.name !== undefined) {
      document.name = updateDto.name;
    }
    if (updateDto.type !== undefined) {
      document.type = updateDto.type;
    }
    if (updateDto.content !== undefined) {
      document.content = DOMPurify.sanitize(updateDto.content);
    }
    if (updateDto.status !== undefined) {
      document.status = updateDto.status;
    }
    if (updateDto.version !== undefined) {
      document.version = updateDto.version;
    }

    return await this.legalDocumentRepository.save(document);
  }

  async getLegalDocumentsList(filterDto: LegalDocumentFilterDto): Promise<{
    data: LegalDocumentEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const { page = 1, limit = 20, ...filters } = filterDto;

    const [documents, total] = await this.legalDocumentRepository.findAdminList(
      page,
      limit,
      filters,
    );

    return {
      data: documents,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getLegalDocumentDetail(id: number): Promise<LegalDocumentEntity> {
    const document = await this.legalDocumentRepository.findByIdWithAdmin(id);

    if (!document) {
      throw new NotFoundException('Legal document not found');
    }

    return document;
  }

  async deleteLegalDocuments(ids: number[]): Promise<void> {
    if (!ids || ids.length === 0) {
      throw new BadRequestException('No document IDs provided');
    }

    // Check if all documents exist
    const documents = await this.legalDocumentRepository.find({
      where: { id: In(ids) },
    });

    if (documents.length !== ids.length) {
      throw new NotFoundException('Some legal documents not found');
    }

    await this.legalDocumentRepository.softDeleteMany(ids);
  }

  async updateLegalDocumentStatus(
    id: number,
    status: LegalDocumentStatus,
  ): Promise<LegalDocumentEntity> {
    const document = await this.legalDocumentRepository.findOne({
      where: { id },
    });

    if (!document) {
      throw new NotFoundException('Legal document not found');
    }

    document.status = status;
    return await this.legalDocumentRepository.save(document);
  }
}
