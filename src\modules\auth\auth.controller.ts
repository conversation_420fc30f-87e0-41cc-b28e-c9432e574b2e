import {
  Controller,
  Post,
  Body,
  Put,
  Req,
  UseGuards,
  Get,
  Query,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dtos/login.dto';
import { RegisterDto } from './dtos/register.dto';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { ForgotPasswordDto } from './dtos/forgot-password.dto';
import { ResetPasswordDto } from './dtos/reset-password.dto';
import { RefreshTokenDto } from './dtos/refresh-token.dto';
import { Request } from 'express';
import { LogoutByUuidDto } from './dtos/logout-uuid.dto';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
@ApiTags('User Auth')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Post('login')
  @ApiOperation({ summary: 'Login' })
  login(@Body() loginDto: LoginDto, @Req() req: Request) {
    return this.authService.login(loginDto, req);
  }

  @Get('check-forgot-password-token')
  @ApiOperation({ summary: 'Check Forgot Password Token' })
  checkForgotPasswordToken(@Query('token') token: string) {
    return this.authService.checkForgotPasswordToken(token);
  }

  @Post('logout')
  @ApiOperation({ summary: 'Logout' })
  logout(@Req() req: Request) {
    return this.authService.logout(req);
  }

  @Post('logout-by-uuid')
  @ApiOperation({ summary: 'Logout by UUID' })
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  logoutByUuid(@Body() logoutByUuidDto: LogoutByUuidDto, @Req() req: any) {
    const userId = req.user.id;
    return this.authService.logoutByUuid(logoutByUuidDto, userId);
  }

  @Post('refresh-token')
  @ApiOperation({ summary: 'Refresh Token' })
  refreshToken(@Body() refreshTokenDto: RefreshTokenDto) {
    return this.authService.refreshToken(refreshTokenDto);
  }

  @Post('register')
  @ApiOperation({ summary: 'Register' })
  register(@Body() registerDto: RegisterDto, @Req() req: Request) {
    return this.authService.register(registerDto, req);
  }

  @Post('forgot-password')
  @ApiOperation({ summary: 'Forgot Password' })
  forgotPassword(@Body() forgotPasswordDto: ForgotPasswordDto) {
    return this.authService.forgotPassword(forgotPasswordDto);
  }

  @Put('reset-password')
  @ApiOperation({ summary: 'Reset Password' })
  resetPassword(@Body() resetPasswordDto: ResetPasswordDto) {
    return this.authService.resetPassword(resetPasswordDto);
  }

  @Post('resend-forgot-password')
  @ApiOperation({ summary: 'Resend Forgot Password' })
  resendForgotPassword(@Body() resendForgotPasswordDto: ForgotPasswordDto) {
    return this.authService.resendForgotPassword(resendForgotPasswordDto);
  }
}
