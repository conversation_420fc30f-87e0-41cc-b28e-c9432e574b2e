import {
  <PERSON><PERSON>rray,
  Is<PERSON>num,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  Min,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { AiWebtoonSyntheticDataType } from './add-images.dto';

class ImagePromptDto {
  @IsString()
  @IsNotEmpty()
  uuid: string;

  @IsString()
  @IsNotEmpty()
  image: string;

  @IsString()
  prompt?: string;
}

export class AiWebtoonSyntheticGenerateImagesDto {
  @ApiProperty({ example: [{ uuid: '', image: '', prompt: '' }] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ImagePromptDto)
  data: ImagePromptDto[];

  @ApiProperty({ enum: AiWebtoonSyntheticDataType })
  @IsEnum(AiWebtoonSyntheticDataType)
  type: AiWebtoonSyntheticDataType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  @Min(1)
  characterId: number;
}
