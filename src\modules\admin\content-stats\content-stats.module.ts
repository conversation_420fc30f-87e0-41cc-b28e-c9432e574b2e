import { Modu<PERSON> } from '@nestjs/common';
import { ContentStatsController } from './content-stats.controller';
import { ContentStatsService } from './content-stats.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserContentEntity } from '../../../database/entities/user_content.entity';
import { EpisodeEntity } from '../../../database/entities/episode.entity';
import { ContentEntity } from '../../../database/entities/content.entity';
import { ContentSettingEntity } from '../../../database/entities/content_setting.entity';
import { SettingEntity } from '../../../database/entities/setting.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserContentEntity,
      EpisodeEntity,
      ContentEntity,
      ContentSettingEntity,
      SettingEntity,
    ]),
  ],
  controllers: [ContentStatsController],
  providers: [ContentStatsService],
})
export class ContentStatsModule {}
