# mô tả chức năng FAQ
## Admin: 
### tạo mới sẽ lưu những trường sau:
- admin tạo
- title
- category: category sẽ lấy category từ /home/<USER>/projects/nestjs-base/src/database/entities/support-category.entity.ts
- status: active (deafult), inactive
- content: có chứa mã html
- view: đếm view faq
### các api cần làm:
- api tạo mới faq
- api update faq
- api delete (xóa mềm)
- api lấy danh sách faq có filter, có phân trang: 
  + filter theo khoảng thời gian tạo (where =)
  + filter theo category (where =)
  + search theo title

## User:
### Các api cần làm
- api lấy danh sách faq (status: active) có filter theo category.
- api lấy chi tiết một faq (đếm view)