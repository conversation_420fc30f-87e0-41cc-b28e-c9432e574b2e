import { <PERSON>, Get, Param } from '@nestjs/common';
import { SettingService } from '../admin/setting/setting.service';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { SettingGroup } from '../../common/setting.enum';
@ApiTags('UserSetting')
@Controller('setting')
export class SettingController {
  constructor(private readonly settingService: SettingService) {}

  @Get(':group')
  @ApiOperation({ summary: 'Get all settings' })
  async getSetting(@Param('group') group: SettingGroup) {
    return await this.settingService.getSettingByGroup(group);
  }
}
