import { IsNotEmpty, IsString, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import configuration from '../../../config/configuration.global';
import { MESSAGE_CONFIG } from '../../../common/message.config';
export class ResetPasswordDto {
  @ApiProperty({ description: 'The token to reset the password' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: 'The new password' })
  @IsNotEmpty()
  @Matches(configuration().auth.regex.password, {
    message: MESSAGE_CONFIG.PASSWORD_REGEX.message,
  })
  password: string;
}
