import { ApiProperty } from '@nestjs/swagger';
import {
  Is<PERSON>rray,
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  IsUUID,
  Min,
} from 'class-validator';
import { EAiBaseModelInpaintType } from 'src/common/ai-webtoon.enum';

export class InpaintImageDto {
  @ApiProperty({ required: false, default: 1 })
  @IsInt()
  @IsOptional()
  @Min(1)
  numberOfImages?: number = 1;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty({ required: false })
  @IsString()
  @IsOptional()
  negativePrompt: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  originImage: string;

  @ApiProperty()
  @IsNotEmpty()
  @IsUrl()
  maskImage: string;

  @ApiProperty({
    example: ['123e4567-e89b-12d3-a456-************'],
    description: 'Character UUIDs',
  })
  @IsNotEmpty()
  @IsArray()
  @IsUUID('all', { each: true })
  characterUuids: string[];

  @ApiProperty()
  @IsEnum(EAiBaseModelInpaintType)
  baseModel: EAiBaseModelInpaintType;
}
