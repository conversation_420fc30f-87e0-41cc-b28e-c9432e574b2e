import {
  Injectable,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { MESSAGE_CONFIG } from '../../../../common/message.config';
import { SupportCategoryRepository } from '../../../../database/repositories/support-category.repository';
import { SupportTicketRepository } from '../../../../database/repositories/support-ticket.repository';
import { CategoryStatus } from '../../../../database/entities/support-category.entity';

@Injectable()
export class SupportCategoryAdminService {
  constructor(
    private categoryRepository: SupportCategoryRepository,
    private ticketRepository: SupportTicketRepository,
  ) {}

  async getCategories(page: number = 1, limit: number = 10) {
    const [categories, total] = await this.categoryRepository.findWithStats(
      page,
      limit,
    );

    return {
      data: categories,
      pagination: {
        page,
        limit,
        total: Number(total),
        totalPages: Math.ceil(Number(total) / limit),
      },
    };
  }

  async createCategory(createDto: {
    name: string;
    title: string;
    description?: string;
    expectedResponseTime?: number;
    expectedResolutionTime?: number;
    order?: number;
  }) {
    // Check if category name already exists
    const existingCategory = await this.categoryRepository.findCategoryByName(
      createDto.name,
    );
    if (existingCategory) {
      throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_EXISTS);
    }

    // Get next order value if not provided
    let order = createDto.order;
    if (!order) {
      const maxOrder = await this.categoryRepository
        .createQueryBuilder('category')
        .select('MAX(category.order)', 'maxOrder')
        .getRawOne();
      order = (maxOrder?.maxOrder || 0) + 1;
    }

    const category = await this.categoryRepository.save({
      name: createDto.name,
      title: createDto.title,
      description: createDto.description || '',
      status: CategoryStatus.ACTIVE,
      expectedResponseTime: createDto.expectedResponseTime || 24, // 24 hours default
      expectedResolutionTime: createDto.expectedResolutionTime || 72, // 72 hours default
      order,
    });

    return {
      success: true,
      category: {
        id: category.id,
        name: category.name,
        title: category.title,
        description: category.description,
        status: category.status,
        order: category.order,
        expectedResponseTime: category.expectedResponseTime,
        expectedResolutionTime: category.expectedResolutionTime,
      },
    };
  }

  async updateCategory(
    categoryId: number,
    updateDto: {
      name?: string;
      title?: string;
      description?: string;
      expectedResponseTime?: number;
      expectedResolutionTime?: number;
      order?: number;
      status?: CategoryStatus;
    },
  ) {
    const category = await this.categoryRepository.findOne({
      where: { id: categoryId },
    });

    if (!category) {
      throw new NotFoundException(MESSAGE_CONFIG.CATEGORY_NOT_FOUND);
    }

    // Check for name conflicts if name is being changed
    if (updateDto.name && updateDto.name !== category.name) {
      const existingCategory = await this.categoryRepository.findCategoryByName(
        updateDto.name,
      );
      if (existingCategory) {
        throw new BadRequestException(MESSAGE_CONFIG.CATEGORY_EXISTS);
      }
    }

    // Update fields
    if (updateDto.name !== undefined) category.name = updateDto.name;
    if (updateDto.title !== undefined) category.title = updateDto.title;
    if (updateDto.description !== undefined)
      category.description = updateDto.description;
    if (updateDto.expectedResponseTime !== undefined) {
      category.expectedResponseTime = updateDto.expectedResponseTime;
    }
    if (updateDto.expectedResolutionTime !== undefined) {
      category.expectedResolutionTime = updateDto.expectedResolutionTime;
    }
    if (updateDto.order !== undefined) category.order = updateDto.order;
    if (updateDto.status !== undefined) category.status = updateDto.status;

    const updatedCategory = await this.categoryRepository.save(category);

    return {
      success: true,
      category: {
        id: updatedCategory.id,
        name: updatedCategory.name,
        title: updatedCategory.title,
        description: updatedCategory.description,
        status: updatedCategory.status,
        order: updatedCategory.order,
        expectedResponseTime: updatedCategory.expectedResponseTime,
        expectedResolutionTime: updatedCategory.expectedResolutionTime,
      },
    };
  }

  async deleteCategories(categoryIds: number[]) {
    if (!categoryIds || categoryIds.length === 0) {
      throw new BadRequestException(MESSAGE_CONFIG.NO_CATEGORIES);
    }

    // Check if any categories have active tickets
    const categoriesInUse: number[] = [];

    for (const categoryId of categoryIds) {
      const ticketCount = await this.ticketRepository.count({
        where: {
          categoryId,
        },
      });

      if (ticketCount > 0) {
        categoriesInUse.push(categoryId);
      }
    }

    if (categoriesInUse.length > 0) {
      throw new BadRequestException(MESSAGE_CONFIG.CATEGORIES_IN_USE);
    }

    // Perform soft delete
    await this.categoryRepository.softDeleteMany(categoryIds);

    return {
      success: true,
      deletedCount: categoryIds.length,
      deletedIds: categoryIds,
    };
  }

  async getActiveCategories() {
    const categories = await this.categoryRepository.findActiveCategories();

    return {
      data: categories.map((category) => ({
        id: category.id,
        name: category.name,
        title: category.title,
        description: category.description,
      })),
    };
  }
}
