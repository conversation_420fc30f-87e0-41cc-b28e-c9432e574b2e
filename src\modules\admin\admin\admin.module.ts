import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AdminService } from './admin.service';
import { AdminRepository } from '../../../database/repositories/admin.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AdminEntity } from '../../../database/entities/admin.entity';
import { AdminController } from './admin.controller';
import { MenuRepository } from '../../../database/repositories/menu.repository';
import { MenuEntity } from '../../../database/entities/menu.entity';
import { MenuAdminRepository } from '../../../database/repositories/menu-admin.repository';
@Module({
  imports: [TypeOrmModule.forFeature([AdminEntity, MenuEntity])],
  providers: [
    AdminService,
    AdminRepository,
    MenuRepository,
    MenuAdminRepository,
  ],
  exports: [AdminService],
  controllers: [AdminController],
})
export class AdminModule {}
