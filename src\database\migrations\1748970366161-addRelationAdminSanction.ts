import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRelationAdminSanction1748970366161
  implements MigrationInterface
{
  name = 'AddRelationAdminSanction1748970366161';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD \`adminId\` int NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE \`sanction\` ADD CONSTRAINT \`FK_9fd33f14c5f0eb084160ff5757e\` FOREIGN KEY (\`adminId\`) REFERENCES \`admins\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE \`sanction\` DROP FOREIGN KEY \`FK_9fd33f14c5f0eb084160ff5757e\``,
    );
    await queryRunner.query(`ALTER TABLE \`sanction\` DROP COLUMN \`adminId\``);
  }
}
