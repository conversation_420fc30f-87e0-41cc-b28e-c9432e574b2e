import {
  Controller,
  Get,
  Post,
  Put,
  Param,
  Body,
  Query,
  UseGuards,
  Request,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { SupportAdminService } from './services/support-admin.service';
import { JwtAdminAuthGuard } from '../auth-admin/guards/jwt-admin-auth.guard';
import { MenuPermissionGuard } from '../auth-admin/guards/menu-permission.guard';
import { TicketStatus } from '../../../database/entities/support-ticket.entity';
import {
  AdminRespondTicketDto,
  AssignTicketDto,
  CloseTicketDto,
} from './dtos/admin-response.dto';
import { UpdateResponseDto } from './dtos/update-response.dto';

@ApiTags('Admin - Support Management')
@Controller('admin/support')
@UseGuards(JwtAdminAuthGuard, MenuPermissionGuard)
@ApiBearerAuth()
export class SupportAdminController {
  constructor(private readonly supportAdminService: SupportAdminService) {}

  @Get('tickets')
  @ApiOperation({
    summary: 'Get Admin Support Tickets',
    description:
      'Get paginated list of all support tickets with advanced filters',
  })
  @ApiQuery({ name: 'page', required: false, type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, type: Number, example: 20 })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: [...Object.values(TicketStatus), 'all'],
    example: 'open',
  })
  @ApiQuery({
    name: 'priority',
    required: false,
    enum: ['low', 'medium', 'high', 'urgent', 'all'],
    example: 'high',
  })
  @ApiQuery({
    name: 'category',
    required: false,
    type: String,
    example: 'payment-billing',
  })
  @ApiQuery({
    name: 'assignedTo',
    required: false,
    description: 'Admin ID, "unassigned", or "me"',
    example: 'unassigned',
  })
  @ApiQuery({
    name: 'dateType',
    required: false,
    enum: ['created', 'updated', 'resolved'],
    example: 'created',
  })
  @ApiQuery({
    name: 'fromDate',
    required: false,
    type: String,
    example: '2025-01-01',
  })
  @ApiQuery({
    name: 'toDate',
    required: false,
    type: String,
    example: '2025-01-31',
  })
  @ApiQuery({
    name: 'searchType',
    required: false,
    enum: ['subject', 'description', 'ticket_number', 'user_email'],
    example: 'subject',
  })
  @ApiQuery({
    name: 'keyword',
    required: false,
    type: String,
    example: 'payment',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin tickets retrieved successfully',
    example: {
      data: [
        {
          id: 123,
          ticketNumber: 'SUP-2025-0001',
          subject: 'Cannot access premium content',
          status: 'open',
          priority: 'medium',
          category: {
            id: 1,
            name: 'content-access',
            title: 'Content Access Issues',
          },
          user: { id: 456, email: '<EMAIL>' },
          assignedAdmin: null,
          responseCount: 1,
          lastResponseAt: '2025-01-22T09:05:00.000Z',
          createdAt: '2025-01-22T09:00:00.000Z',
          updatedAt: '2025-01-22T09:05:00.000Z',
        },
      ],
      pagination: { page: 1, limit: 20, total: 1, totalPages: 1 },
    },
  })
  async getAdminTickets(
    @Request() req,
    @Query('page', new ParseIntPipe({ optional: true })) page: number = 1,
    @Query('limit', new ParseIntPipe({ optional: true })) limit: number = 20,
    @Query('status') status?: string,
    @Query('priority') priority?: string,
    @Query('category') category?: string,
    @Query('assignedTo') assignedTo?: string,
    @Query('dateType') dateType?: 'created' | 'updated' | 'resolved',
    @Query('fromDate') fromDate?: string,
    @Query('toDate') toDate?: string,
    @Query('searchType')
    searchType?: 'subject' | 'description' | 'ticket_number' | 'user_email',
    @Query('keyword') keyword?: string,
  ) {
    limit = Math.min(limit, 100); // Admin can see more items per page

    const filters = {};
    if (status) filters['status'] = status;
    if (priority) filters['priority'] = priority;
    if (category) filters['category'] = category;
    if (dateType) filters['dateType'] = dateType;
    if (fromDate) filters['fromDate'] = fromDate;
    if (toDate) filters['toDate'] = toDate;
    if (searchType) filters['searchType'] = searchType;
    if (keyword) filters['keyword'] = keyword;

    // Handle assignedTo filter
    if (assignedTo) {
      if (assignedTo === 'me') {
        filters['assignedTo'] = req.user.id; // Current admin's ID
      } else if (assignedTo === 'unassigned') {
        filters['assignedTo'] = 'unassigned';
      } else if (!isNaN(Number(assignedTo))) {
        filters['assignedTo'] = Number(assignedTo);
      }
    }

    return await this.supportAdminService.getAdminTickets(
      page,
      limit,
      Object.keys(filters).length > 0 ? filters : undefined,
    );
  }

  @Get('tickets/:id')
  @ApiOperation({
    summary: 'Get Admin Support Ticket Detail',
    description: 'Get detailed view of a specific support ticket for admin',
  })
  @ApiResponse({
    status: 200,
    description: 'Admin ticket detail retrieved successfully',
    example: {
      id: 123,
      ticketNumber: 'SUP-2025-0001',
      subject: 'Cannot access premium content',
      description: 'I have an active subscription but cannot read episode 15',
      attachments: ['https://example.com/screenshot1.png'],
      status: 'open',
      priority: 'medium',
      category: {
        id: 1,
        name: 'content-access',
        title: 'Content Access Issues',
      },
      user: { id: 456, email: '<EMAIL>', username: 'john_doe' },
      assignedAdmin: null,
      contextData: {
        contentId: 123,
        episodeId: 456,
        subscriptionId: 789,
        deviceInfo: 'iPhone 14 iOS 16.1',
      },
      responses: [
        {
          id: 789,
          response: 'Auto-reply content...',
          status: 'auto_reply',
          createdAt: '2025-01-22T09:05:00.000Z',
          admin: { id: 1, username: 'system' },
        },
      ],
      createdAt: '2025-01-22T09:00:00.000Z',
      updatedAt: '2025-01-22T09:05:00.000Z',
    },
  })
  @ApiResponse({ status: 404, description: 'Ticket not found' })
  async getAdminTicketDetail(@Param('id', ParseIntPipe) ticketId: number) {
    return await this.supportAdminService.getAdminTicketDetail(ticketId);
  }

  @Post('tickets/:id/respond')
  @ApiOperation({
    summary: 'Admin Respond to Ticket',
    description: 'Create an admin response to a support ticket',
  })
  @ApiResponse({
    status: 201,
    description: 'Admin response created successfully',
    example: {
      success: true,
      responseId: 789,
      ticketStatus: 'in_progress',
      emailSent: true,
    },
  })
  @ApiResponse({ status: 404, description: 'Ticket not found' })
  async respondToTicket(
    @Param('id', ParseIntPipe) ticketId: number,
    @Body() responseDto: AdminRespondTicketDto,
    @Request() req: any,
  ) {
    return await this.supportAdminService.respondToTicket(
      ticketId,
      responseDto,
      req.user.id,
    );
  }

  @Put('tickets/:id/assign')
  @ApiOperation({
    summary: 'Assign Ticket to Admin',
    description: 'Assign a support ticket to an admin user',
  })
  @ApiResponse({
    status: 200,
    description: 'Ticket assigned successfully',
    example: {
      success: true,
      assignedTo: {
        id: 2,
        username: 'support_admin',
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Ticket or admin not found' })
  async assignTicket(
    @Param('id', ParseIntPipe) ticketId: number,
    @Body() assignDto: AssignTicketDto,
  ) {
    return await this.supportAdminService.assignTicket(
      ticketId,
      assignDto.adminId,
    );
  }

  @Put('tickets/:id/close')
  @ApiOperation({
    summary: 'Close Support Ticket',
    description: 'Close a support ticket with resolution notes',
  })
  @ApiResponse({
    status: 200,
    description: 'Ticket closed successfully',
    example: {
      success: true,
      ticketId: 123,
      status: 'closed',
      closedAt: '2025-01-22T15:30:00.000Z',
    },
  })
  @ApiResponse({ status: 404, description: 'Ticket not found' })
  async closeTicket(
    @Param('id', ParseIntPipe) ticketId: number,
    @Body() closeDto: CloseTicketDto,
    @Request() req: any,
  ) {
    return await this.supportAdminService.closeTicket(
      ticketId,
      closeDto,
      req.user.id,
    );
  }

  @Put('responses/:id')
  @ApiOperation({
    summary: 'Update Admin Response',
    description: 'Update an existing admin response (draft or sent)',
  })
  @ApiResponse({
    status: 200,
    description: 'Response updated successfully',
    example: {
      success: true,
      responseId: 789,
      status: 'sent',
      updatedAt: '2025-01-22T16:00:00.000Z',
    },
  })
  @ApiResponse({
    status: 404,
    description: 'Response not found or access denied',
    example: {
      error: 'support/response-not-found',
      message: 'Response not found or access denied',
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Cannot update sent response',
    example: {
      error: 'support/response-already-sent',
      message: 'Cannot update response that has already been sent',
    },
  })
  async updateResponse(
    @Param('id', ParseIntPipe) responseId: number,
    @Body() updateDto: UpdateResponseDto,
    @Request() req: any,
  ) {
    return await this.supportAdminService.updateResponse(
      responseId,
      updateDto,
      req.user.id,
    );
  }

  @Get('analytics')
  @ApiOperation({
    summary: 'Support Analytics Dashboard',
    description: 'Get support system analytics and statistics',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: ['today', 'week', 'month', 'quarter', 'year'],
    example: 'month',
  })
  @ApiResponse({
    status: 200,
    description: 'Support analytics retrieved successfully',
    example: {
      overview: {
        totalTickets: 1250,
        openTickets: 87,
        closedTickets: 1163,
        averageResponseTime: 4.2,
        averageResolutionTime: 28.5,
        satisfactionRate: 94.2,
      },
      ticketsByStatus: {
        open: 87,
        assigned: 23,
        in_progress: 45,
        pending_user: 12,
        closed: 1163,
      },
      ticketsByPriority: {
        low: 423,
        medium: 687,
        high: 125,
        urgent: 15,
      },
      ticketsByCategory: [
        { category: 'Payment & Billing', count: 345, percentage: 27.6 },
        { category: 'Content Access', count: 287, percentage: 23.0 },
        { category: 'Technical Issues', count: 198, percentage: 15.8 },
      ],
      trends: {
        dailyTickets: [12, 15, 8, 23, 18, 14, 16],
        responseTimesByDay: [3.2, 4.1, 2.8, 5.2, 4.5, 3.9, 4.2],
      },
      adminPerformance: [
        {
          adminId: 1,
          username: 'support_admin',
          ticketsHandled: 156,
          averageResponseTime: 3.8,
          closureRate: 92.3,
        },
      ],
    },
  })
  async getSupportAnalytics(
    @Query('period')
    period: 'today' | 'week' | 'month' | 'quarter' | 'year' = 'month',
  ) {
    return await this.supportAdminService.getSupportAnalytics(period);
  }
}
