import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsString,
  Matches,
  MaxLength,
  MinLength,
  ValidateNested,
} from 'class-validator';
export class CreateLanguageDto {
  @ApiProperty({ description: 'Language name', example: 'English' })
  @IsString()
  @Matches(/^[a-zA-Z]+$/, {
    message: 'Language must contain only letters',
  })
  language: string;

  @ApiProperty({ description: 'Currency', example: 'USD' })
  @IsString()
  @Matches(/^[a-zA-Z]+$/, {
    message: 'Currency must contain only letters',
  })
  @MinLength(3)
  @MaxLength(3)
  currency: string;

  @ApiProperty({ description: 'Subscription status', example: 'ON' })
  @IsString()
  subscriptionStatus: string;

  @ApiProperty({ description: 'Coins status', example: 'ON' })
  @IsString()
  coinsStatus: string;

  @ApiProperty({ description: 'Status of the language', example: 'active' })
  @IsString()
  status: string;
}

export class CreateMultipleLanguageDto {
  @ApiProperty({
    description: 'Languages',
    example: [
      {
        language: 'English',
        currency: 'USD',
        subscriptionStatus: 'ON',
        coinsStatus: 'ON',
        status: 'active',
      },
    ],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreateLanguageDto)
  languages: CreateLanguageDto[];
}
